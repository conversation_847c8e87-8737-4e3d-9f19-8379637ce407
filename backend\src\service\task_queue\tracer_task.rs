use std::str::FromStr;
use std::sync::Arc;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use tracing::log;
use redis::{Client as RedisClient, AsyncCommands};
use crate::utils::redis_client::RedisPool;
use super::Task;

#[derive(Clone, Deserialize, Serialize)]
pub struct TracerTask {
    score_id: String,
    tenant: String,
    score_mode: bool,
    urls: Vec<String>,
    score: f64,
}
#[derive(Serialize, Debug)]
struct Payload {
    images: Vec<String>,
}
#[derive(Deserialize, Debug)]
struct ResponseData {
    ret: Vec<ImageResp>,
}
#[derive(Deserialize, Debug)]
struct ImageResp {
    ocr_results: Vec<OcrResp>
}
#[derive(Deserialize, Debug)]
struct OcrResp {
    ocr_result: String,
}
impl Task for TracerTask {
    fn get_id(&self) -> (String, String) {
        (self.score_id.clone(), self.tenant.clone())
    }

    // 获取OCR结果，修改内容，添加到score任务队列
    async fn execute(&mut self, client: Client, db: PgPool, redis_pool: RedisPool) -> Result<(), String> {
        let url = if self.score_mode {
            "http://192.168.4.9:3000/Question_Card_Score_OCR"
        } else {
            "http://192.168.4.9:3000/Question_Card_Correct_OCR"
        };
        let builder = client.post(url);
        let payload = Payload { images: self.urls.clone() };
        // log::info!("url: {}, Payload: {:?}", url, payload);
        let response = builder.json(&payload).send().await.map_err(|e| e.to_string())?;
        if !response.status().is_success() {
            return Err(response.text().await.map_err(|e| e.to_string())?);
        }
        let data: ResponseData = response.json().await.map_err(|e| e.to_string())?;
        log::info!("Got response: {:?}", data);
        let mut scores = Vec::new();
        data.ret.into_iter().for_each(|image| {
            image.ocr_results.into_iter().for_each(|ocr| {
                scores.push(ocr.ocr_result);
            })
        });
        if !scores.is_empty() {
            let score = if self.score_mode {
                let s = scores.into_iter().fold(0.0, |acc, item| {
                    acc + item.parse().unwrap_or(0.0)
                });
                s
            } else {
                let s = scores.into_iter().fold((0, 0), |acc, item| {
                    if item == "√" {
                        (acc.0 + 1, acc.1)
                    } else {
                        (acc.0, acc.1 + 1)
                    }
                });
                self.score * s.0 as f64 / (s.0 + s.1) as f64
            };
            log::info!("检测到得分为： {:?}", score);
        } else {
            // 没检测到打分
            log::info!("未检测到得分痕迹");
        }
        Ok(())
    }
}