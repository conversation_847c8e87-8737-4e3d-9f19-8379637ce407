use std::sync::Arc;

use crate::model::homework::homework::{CreateHomeworkPayload, Homework, HomeworkStatistics};
use crate::service::homework::homework_service::HomeworkService;
use crate::utils::api_response::ApiResponse;
use axum::{
    extract::{Path, State},
    routing::{get, post},
    Json, Router,
};
use uuid::Uuid;

pub fn create_router() -> Router<Arc<HomeworkService>> {
    Router::new()
        .route("/getStatistics", get(get_statistics))
        .route("/homeworks", post(create_homework))
        .route("/homeworks/{id}", get(get_homework))
}

pub async fn get_statistics(
    State(service): State<Arc<HomeworkService>>,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<HomeworkStatistics>, ApiResponse<()>> {
    service.get_statistics(tenant_name).await
}

async fn create_homework(
    State(homework_service): State<Arc<HomeworkService>>,
    Json(payload): Json<CreateHomeworkPayload>,
) -> Result<Json<Homework>, String> {
    match homework_service.create_homework(&payload).await {
        Ok(homework) => Ok(Json(homework)),
        Err(e) => Err(e.to_string()),
    }
}

async fn get_homework(
    State(homework_service): State<Arc<HomeworkService>>,
    Path(homework_id): Path<Uuid>,
) -> Result<Json<Option<Homework>>, String> {
    match homework_service.get_homework(homework_id).await {
        Ok(homework) => Ok(Json(homework)),
        Err(e) => Err(e.to_string()),
    }
}
