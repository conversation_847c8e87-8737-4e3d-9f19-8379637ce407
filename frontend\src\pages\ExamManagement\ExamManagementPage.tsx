import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Calendar,
  Clock,
  Users,
  FileText,
  CheckCircle2,
  AlertCircle,
  Play,
  Square,
  Upload,
  RefreshCw,
  Eye
} from 'lucide-react';

import {
  examApi,
  ExamResponse,
  ExamCreateRequest,
  ExamStatistics,
  ExamQueryParams,
  subjectsApi,
  gradeLevelsApi,
  GradeLevel,
} from '@/services/examApi';

const ExamManagementPage: React.FC = () => {
  // State management
  const [exams, setExams] = useState<ExamResponse[]>([]);
  const [statistics, setStatistics] = useState<ExamStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [gradeFilter, setGradeFilter] = useState('all');

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedExam, setSelectedExam] = useState<ExamResponse | null>(null);

  // Form states
  const [subjects, setSubjects] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [gradeLevels, setGradeLevels] = useState<GradeLevel[]>([]);

  // Create exam form
  const [examForm, setExamForm] = useState<ExamCreateRequest>({
    name: '',
    exam_type: '',
    grade_level: '',
    exam_nature: 'formal',
    description: '',
    start_time: '',
    end_time: '',
    expected_collection_time: '',
    scan_start_time: '',
    grading_mode: 'intelligent',
    quality_control: 'single',
    ai_confidence_threshold: 0.95,
    manual_review_ratio: 0.05,
    subjects: [],
    classes: [],
    selected_students: []
  });

  // Get tenant ID from auth context (mock for now)
  const tenantId = '00000000-0000-0000-0000-000000000001';

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load exams when filters change
  useEffect(() => {
    loadExams();
  }, [currentPage, searchTerm, statusFilter, typeFilter, gradeFilter]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [statsData, subjectsData, gradeLevelsData] = await Promise.all([
        examApi.getStatistics(tenantId),
        subjectsApi.getSubjects(),
        gradeLevelsApi.getGradeLevels(),
      ]);

      setStatistics(statsData);
      setSubjects(subjectsData);
      setGradeLevels(gradeLevelsData);
      setError(null);
    } catch (err) {
      setError('Failed to load initial data');
      console.error('Error loading initial data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadExams = async () => {
    try {
      const params: ExamQueryParams = {
        page: currentPage,
        page_size: 10,
        name: searchTerm || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
        exam_type: typeFilter === 'all' ? undefined : typeFilter,
        grade_level: gradeFilter === 'all' ? undefined : gradeFilter
      };

      const data = await examApi.getExams(tenantId, params);
      setExams(data.exams);
      setTotalPages(Math.ceil(data.total / 10));
    } catch (err) {
      setError('Failed to load exams');
      console.error('Error loading exams:', err);
    }
  };

  const handleCreateExam = async () => {
    try {
      if (!examForm.name || !examForm.exam_type || !examForm.grade_level) {
        setError('Please fill in all required fields');
        return;
      }

      await examApi.createExam(tenantId, examForm);
      setIsCreateDialogOpen(false);
      resetForm();
      loadExams();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      setError('Failed to create exam');
      console.error('Error creating exam:', err);
    }
  };

  const handleUpdateExam = async () => {
    if (!selectedExam) return;

    try {
      await examApi.updateExam(tenantId, selectedExam.id, {
        name: examForm.name,
        description: examForm.description,
        start_time: examForm.start_time,
        end_time: examForm.end_time
      });
      setIsEditDialogOpen(false);
      setSelectedExam(null);
      resetForm();
      loadExams();
    } catch (err) {
      setError('Failed to update exam');
      console.error('Error updating exam:', err);
    }
  };

  const handleDeleteExam = async () => {
    if (!selectedExam) return;

    try {
      await examApi.deleteExam(tenantId, selectedExam.id);
      setIsDeleteDialogOpen(false);
      setSelectedExam(null);
      loadExams();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      setError('Failed to delete exam');
      console.error('Error deleting exam:', err);
    }
  };

  const handlePublishExam = async (examId: string) => {
    try {
      await examApi.publishExam(tenantId, examId);
      loadExams();
    } catch (err) {
      setError('Failed to publish exam');
      console.error('Error publishing exam:', err);
    }
  };

  const handleStartExam = async (examId: string) => {
    try {
      await examApi.startExam(tenantId, examId);
      loadExams();
    } catch (err) {
      setError('Failed to start exam');
      console.error('Error starting exam:', err);
    }
  };

  const handleCompleteExam = async (examId: string) => {
    try {
      await examApi.completeExam(tenantId, examId);
      loadExams();
    } catch (err) {
      setError('Failed to complete exam');
      console.error('Error completing exam:', err);
    }
  };

  const resetForm = () => {
    setExamForm({
      name: '',
      exam_type: '',
      grade_level: '',
      exam_nature: 'formal',
      description: '',
      start_time: '',
      end_time: '',
      expected_collection_time: '',
      scan_start_time: '',
      grading_mode: 'intelligent',
      quality_control: 'single',
      ai_confidence_threshold: 0.95,
      manual_review_ratio: 0.05,
      subjects: [],
      classes: [],
      selected_students: []
    });
  };

  const openEditDialog = (exam: ExamResponse) => {
    setSelectedExam(exam);
    setExamForm({
      name: exam.name,
      exam_type: exam.exam_type,
      grade_level: exam.grade_level,
      exam_nature: exam.exam_nature,
      description: exam.description || '',
      start_time: exam.start_time,
      end_time: exam.end_time,
      expected_collection_time: exam.expected_collection_time || '',
      scan_start_time: exam.scan_start_time || '',
      grading_mode: exam.grading_mode,
      quality_control: exam.quality_control,
      ai_confidence_threshold: exam.ai_confidence_threshold || 0.95,
      manual_review_ratio: exam.manual_review_ratio || 0.05,
      subjects: exam.subjects.map(s => ({
        subject_id: s.subject_id,
        paper_template_id: s.paper_template_id,
        total_score: s.total_score,
        pass_score: s.pass_score
      })),
      classes: exam.classes.map(c => ({
        class_id: c.class_id,
        class_type: c.class_type
      })),
      selected_students: []
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (exam: ExamResponse) => {
    setSelectedExam(exam);
    setIsDeleteDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: '草稿', icon: FileText },
      published: { variant: 'default' as const, label: '已发布', icon: CheckCircle2 },
      in_progress: { variant: 'outline' as const, label: '进行中', icon: Clock },
      completed: { variant: 'destructive' as const, label: '已完成', icon: Square }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getActionButtons = (exam: ExamResponse) => {
    return (
      <div className="flex gap-1">
        <Button variant="ghost" size="sm" onClick={() => openEditDialog(exam)}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4" />
        </Button>
        {exam.status === 'draft' && (
          <Button variant="ghost" size="sm" onClick={() => handlePublishExam(exam.id)}>
            <Upload className="h-4 w-4" />
          </Button>
        )}
        {exam.status === 'published' && (
          <Button variant="ghost" size="sm" onClick={() => handleStartExam(exam.id)}>
            <Play className="h-4 w-4" />
          </Button>
        )}
        {exam.status === 'in_progress' && (
          <Button variant="ghost" size="sm" onClick={() => handleCompleteExam(exam.id)}>
            <Square className="h-4 w-4" />
          </Button>
        )}
        {exam.status === 'draft' && (
          <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(exam)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <AlertDialog>
          <AlertCircle className="h-4 w-4" />
          <AlertDialogDescription>{error}</AlertDialogDescription>
        </AlertDialog>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">考试管理</h1>
          <p className="text-muted-foreground">创建、管理和监控考试</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadExams}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建考试
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建考试</DialogTitle>
                <DialogDescription>
                  请填写考试的基本信息
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">考试名称 *</Label>
                    <Input
                      id="name"
                      value={examForm.name}
                      onChange={(e) => setExamForm({ ...examForm, name: e.target.value })}
                      placeholder="请输入考试名称"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="exam_type">考试类型 *</Label>
                    <Select value={examForm.exam_type} onValueChange={(value) => setExamForm({ ...examForm, exam_type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择考试类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="midterm">期中考试</SelectItem>
                        <SelectItem value="final">期末考试</SelectItem>
                        <SelectItem value="unit">单元测试</SelectItem>
                        <SelectItem value="quiz">随堂测验</SelectItem>
                        <SelectItem value="mock">模拟考试</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="grade_level">年级 *</Label>
                    <Select value={examForm.grade_level} onValueChange={(value) => setExamForm({ ...examForm, grade_level: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        {gradeLevels.map(grade => (
                          <SelectItem key={grade.id} value={grade.name}>{grade.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="exam_nature">考试性质</Label>
                    <Select value={examForm.exam_nature} onValueChange={(value) => setExamForm({ ...examForm, exam_nature: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择考试性质" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="formal">正式考试</SelectItem>
                        <SelectItem value="mock">模拟考试</SelectItem>
                        <SelectItem value="practice">练习考试</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">考试说明</Label>
                  <Textarea
                    id="description"
                    value={examForm.description}
                    onChange={(e) => setExamForm({ ...examForm, description: e.target.value })}
                    placeholder="请输入考试说明"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start_time">开始时间 *</Label>
                    <Input
                      id="start_time"
                      type="datetime-local"
                      value={examForm.start_time}
                      onChange={(e) => setExamForm({ ...examForm, start_time: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end_time">结束时间 *</Label>
                    <Input
                      id="end_time"
                      type="datetime-local"
                      value={examForm.end_time}
                      onChange={(e) => setExamForm({ ...examForm, end_time: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="grading_mode">评阅模式</Label>
                    <Select value={examForm.grading_mode} onValueChange={(value) => setExamForm({ ...examForm, grading_mode: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择评阅模式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="intelligent">智能评阅</SelectItem>
                        <SelectItem value="manual_then_scan">先阅后扫</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quality_control">质量控制</Label>
                    <Select value={examForm.quality_control} onValueChange={(value) => setExamForm({ ...examForm, quality_control: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择质量控制方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="single">单次评阅</SelectItem>
                        <SelectItem value="double_blind">双评机制</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {examForm.grading_mode === 'intelligent' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="ai_confidence_threshold">AI置信度阈值</Label>
                      <Input
                        id="ai_confidence_threshold"
                        type="number"
                        min="0"
                        max="1"
                        step="0.01"
                        value={examForm.ai_confidence_threshold}
                        onChange={(e) => setExamForm({ ...examForm, ai_confidence_threshold: parseFloat(e.target.value) })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="manual_review_ratio">人工复核比例</Label>
                      <Input
                        id="manual_review_ratio"
                        type="number"
                        min="0"
                        max="1"
                        step="0.01"
                        value={examForm.manual_review_ratio}
                        onChange={(e) => setExamForm({ ...examForm, manual_review_ratio: parseFloat(e.target.value) })}
                      />
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateExam}>
                  创建考试
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总考试数</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_exams || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中考试</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.in_progress_exams || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成考试</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.completed_exams || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总参与学生</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_students || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索考试..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="published">已发布</SelectItem>
            <SelectItem value="in_progress">进行中</SelectItem>
            <SelectItem value="completed">已完成</SelectItem>
          </SelectContent>
        </Select>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="midterm">期中考试</SelectItem>
            <SelectItem value="final">期末考试</SelectItem>
            <SelectItem value="unit">单元测试</SelectItem>
            <SelectItem value="quiz">随堂测验</SelectItem>
            <SelectItem value="mock">模拟考试</SelectItem>
          </SelectContent>
        </Select>
        <Select value={gradeFilter} onValueChange={setGradeFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部年级</SelectItem>
            {gradeLevels.map(grade => (
              <SelectItem key={grade.id} value={grade.name}>{grade.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Exam List */}
      <Card>
        <CardHeader>
          <CardTitle>考试列表</CardTitle>
          <CardDescription>
            当前共有 {exams.length} 场考试
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>考试名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>年级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>开始时间</TableHead>
                <TableHead>参与学生</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {exams.map((exam) => (
                <TableRow key={exam.id}>
                  <TableCell className="font-medium">{exam.name}</TableCell>
                  <TableCell>{exam.exam_type}</TableCell>
                  <TableCell>{exam.grade_level}</TableCell>
                  <TableCell>{getStatusBadge(exam.status)}</TableCell>
                  <TableCell>{new Date(exam.start_time).toLocaleDateString()}</TableCell>
                  <TableCell>{exam.student_count}人</TableCell>
                  <TableCell>{getActionButtons(exam)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(currentPage > 1 ? currentPage - 1 : 1);
                  }}
                />
              </PaginationItem>
              {[...Array(totalPages)].map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink
                    href="#"
                    isActive={currentPage === i + 1}
                    onClick={(e) => {
                      e.preventDefault();
                      setCurrentPage(i + 1);
                    }}
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(currentPage < totalPages ? currentPage + 1 : totalPages);
                  }}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>编辑考试</DialogTitle>
            <DialogDescription>
              修改考试信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_name">考试名称</Label>
              <Input
                id="edit_name"
                value={examForm.name}
                onChange={(e) => setExamForm({ ...examForm, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_description">考试说明</Label>
              <Textarea
                id="edit_description"
                value={examForm.description}
                onChange={(e) => setExamForm({ ...examForm, description: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_start_time">开始时间</Label>
                <Input
                  id="edit_start_time"
                  type="datetime-local"
                  value={examForm.start_time}
                  onChange={(e) => setExamForm({ ...examForm, start_time: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_end_time">结束时间</Label>
                <Input
                  id="edit_end_time"
                  type="datetime-local"
                  value={examForm.end_time}
                  onChange={(e) => setExamForm({ ...examForm, end_time: e.target.value })}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleUpdateExam}>
              保存
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              你确定要删除考试 {selectedExam?.name} 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteExam}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExamManagementPage;