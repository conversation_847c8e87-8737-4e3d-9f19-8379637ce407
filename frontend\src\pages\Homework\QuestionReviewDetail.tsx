import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import CustomPagination from "@/components/Pagination";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  ChevronDown,
  <PERSON><PERSON>ronUp,
  <PERSON><PERSON><PERSON>,
  RotateCcw,
  Search,
  Filter,
  CheckSquare,
  ChevronRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import PureMathRenderer from '@/components/math/PureMathRenderer';


interface Question {
  id: string;
  questionNumber: string;
  questionType: "objective" | "subjective";
  title: string;
  content: string;
  type: string;
  gradingModel: string;
  totalScore: number;
  averageScore: number;
  gradingStatus: "not_started" | "in_progress" | "completed";
  completionRate: number;
  answer: string;
  knowledgePoints: string[];
}

interface ReviewImage {
  id: string;
  studentId: string;
  studentNumber: string;
  className: string;
  imageUrl: string;
  aiScore: number;
  reviewStatus: "not_reviewed" | "review_correct" | "review_error";
  recognitionResult: string;
  aiGradingBasis: string;
}

interface QuestionReviewStats {
  gradingCount: number;
  gradingCompletionRate: number;
  totalReviewCount: number;
  reviewedCount: number;
  pendingReviewCount: number;
  reviewCompletionRate: number;
  totalScore: number;
  averageScore: number;
  gradingCriteria: string;
  gradingMethod: string;
  pendingReviewImages: number;
  notReviewedImages: number;
  reviewErrorImages: number;
  reviewCorrectImages: number;
  feedbackExceptionImages: number;
}

const mockReviewImages: ReviewImage[] = [
  {
    id: "1",
    studentId: "stu001",
    studentNumber: "2024001",
    className: "高三(1)班",
    imageUrl:
      "http://202.116.234.4:8173/static/image/1753692963655/score_area/2320511/68883b77eab0ec114ea9eff7.jpg",
    aiScore: 8.5,
    reviewStatus: "not_reviewed",
    recognitionResult: "学生答案：根据题目要求...",
    aiGradingBasis: "AI评分依据：答案完整性80%，准确性90%",
  },
  {
    id: "2",
    studentId: "stu002",
    studentNumber: "2024002",
    className: "高三(1)班",
    imageUrl:
      "http://202.116.234.4:8173/static/image/1753692963655/score_area/2320535/68883b76eab0ec114ea9efa2.jpg",
    aiScore: 6.0,
    reviewStatus: "review_correct",
    recognitionResult: "学生答案：部分正确...",
    aiGradingBasis: "AI评分依据：答案完整性60%，准确性70%",
  },
  {
    id: "3",
    studentId: "stu003",
    studentNumber: "2024003",
    className: "高三(2)班",
    imageUrl:
      "http://202.116.234.4:8173/static/image/1753692963655/score_area/2320535/68883b76eab0ec114ea9efa2.jpg",
    aiScore: 9.2,
    reviewStatus: "review_error",
    recognitionResult: "学生答案：答案详细...",
    aiGradingBasis: "AI评分依据：答案完整性95%，准确性90%",
  },
];

const mockquestion: Question = {
  id: "3",
  questionNumber: "3",
  questionType: "subjective",
  title: "第3题",
  content: "阅读理解题：请根据文章内容回答问题",
  type: "阅读理解",
  gradingModel: "语义理解",
  totalScore: 10,
  averageScore: 7.5,
  gradingStatus: "not_started",
  completionRate: 0,
  answer: `（1）原式 \\( =0.5-0.9+ \\) \\( 1=0.6 \\)`,
  knowledgePoints: ["阅读理解", "文本分析"],
};

const QuestionReviewDetail: React.FC = () => {
  const navigate = useNavigate();
  // 题目信息
  const [question, setQuestion] = useState<Question>(mockquestion);
  // 筛选和配置状态
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [filters, setFilters] = useState({
    className: "",
    studentNumber: "",
    scoreRange: { min: "", max: "" },
    status: "",
  });
  const [imagesPerRow, setImagesPerRow] = useState(3);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  // 编辑分数状态
  const [editingScore, setEditingScore] = useState<{
    imageId: string;
    score: number;
  } | null>(null);

  // 重新批阅弹窗状态
  const [reGradingDialogOpen, setReGradingDialogOpen] = useState(false);
  const [reGradingOptions, setReGradingOptions] = useState({
    resetOCR: false,
    reGradeCompleted: false,
    reGradeReviewed: false,
  });
  const [selectAll, setSelectAll] = useState(false);

  const mockStats: QuestionReviewStats = {
    gradingCount: 150,
    gradingCompletionRate: 95,
    totalReviewCount: 45,
    reviewedCount: 32,
    pendingReviewCount: 13,
    reviewCompletionRate: 71,
    totalScore: question.totalScore,
    averageScore: question.averageScore,
    gradingCriteria: question.answer,
    gradingMethod: question.gradingModel,
    pendingReviewImages: 13,
    notReviewedImages: 8,
    reviewErrorImages: 3,
    reviewCorrectImages: 29,
    feedbackExceptionImages: 2,
  };

  // 处理筛选
  const handleFilter = () => {
    console.log("筛选条件:", filters);
    setCurrentPage(1);
  };

  // 重置筛选
  const resetFilters = () => {
    setFilters({
      className: "",
      studentNumber: "",
      scoreRange: { min: "", max: "" },
      status: "",
    });
    setCurrentPage(1);
  };

  // 编辑分数
  const handleEditScore = (imageId: string, currentScore: number) => {
    setEditingScore({ imageId, score: currentScore });
  };

  // 保存分数
  const saveScore = () => {
    if (editingScore) {
      console.log("保存分数:", editingScore);
      setEditingScore(null);
    }
  };

  // 核查操作
  const handleReview = (imageId: string, isCorrect: boolean) => {
    console.log("核查操作:", imageId, isCorrect ? "正确" : "错误");
  };

  // 反馈识别结果
  const handleFeedback = (imageId: string, isCorrect: boolean) => {
    console.log("反馈识别结果:", imageId, isCorrect ? "正确" : "错误");
  };

  // 重阅操作
  const handleRegrade = (imageId: string) => {
    console.log("重阅:", imageId);
  };

  // 批量确认
  const handleBatchConfirm = async () => {
    try {
      console.log("批量确认核查结果");
      // 调用API
      // const response = await reviewApi.batchConfirmReview({
      //   questionId: question.id,
      //   imageIds: mockReviewImages.map(img => img.id)
      // });
      alert("批量确认成功");
    } catch (error) {
      console.error("批量确认失败:", error);
      alert("批量确认失败");
    }
  };

  // 处理全选/反选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setReGradingOptions({
      resetOCR: checked,
      reGradeCompleted: checked,
      reGradeReviewed: checked,
    });
  };

  // 处理单个选项变化
  const handleOptionChange = (
    option: keyof typeof reGradingOptions,
    checked: boolean
  ) => {
    const newOptions = { ...reGradingOptions, [option]: checked };
    setReGradingOptions(newOptions);

    // 检查是否全选
    const allSelected = Object.values(newOptions).every((value) => value);
    const noneSelected = Object.values(newOptions).every((value) => !value);
    setSelectAll(allSelected ? true : noneSelected ? false : false);
  };

  // 重新批阅确认
  const handleReGradingConfirm = async () => {
    try {
      console.log("重新批阅:", reGradingOptions);
      // 调用API
      // const response = await gradingApi.reGrading({
      //   questionId: question.id,
      //   options: reGradingOptions
      // });
      setReGradingDialogOpen(false);
      alert("重新批阅任务已提交");
    } catch (error) {
      console.error("重新批阅失败:", error);
      alert("重新批阅失败");
    }
  };

  // 取消重新批阅
  const handleReGradingCancel = () => {
    setReGradingDialogOpen(false);
    setReGradingOptions({
      resetOCR: false,
      reGradeCompleted: false,
      reGradeReviewed: false,
    });
    setSelectAll(false);
  };

  // 此页重阅
  const handlePageRegrade = () => {
    console.log("重阅当前页面的图片");
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
            onClick={() => navigate("/homework-management")}
          >
            作业管理
          </Button>
          <ChevronRight className="h-4 w-4" />
          <Button
            onClick={() => navigate(-1)}
            variant="ghost"
            size="sm"
            className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
          >
            期中作业1
          </Button>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground font-medium">
            第{question.questionNumber}题核查
          </span>
        </nav>
        {/* 数据统计 */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">已阅卷</div>
              <div className="text-2xl font-bold">{mockStats.gradingCount}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">阅卷完成率</div>
              <div className="text-2xl font-bold">
                {mockStats.gradingCompletionRate}%
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">待核查</div>
              <div className="text-2xl font-bold">
                {mockStats.pendingReviewCount}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">核查完成率</div>
              <div className="text-2xl font-bold">
                {mockStats.reviewCompletionRate}%
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 题目信息 */}
        <Card>
          <CardHeader>
            <CardTitle>题目信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">总分:</span>
                <span className="ml-2 font-medium">{mockStats.totalScore}</span>
              </div>
              <div>
                <span className="text-muted-foreground">平均分:</span>
                <span className="ml-2 font-medium">
                  {mockStats.averageScore}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">评分方式:</span>
                <span className="ml-2 font-medium">
                  {mockStats.gradingMethod}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">待核查数:</span>
                <span className="ml-2 font-medium">
                  {mockStats.pendingReviewImages}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">未核查数:</span>
                <span className="ml-2 font-medium">
                  {mockStats.notReviewedImages}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">核查错误数:</span>
                <span className="ml-2 font-medium text-red-600">
                  {mockStats.reviewErrorImages}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">核查正确数:</span>
                <span className="ml-2 font-medium text-green-600">
                  {mockStats.reviewCorrectImages}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">反馈异常数:</span>
                <span className="ml-2 font-medium text-orange-600">
                  {mockStats.feedbackExceptionImages}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 评分标准 - 粘性定位 */}
        <div className="sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <Card className="border-b">
            <CardContent className="py-3">
              <div>
                <div className="text-sm font-medium text-muted-foreground mb-2">
                  评分标准:
                </div>
                {/* <div className="text-sm bg-muted p-3 rounded">
                  {mockStats.gradingCriteria}
                </div> */}
                <PureMathRenderer
                  content={mockStats.gradingCriteria}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 图片列表配置和批量操作 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button onClick={handleBatchConfirm}>
                <CheckSquare className="h-4 w-4 mr-2" />
                批量确认
              </Button>
              <Dialog
                open={reGradingDialogOpen}
                onOpenChange={setReGradingDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <RotateCcw className="h-4 w-4 mr-2" />
                    重新批阅
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>重新批阅</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      此操作将重置批阅结果和核查结果
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="selectAll"
                          checked={selectAll}
                          onCheckedChange={handleSelectAll}
                        />
                        <label
                          htmlFor="selectAll"
                          className="text-sm font-medium"
                        >
                          全选/反选
                        </label>
                      </div>

                      <div className="ml-6 space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="resetOCR"
                            checked={reGradingOptions.resetOCR}
                            onCheckedChange={(checked) =>
                              handleOptionChange("resetOCR", checked as boolean)
                            }
                          />
                          <label htmlFor="resetOCR" className="text-sm">
                            重置OCR
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="reGradeCompleted"
                            checked={reGradingOptions.reGradeCompleted}
                            onCheckedChange={(checked) =>
                              handleOptionChange(
                                "reGradeCompleted",
                                checked as boolean
                              )
                            }
                          />
                          <label htmlFor="reGradeCompleted" className="text-sm">
                            重阅已阅试题
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="reGradeReviewed"
                            checked={reGradingOptions.reGradeReviewed}
                            onCheckedChange={(checked) =>
                              handleOptionChange(
                                "reGradeReviewed",
                                checked as boolean
                              )
                            }
                          />
                          <label htmlFor="reGradeReviewed" className="text-sm">
                            重阅已核查试题
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button
                        onClick={handleReGradingConfirm}
                        className="flex-1"
                      >
                        确认
                      </Button>
                      <Button
                        onClick={handleReGradingCancel}
                        variant="outline"
                        className="flex-1"
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            <div className="flex items-center gap-4">
              {/* 每行显示配置 */}
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span className="text-sm">每行显示:</span>
                <Select
                  value={imagesPerRow.toString()}
                  onValueChange={(value) => setImagesPerRow(Number(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1列</SelectItem>
                    <SelectItem value="2">2列</SelectItem>
                    <SelectItem value="3">3列</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 筛选条件 */}
              <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    筛选条件
                    {filtersOpen ? (
                      <ChevronUp className="h-4 w-4 ml-2" />
                    ) : (
                      <ChevronDown className="h-4 w-4 ml-2" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              </Collapsible>
            </div>
          </div>

          {/* 筛选条件展开内容 */}
          <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
            <CollapsibleContent>
              <Card className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium">班级</label>
                    <Select
                      value={filters.className}
                      onValueChange={(value) =>
                        setFilters({ ...filters, className: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择班级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部班级</SelectItem>
                        <SelectItem value="高三(1)班">高三(1)班</SelectItem>
                        <SelectItem value="高三(2)班">高三(2)班</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">学号</label>
                    <Input
                      placeholder="输入学号"
                      value={filters.studentNumber}
                      onChange={(e) =>
                        setFilters({
                          ...filters,
                          studentNumber: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">分数范围</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="最低分"
                        value={filters.scoreRange.min}
                        onChange={(e) =>
                          setFilters({
                            ...filters,
                            scoreRange: {
                              ...filters.scoreRange,
                              min: e.target.value,
                            },
                          })
                        }
                      />
                      <span>-</span>
                      <Input
                        placeholder="最高分"
                        value={filters.scoreRange.max}
                        onChange={(e) =>
                          setFilters({
                            ...filters,
                            scoreRange: {
                              ...filters.scoreRange,
                              max: e.target.value,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">状态</label>
                    <Select
                      value={filters.status}
                      onValueChange={(value) =>
                        setFilters({ ...filters, status: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="pending">待批改</SelectItem>
                        <SelectItem value="not_reviewed">未核查</SelectItem>
                        <SelectItem value="review_correct">批阅正确</SelectItem>
                        <SelectItem value="review_error">批阅错误</SelectItem>
                        <SelectItem value="feedback_exception">
                          反馈异常
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex gap-2 mt-4">
                  <Button onClick={handleFilter} size="sm">
                    <Search className="h-4 w-4 mr-2" />
                    筛选
                  </Button>
                  <Button variant="outline" onClick={resetFilters} size="sm">
                    重置
                  </Button>
                </div>
              </Card>
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* 图片列表 */}
        <div
          className="grid gap-4"
          style={{ gridTemplateColumns: `repeat(${imagesPerRow}, 1fr)` }}
        >
          {mockReviewImages.map((image) => (
            <Card key={image.id} className="relative">
              <div className="relative">
                <img
                  src={image.imageUrl}
                  alt={`学生${image.studentNumber}答题图片`}
                  className="w-full h-auto"
                />

                {/* 左上角AI分数 - 修复z-index问题 */}
                <div className="absolute top-2 left-2 z-50">
                  {editingScore?.imageId === image.id ? (
                    <div className="bg-white/95 backdrop-blur-sm rounded p-2 shadow-xl border z-50">
                      <Input
                        type="number"
                        value={editingScore.score}
                        onChange={(e) =>
                          setEditingScore({
                            ...editingScore,
                            score: Number(e.target.value),
                          })
                        }
                        className="w-20 h-8 text-center"
                      />
                      <div className="flex gap-1 mt-2">
                        <Button size="sm" onClick={saveScore}>
                          保存
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingScore(null)}
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="text-red-600 font-bold text-lg cursor-pointer hover:text-red-700 drop-shadow-lg"
                      onClick={() => handleEditScore(image.id, image.aiScore)}
                      style={{
                        textShadow: "1px 1px 2px rgba(255,255,255,0.8)",
                      }}
                    >
                      {image.aiScore}分
                    </div>
                  )}
                </div>

                {/* 右上角查看详情 - 去掉背景色 */}
                <div className="absolute top-2 right-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0 text-gray-600 hover:text-gray-800"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80">
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm font-medium">学号:</span>
                          <span className="ml-2">{image.studentNumber}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium">核查状态:</span>
                          <Badge
                            className="ml-2"
                            variant={
                              image.reviewStatus === "review_correct"
                                ? "default"
                                : image.reviewStatus === "review_error"
                                ? "destructive"
                                : "secondary"
                            }
                          >
                            {image.reviewStatus === "not_reviewed"
                              ? "未核查"
                              : image.reviewStatus === "review_correct"
                              ? "核查正确"
                              : "核查错误"}
                          </Badge>
                        </div>
                        <div>
                          <span className="text-sm font-medium">识别结果:</span>
                          <div className="text-sm mt-1 p-2 bg-muted rounded">
                            {image.recognitionResult}
                          </div>
                          <div className="flex gap-2 mt-2">
                            <Button
                              size="sm"
                              onClick={() => handleFeedback(image.id, true)}
                            >
                              <Check className="h-3 w-3 mr-1" />
                              正确
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleFeedback(image.id, false)}
                            >
                              <X className="h-3 w-3 mr-1" />
                              错误
                            </Button>
                          </div>
                        </div>
                        <div>
                          <span className="text-sm font-medium">
                            AI评分依据:
                          </span>
                          <div className="text-sm mt-1 p-2 bg-muted rounded">
                            {image.aiGradingBasis}
                          </div>
                        </div>
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={() => handleRegrade(image.id)}
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          重阅
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* 底部操作按钮 - 优化位置避免遮挡 */}
              <CardContent className="p-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    {image.studentNumber}
                  </span>
                  <div className="flex gap-1">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          onClick={() => handleReview(image.id, true)}
                          className="h-6 w-6 p-0"
                        >
                          <Check className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>核查正确</p>
                      </TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleReview(image.id, false)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>核查错误</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 分页和批量操作 */}
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Button onClick={handleBatchConfirm}>
              <CheckSquare className="h-4 w-4 mr-2" />
              批量确认
            </Button>
            <Button variant="outline" onClick={handlePageRegrade}>
              <RotateCcw className="h-4 w-4 mr-2" />
              此页重阅
            </Button>
          </div>
          <CustomPagination
            total={mockReviewImages.length}
            current={currentPage}
            pageSize={pageSize}
            onChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size);
            }}
            showSizeChanger={true}
            showTotal={true}
          />
        </div>
      </div>
    </TooltipProvider>
  );
};

export default QuestionReviewDetail;
