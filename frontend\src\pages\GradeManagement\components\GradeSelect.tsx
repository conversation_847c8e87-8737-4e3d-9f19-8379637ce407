import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { gradeApi } from '@/services/gradeApi';
import { GradeLevelSummary, GradeLevelSelectProps } from '@/types/grade';

const GradeSelect: React.FC<GradeLevelSelectProps> = ({
  value,
  onChange,
  placeholder = "请选择年级",
  disabled = false,
  showInactive = false,
  className
}) => {
  const [grades, setGrades] = useState<GradeLevelSummary[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadGrades();
  }, [showInactive]);

  const loadGrades = async () => {
    try {
      setLoading(true);
      const response = await gradeApi.getGradeSummaries(showInactive ? undefined : true);
      if (response.success && response.data) {
        setGrades(response.data);
      }
    } catch (error) {
      console.error('Failed to load grades:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Select value={value} onValueChange={onChange} disabled={disabled || loading}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={loading ? "加载中..." : placeholder} />
      </SelectTrigger>
      <SelectContent>
        {grades.map((grade) => (
          <SelectItem 
            key={grade.id} 
            value={grade.id}
            disabled={!grade.is_active && !showInactive}
          >
            <div className="flex items-center justify-between w-full">
              <span>{grade.name}</span>
              <span className="text-xs text-gray-500 ml-2">({grade.code})</span>
              {!grade.is_active && (
                <span className="text-xs text-red-500 ml-1">[禁用]</span>
              )}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default GradeSelect;