use bytes::Bytes;
use std::env;
use tokio;
use uuid::Uuid;

use crate::config::MinioConfig;
use crate::service::storage::{
    minio_storage::MinioStorageService,
    storage_service::{StorageService, UploadOptions, FileInfo}
};

/// 测试用的MinIO存储服务实例
/// 只有在设置了TEST_MINIO_ENABLED环境变量时才会真正连接MinIO
async fn setup_test_storage_service() -> Option<MinioStorageService> {
    // 检查是否启用了MinIO集成测试
    if env::var("TEST_MINIO_ENABLED").is_err() {
        println!("Skipping MinIO integration tests - set TEST_MINIO_ENABLED=1 to enable");
        return None;
    }

    let config = MinioConfig {
        endpoint: env::var("TEST_MINIO_ENDPOINT")
            .unwrap_or_else(|_| "***********:900".to_string()),
        access_key: env::var("TEST_MINIO_ACCESS_KEY")
            .unwrap_or_else(|_| "minio_admin".to_string()),
        secret_key: env::var("TEST_MINIO_SECRET_KEY")
            .unwrap_or_else(|_| "QCTchina#*202307$".to_string()),
        use_ssl: false,
        default_bucket: "test-bucket".to_string(),
        region: None,
        max_file_size: 10 * 1024 * 1024, // 10MB for testing
        allowed_mime_types: vec![
            "text/plain".to_string(),
            "image/jpeg".to_string(),
            "image/png".to_string(),
            "application/pdf".to_string(),
        ],
    };

    match MinioStorageService::new(config).await {
        Ok(service) => Some(service),
        Err(e) => {
            println!("Failed to setup MinIO test service: {}", e);
            None
        }
    }
}

/// 生成测试文件内容
fn create_test_file_content(content: &str) -> Bytes {
    Bytes::from(content.as_bytes().to_vec())
}

/// 生成唯一的测试文件名
fn generate_test_filename(prefix: &str, extension: &str) -> String {
    format!("{}_{}.{}", prefix, Uuid::new_v4(), extension)
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_upload_basic() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return, // 跳过测试如果没有MinIO环境
    };

    let filename = generate_test_filename("test_upload", "txt");
    let content = create_test_file_content("Hello, World!");
    let options = UploadOptions::default();

    let result = storage.upload(&filename, content, options).await;
    assert!(result.is_ok());

    let file_info = result.unwrap();
    assert!(!file_info.key.is_empty());
    assert_eq!(file_info.size, 13); // "Hello, World!" 长度
    assert_eq!(file_info.content_type, "text/plain");
    assert!(!file_info.url.is_empty());
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_upload_with_preserve_filename() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("preserve", "txt");
    let content = create_test_file_content("Test content");
    let options = UploadOptions {
        preserve_filename: true,
        prefix: None,
    };

    let result = storage.upload(&filename, content, options).await;
    assert!(result.is_ok());

    let file_info = result.unwrap();
    assert_eq!(file_info.key, filename);
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_upload_with_prefix() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("prefixed", "txt");
    let content = create_test_file_content("Test content with prefix");
    let prefix = "test_prefix";
    let options = UploadOptions {
        preserve_filename: true,
        prefix: Some(prefix.to_string()),
    };

    let result = storage.upload(&filename, content, options).await;
    assert!(result.is_ok());

    let file_info = result.unwrap();
    assert!(file_info.key.starts_with(&format!("{}/", prefix)));
    assert!(file_info.key.ends_with(&filename));

}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_upload_with_multilevel_directory() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("multilevel", "txt");
    let content = create_test_file_content("Multi-level directory test content");
    
    // 测试多级目录路径
    let multilevel_prefix = "docs/2024/images/thumbnails";
    let options = UploadOptions {
        preserve_filename: true,
        prefix: Some(multilevel_prefix.to_string()),
    };

    let result = storage.upload(&filename, content, options).await;
    assert!(result.is_ok());

    let file_info = result.unwrap();
    
    // 验证文件路径包含完整的多级目录结构
    let expected_path = format!("{}/{}", multilevel_prefix, filename);
    assert_eq!(file_info.key, expected_path);
    assert!(file_info.key.contains("docs/2024/images/thumbnails/"));
    
    // 验证文件可以正常获取信息
    let info_result = storage.get_info(&file_info.key).await;
    assert!(info_result.is_ok());
    
    let retrieved_info = info_result.unwrap();
    assert_eq!(retrieved_info.key, expected_path);
    assert_eq!(retrieved_info.size, 34); // "Multi-level directory test content" 长度
    assert_eq!(retrieved_info.content_type, "text/plain");
    assert!(!retrieved_info.url.is_empty());
    
    // 验证文件存在性检查
    let exists_result = storage.exists(&file_info.key).await;
    assert!(exists_result.is_ok());
    assert!(exists_result.unwrap());
    
    // 验证可以下载文件
    let download_result = storage.download(&file_info.key).await;
    assert!(download_result.is_ok());
    
    let downloaded_content = download_result.unwrap();
    let downloaded_str = String::from_utf8(downloaded_content.to_vec()).unwrap();
    assert_eq!(downloaded_str, "Multi-level directory test content");
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_upload_with_deep_nested_directory() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("deeply_nested", "json");
    let content = create_test_file_content(r#"{"message": "Deep nested directory test"}"#);
    
    // 测试更深层的目录结构
    let deep_prefix = "projects/deep-mate/backend/src/tests/storage/temp/uploads/2024/01/15";
    let options = UploadOptions {
        preserve_filename: false, // 测试UUID文件名生成
        prefix: Some(deep_prefix.to_string()),
    };

    let result = storage.upload(&filename, content, options).await;
    assert!(result.is_ok());

    let file_info = result.unwrap();
    
    // 验证文件路径包含前缀并且文件名是UUID格式
    assert!(file_info.key.starts_with(&format!("{}/", deep_prefix)));
    assert!(file_info.key.ends_with(".json"));
    assert!(!file_info.key.ends_with(&filename)); // 因为preserve_filename=false
    
    // 提取UUID部分验证格式
    let key_parts: Vec<&str> = file_info.key.split('/').collect();
    let uuid_filename = key_parts.last().unwrap();
    assert!(uuid_filename.len() > 30); // UUID + .json 应该超过30个字符
    
    // 验证文件内容正确性
    let download_result = storage.download(&file_info.key).await;
    assert!(download_result.is_ok());
    
    let downloaded_content = download_result.unwrap();
    let downloaded_str = String::from_utf8(downloaded_content.to_vec()).unwrap();
    assert_eq!(downloaded_str, r#"{"message": "Deep nested directory test"}"#);
    
    // 验证文件信息
    let info_result = storage.get_info(&file_info.key).await;
    assert!(info_result.is_ok());
    
    let retrieved_info = info_result.unwrap();
    assert_eq!(retrieved_info.size, 41); // JSON字符串长度
    assert!(retrieved_info.content_type.contains("json") || retrieved_info.content_type == "application/octet-stream");
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_download() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("download_test", "txt");
    let original_content = "This is test content for download";
    let content = create_test_file_content(original_content);
    let options = UploadOptions::default();

    // 先上传文件
    let upload_result = storage.upload(&filename, content, options).await;
    assert!(upload_result.is_ok());
    let file_info = upload_result.unwrap();

    // 下载文件
    let download_result = storage.download(&file_info.key).await;
    assert!(download_result.is_ok());

    let downloaded_content = download_result.unwrap();
    let downloaded_str = String::from_utf8(downloaded_content.to_vec()).unwrap();
    assert_eq!(downloaded_str, original_content);
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_exists() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("exists_test", "txt");
    let content = create_test_file_content("Exists test content");
    let options = UploadOptions::default();

    // 检查不存在的文件
    let non_existent_key = "non_existent_file.txt";
    let exists_result = storage.exists(non_existent_key).await;
    assert!(exists_result.is_ok());
    assert!(!exists_result.unwrap());

    // 上传文件
    let upload_result = storage.upload(&filename, content, options).await;
    assert!(upload_result.is_ok());
    let file_info = upload_result.unwrap();

    // 检查文件是否存在
    let exists_result = storage.exists(&file_info.key).await;
    assert!(exists_result.is_ok());
    assert!(exists_result.unwrap());
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_info() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("info_test", "txt");
    let content = create_test_file_content("File info test content");
    let options = UploadOptions::default();

    // 上传文件
    let upload_result = storage.upload(&filename, content, options).await;
    assert!(upload_result.is_ok());
    let file_info = upload_result.unwrap();

    // 获取文件信息
    let info_result = storage.get_info(&file_info.key).await;
    assert!(info_result.is_ok());

    let retrieved_info = info_result.unwrap();
    assert_eq!(retrieved_info.key, file_info.key);
    assert_eq!(retrieved_info.size, file_info.size);
    assert_eq!(retrieved_info.content_type, file_info.content_type);
    assert!(!retrieved_info.url.is_empty());
}

#[tokio::test(flavor = "multi_thread")]
async fn test_file_delete() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("delete_test", "txt");
    let content = create_test_file_content("Delete test content");
    let options = UploadOptions::default();

    // 上传文件
    let upload_result = storage.upload(&filename, content, options).await;
    assert!(upload_result.is_ok());
    let file_info = upload_result.unwrap();

    // 确认文件存在
    let exists_result = storage.exists(&file_info.key).await;
    assert!(exists_result.is_ok());
    assert!(exists_result.unwrap());

    // 删除文件
    let delete_result = storage.delete(&file_info.key).await;
    assert!(delete_result.is_ok());

    // 确认文件不存在
    let exists_result = storage.exists(&file_info.key).await;
    assert!(exists_result.is_ok());
    assert!(!exists_result.unwrap());
}

#[tokio::test(flavor = "multi_thread")]
async fn test_batch_delete() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let mut uploaded_keys = Vec::new();
    let options = UploadOptions::default();

    // 上传多个文件
    for i in 0..3 {
        let filename = generate_test_filename(&format!("batch_delete_{}", i), "txt");
        let content = create_test_file_content(&format!("Batch delete content {}", i));
        
        let upload_result = storage.upload(&filename, content, options.clone()).await;
        assert!(upload_result.is_ok());
        uploaded_keys.push(upload_result.unwrap().key);
    }

    // 批量删除
    let keys_to_delete: Vec<&str> = uploaded_keys.iter().map(|s| s.as_str()).collect();
    let batch_delete_result = storage.delete_batch(keys_to_delete).await;
    assert!(batch_delete_result.is_ok());

    let deleted_keys = batch_delete_result.unwrap();
    assert_eq!(deleted_keys.len(), 3);

    // 确认所有文件都被删除
    for key in &uploaded_keys {
        let exists_result = storage.exists(key).await;
        assert!(exists_result.is_ok());
        assert!(!exists_result.unwrap());
    }
}

#[tokio::test(flavor = "multi_thread")]
async fn test_presigned_url_generation() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let filename = generate_test_filename("presigned_test", "txt");
    let content = create_test_file_content("Presigned URL test content");
    let options = UploadOptions::default();

    // 上传文件
    let upload_result = storage.upload(&filename, content, options).await;
    assert!(upload_result.is_ok());
    let file_info = upload_result.unwrap();

    // 生成预签名URL
    let presigned_result = storage.generate_presigned_url(&file_info.key).await;
    assert!(presigned_result.is_ok());

    let presigned_url = presigned_result.unwrap();
    assert!(!presigned_url.is_empty());
    assert!(presigned_url.starts_with("http"));

}

#[tokio::test(flavor = "multi_thread")]
async fn test_download_non_existent_file() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let non_existent_key = "this_file_does_not_exist.txt";
    let download_result = storage.download(non_existent_key).await;
    assert!(download_result.is_err());
}

#[tokio::test(flavor = "multi_thread")]
async fn test_get_info_non_existent_file() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let non_existent_key = "this_file_does_not_exist.txt";
    let info_result = storage.get_info(non_existent_key).await;
    assert!(info_result.is_err());
}

#[tokio::test(flavor = "multi_thread")]
async fn test_generate_presigned_url_non_existent_file() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    let non_existent_key = "this_file_does_not_exist.txt";
    let presigned_result = storage.generate_presigned_url(non_existent_key).await;
    // 注意：某些S3实现可能会为不存在的文件生成预签名URL
    // 这个测试可能需要根据具体的MinIO行为调整
    if presigned_result.is_ok() {
        println!("MinIO generates presigned URLs for non-existent files");
    }
}

#[tokio::test(flavor = "multi_thread")]
async fn test_batch_delete_with_non_existent_files() {
    let storage = match setup_test_storage_service().await {
        Some(service) => service,
        None => return,
    };

    // 准备一个存在的文件和一个不存在的文件
    let filename = generate_test_filename("batch_mixed", "txt");
    let content = create_test_file_content("Mixed batch delete test");
    let options = UploadOptions::default();

    let upload_result = storage.upload(&filename, content, options).await;
    assert!(upload_result.is_ok());
    let existing_key = upload_result.unwrap().key;

    let non_existent_key = "non_existent_file.txt";

    // 批量删除（包含存在和不存在的文件）
    let keys_to_delete = vec![existing_key.as_str(), non_existent_key];
    let batch_delete_result = storage.delete_batch(keys_to_delete).await;
    assert!(batch_delete_result.is_ok());

    let deleted_keys = batch_delete_result.unwrap();
    // MinIO/S3删除不存在的文件会返回成功（幂等操作），所以两个都会被返回
    assert_eq!(deleted_keys.len(), 2);
    assert!(deleted_keys.contains(&existing_key));
    assert!(deleted_keys.contains(&non_existent_key.to_string()));
}

// 单元测试：测试文件键生成逻辑（不需要MinIO连接）
#[cfg(test)]
mod unit_tests {
    use super::*;

    // 由于generate_file_key是私有方法，我们需要通过公共接口来测试
    // 这里我们可以测试上传操作的键生成行为

    #[test]
    fn test_upload_options_default() {
        let options = UploadOptions::default();
        assert!(!options.preserve_filename);
        assert!(options.prefix.is_none());
    }

    #[test]
    fn test_upload_options_custom() {
        let options = UploadOptions {
            preserve_filename: true,
            prefix: Some("test_prefix".to_string()),
        };
        assert!(options.preserve_filename);
        assert_eq!(options.prefix.unwrap(), "test_prefix");
    }

    #[test]
    fn test_file_info_creation() {
        let file_info = FileInfo {
            key: "test_key".to_string(),
            size: 1024,
            content_type: "text/plain".to_string(),
            url: "https://example.com/test_key".to_string(),
        };

        assert_eq!(file_info.key, "test_key");
        assert_eq!(file_info.size, 1024);
        assert_eq!(file_info.content_type, "text/plain");
        assert_eq!(file_info.url, "https://example.com/test_key");
    }
}

// 性能测试：测试大文件和批量操作
#[cfg(test)]
mod performance_tests {
    use super::*;

    #[tokio::test(flavor = "multi_thread")]
    async fn test_large_file_upload() {
        let storage = match setup_test_storage_service().await {
            Some(service) => service,
            None => return,
        };

        // 创建一个1MB的测试文件
        let large_content = vec![b'A'; 1024 * 1024];
        let filename = generate_test_filename("large_file", "bin");
        let content = Bytes::from(large_content);
        let options = UploadOptions::default();

        let start_time = std::time::Instant::now();
        let upload_result = storage.upload(&filename, content, options).await;
        let upload_duration = start_time.elapsed();

        assert!(upload_result.is_ok());
        let file_info = upload_result.unwrap();
        assert_eq!(file_info.size, 1024 * 1024);

        println!("Large file upload took: {:?}", upload_duration);

        // 测试下载性能
        let start_time = std::time::Instant::now();
        let download_result = storage.download(&file_info.key).await;
        let download_duration = start_time.elapsed();

        assert!(download_result.is_ok());
        let downloaded_content = download_result.unwrap();
        assert_eq!(downloaded_content.len(), 1024 * 1024);

        println!("Large file download took: {:?}", download_duration);
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_concurrent_uploads() {
        let storage = match setup_test_storage_service().await {
            Some(service) => service,
            None => return,
        };

        let storage = std::sync::Arc::new(storage);
        let mut handles = Vec::new();

        // 并发上传10个文件
        for i in 0..10 {
            let storage_clone = storage.clone();
            let handle = tokio::spawn(async move {
                let filename = generate_test_filename(&format!("concurrent_{}", i), "txt");
                let content = create_test_file_content(&format!("Concurrent upload {}", i));
                let options = UploadOptions::default();

                storage_clone.upload(&filename, content, options).await
            });
            handles.push(handle);
        }

        let mut uploaded_keys = Vec::new();
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok());
            uploaded_keys.push(result.unwrap().key);
        }

        assert_eq!(uploaded_keys.len(), 10);

        // 清理所有测试文件
        let keys_to_delete: Vec<&str> = uploaded_keys.iter().map(|s| s.as_str()).collect();
        let _ = storage.delete_batch(keys_to_delete).await;
    }
}