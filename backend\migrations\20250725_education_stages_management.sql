-- Education Stages Dictionary Management Migration
-- Migration: 20250725_education_stages_management
-- Description: Add education stages (学段) dictionary management system

-- =============================================
-- EDUCATION STAGES CORE TABLE
-- =============================================

-- Education stages dictionary table (学段字典表)
CREATE TABLE IF NOT EXISTS public.education_stages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(30) UNIQUE NOT NULL,           -- 学段编码 (PRIMARY, MIDDLE, HIGH, etc.)
    name VARCHAR(100) NOT NULL,                 -- 学段名称 (小学, 初中, 高中, etc.)
    short_name VARCHAR(20),                     -- 简称 (小, 初, 高)
    description TEXT,                           -- 详细描述
    order_level INTEGER NOT NULL,              -- 排序级别 (1-小学, 2-初中, 3-高中)
    duration_years INTEGER DEFAULT 3,          -- 学段持续年数
    age_range VARCHAR(20),                      -- 适用年龄范围 (如 "6-12岁")
    is_standard BOOLEAN DEFAULT TRUE,           -- 是否为标准学段
    is_active BOOLEAN DEFAULT TRUE,             -- 是否启用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add education_stage_code to grade_levels table for direct association
ALTER TABLE public.grade_levels 
ADD COLUMN IF NOT EXISTS education_stage_code VARCHAR(30);

-- Add foreign key constraint
ALTER TABLE public.grade_levels 
ADD CONSTRAINT fk_grade_levels_education_stage_code 
FOREIGN KEY (education_stage_code) REFERENCES public.education_stages(code);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Indexes for education_stages table
CREATE INDEX IF NOT EXISTS idx_education_stages_code ON public.education_stages(code);
CREATE INDEX IF NOT EXISTS idx_education_stages_active ON public.education_stages(is_active);
CREATE INDEX IF NOT EXISTS idx_education_stages_standard ON public.education_stages(is_standard);
CREATE INDEX IF NOT EXISTS idx_education_stages_order ON public.education_stages(order_level);

-- Index for grade_levels education_stage_code
CREATE INDEX IF NOT EXISTS idx_grade_levels_education_stage_code ON public.grade_levels(education_stage_code);

-- =============================================
-- INSERT DEFAULT EDUCATION STAGES DATA
-- =============================================

-- Insert standard education stages (标准学段数据)
INSERT INTO public.education_stages (code, name, short_name, description, order_level, duration_years, age_range, is_standard, is_active) 
VALUES 
    ('PRIMARY', '小学', '小', '基础教育阶段，培养学生基本知识和技能', 1, 6, '6-12岁', TRUE, TRUE),
    ('MIDDLE', '初中', '初', '义务教育高级阶段，培养学生综合能力', 2, 3, '12-15岁', TRUE, TRUE),
    ('HIGH', '高中', '高', '高等教育预备阶段，培养学生深度思维能力', 3, 3, '15-18岁', TRUE, TRUE),
    ('VOCATIONAL', '职业教育', '职', '技能型人才培养阶段，注重实践能力', 4, 3, '15-22岁', TRUE, TRUE),
    ('INTERNATIONAL_IB', 'IB课程', 'IB', '国际文凭课程，培养国际化人才', 5, 2, '16-19岁', FALSE, TRUE),
    ('INTERNATIONAL_AP', 'AP课程', 'AP', '美国大学先修课程', 6, 2, '16-18岁', FALSE, TRUE)
ON CONFLICT (code) DO NOTHING;

-- =============================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =============================================

-- Update trigger for education_stages
CREATE OR REPLACE FUNCTION update_education_stages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_education_stages_updated_at
    BEFORE UPDATE ON public.education_stages
    FOR EACH ROW
    EXECUTE FUNCTION update_education_stages_updated_at();

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON TABLE public.education_stages IS '学段字典表 - 存储教育阶段定义(小学/初中/高中等)';
COMMENT ON COLUMN public.education_stages.code IS '学段编码，全局唯一';
COMMENT ON COLUMN public.education_stages.name IS '学段名称';
COMMENT ON COLUMN public.education_stages.short_name IS '学段简称';
COMMENT ON COLUMN public.education_stages.order_level IS '排序级别，用于显示顺序';
COMMENT ON COLUMN public.education_stages.duration_years IS '学段持续年数';
COMMENT ON COLUMN public.education_stages.age_range IS '适用年龄范围';
COMMENT ON COLUMN public.education_stages.is_standard IS '是否为标准学段';
COMMENT ON COLUMN public.education_stages.is_active IS '是否启用';

COMMENT ON COLUMN public.grade_levels.education_stage_code IS '所属学段编码，关联education_stages.code';