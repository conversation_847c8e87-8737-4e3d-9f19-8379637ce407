import { GradeLevel } from "./grade";
import { Teacher } from "./teacher";

export interface Classes {
  id: String,
  name: String,
  code: String,
  grade_level_code: String,
  //行政班无任课老师只有班主任，教学班有任课老师无班主任,学科和任课老师有关，有学科才有任课老师
  subject_code?: String,
  class_type: ClassType,
  school_year: Number,
}

/**
 * 班级类型
 */
export enum ClassType {
  /**
  * 行政班
  */
  AdministrativeClass = 'AdministrativeClass',
  /**
   * 教学班
   */
  TeachingClass = 'TeachingClass',
}

/**
 * 在班级基础上有更多联查信息的实体
 */
export interface ClassesDetail {
  //数据库内部分
  classes: Classes,
  //如果是行政班则具有的额外信息
  administrative_detail?: AdministrativeDetail,
  //教学班才具有的额外信息
  teaching_detail?: TeachingDetail,
  //年级列表
  grade_level_list?: Array<GradeLevel>,
  //学生总数
  student_totals: number,
}

/**
 * 班级统计数据
 */
export interface ClassesStatistics {
  total_classes: number;
  total_teacher: number;
  total_students: number;
}

export interface AdministrativeDetail {
  //班主任列表
  head_teacher_list: Array<HeadTeacher>,
}


/**
 * 班主任信息
 */
export interface HeadTeacher {
  teacher: Teacher,
  //任课结束时间，对于临时代班的才有
  end_time?: String,
}

///教学班的额外信息
export interface TeachingDetail {
  //任课老师列表
  teacher_list: Array<TeachingTeacher>,
}

///任课老师信息
export interface TeachingTeacher {
  //教师信息
  teacher: Teacher,
  //学科
  subject_code: String,
  //任课结束时间，对于临时代班的才有
  end_time?: String,
}

/**
 * 创建班级用的参数对象
 */
export interface CreateClassesParams {
  name: String,
  code: String,
  class_type: ClassType,
  school_year: Number,
  //教学班额外参数
  grade_level_code?: Array<String>,
  subject_code?: Array<String>,
}

