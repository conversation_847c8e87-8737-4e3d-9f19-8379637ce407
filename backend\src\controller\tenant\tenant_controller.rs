use std::sync::Arc;
use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::tenant::tenant::{CreateTenantRequest, TenantResponse, UpdateTenantRequest};

use crate::service::tenant::tenant_service::TenantService;
use crate::utils::api_response::{responses};

use axum::{
    extract::{Json, Path, State},
    http::StatusCode,
    response::IntoResponse,
    routing::{delete, get, post, put},
    Router,
};
use uuid::Uuid;
use crate::utils::api_response::{ErrorResponse, SuccessResponse};

/// 创建租户
#[axum::debug_handler]
pub async fn create_tenant(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(request): Json<CreateTenantRequest>,
) -> impl IntoResponse {
    // 使用管理员认证中间件提供的用户ID
    let creator_id = admin_context.user_id;

    match tenant_service.create_tenant(request, creator_id).await {
        Ok(tenant) => {
            responses::success(tenant, Some("Tenant created successfully"))
        }
        Err(e) => {
            eprintln!("Error creating tenant: {}", e);
            responses::error(format!("Failed to create tenant. {}", e).as_str(), Some("TENANT_CREATION_ERROR"))
        }
    }
}

/// 获取租户列表
#[axum::debug_handler]
pub async fn get_tenants(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    
) -> Result<Json<SuccessResponse<Vec<TenantResponse>>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.get_tenants().await {
        Ok(tenants) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(tenants),
            message: "Tenants retrieved successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error retrieving tenants: {}", e);
            let error_response = ErrorResponse {
                success: false,
                message: "Failed to retrieve tenants".to_string(),
                error_code: Some("DATABASE_ERROR".to_string()),
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)))
        }
    }
}

/// 根据 ID 获取租户
#[axum::debug_handler]
pub async fn get_tenant_by_id(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
) -> Result<Json<SuccessResponse<TenantResponse>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.get_tenant_by_id(tenant_id).await {
        Ok(tenant) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(tenant),
            message: "Tenant retrieved successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error retrieving tenant: {}", e);
            let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
                (StatusCode::NOT_FOUND, "Tenant not found", "The requested tenant does not exist")
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error", "Failed to retrieve tenant")
            };

            let error_response = ErrorResponse {
                success: false,
                message: user_msg.to_string(),
                error_code: Some("TENANT_ERROR".to_string()),
            };
            Err((status_code, Json(error_response)))
        }
    }
}

/// 更新租户
#[axum::debug_handler]
pub async fn update_tenant(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
    Json(request): Json<UpdateTenantRequest>,
) -> Result<Json<SuccessResponse<TenantResponse>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.update_tenant(tenant_id, request).await {
        Ok(tenant) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(tenant),
            message: "Tenant updated successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error updating tenant: {}", e);
            let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
                (StatusCode::NOT_FOUND, "Tenant not found", "The requested tenant does not exist")
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error", "Failed to update tenant")
            };

            let error_response = ErrorResponse {
                success: false,
                message: user_msg.to_string(),
                error_code: Some("TENANT_ERROR".to_string()),
            };
            Err((status_code, Json(error_response)))
        }
    }
}

/// 删除租户
#[axum::debug_handler]
pub async fn delete_tenant(
    State(tenant_service): State<Arc<TenantService>>,
    AuthExtractor(_admin_context): AuthExtractor,
    Path(tenant_id): Path<Uuid>,
) -> Result<Json<SuccessResponse<()>>, (StatusCode, Json<ErrorResponse>)> {
    match tenant_service.delete_tenant(tenant_id).await {
        Ok(_) => Ok(Json(SuccessResponse {
            success: true,
            data: Some(()),
            message: "Tenant deleted successfully".to_string(),
        })),
        Err(e) => {
            eprintln!("Error deleting tenant: {}", e);
            let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
                (StatusCode::NOT_FOUND, "Tenant not found", "The requested tenant does not exist")
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error", "Failed to delete tenant")
            };

            let error_response = ErrorResponse {
                success: false,
                message: user_msg.to_string(),
                error_code: Some("TENANT_ERROR".to_string()),
            };
            Err((status_code, Json(error_response)))
        }
    }
}

/// 租户管理路由 (New service-based routes)
pub fn create_router() -> Router<Arc<TenantService>> {
    Router::new()
        .route("/", post(create_tenant))
        .route("/", get(get_tenants))
        .route("/{id}", get(get_tenant_by_id))
        .route("/{id}", put(update_tenant))
        .route("/{id}", delete(delete_tenant))
}