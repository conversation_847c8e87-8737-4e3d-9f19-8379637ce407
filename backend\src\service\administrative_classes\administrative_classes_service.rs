use std::collections::HashSet;

use sqlx::PgPool;
use uuid::Uuid;

use crate::model::administrative_classes::administrative_classes::{
    AdministrativeClasses, AdministrativeClassesStatistics, CreateAdministrativeClassesParams,
};

#[derive(Clone)]
pub struct AdministrativeClassesService {
    db_pool: PgPool,
}

//行政班级管理服务
impl AdministrativeClassesService {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /**
     * 作者：张瀚
     * 说明：获取用户能查看的行政班列表
     */
    pub async fn get_user_class_list(
        &self,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut result: Vec<AdministrativeClasses> = vec![];
        //自己担任班主任的班级列表
        let mut head_teacher_class_list = self
            .find_head_teacher_class_list(schema_name, user_id)
            .await?;
        result.append(&mut head_teacher_class_list);
        //管理员或者高级别的权限能查看的列表 TODO
        //自己担任临时班主任的班级列表 TODO
        Ok(result)
    }

    /**
     * 作者：张瀚
     * 说明：查询自己担任班主任的班级列表
     */
    pub async fn find_head_teacher_class_list(
        &self,
        schema_name: &String,
        teacher_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        builder.push(" order by created_at desc");
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询班级内的学生的ID列表
     */
    pub async fn find_student_id_list_in_classes(
        &self,
        schema_name: &String,
        classes_id: &Uuid,
    ) -> Result<Vec<Uuid>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.id from {}.students s where s.administrative_class_id = ",
            schema_name
        ));
        builder.push_bind(classes_id);
        Ok(builder
            .build_query_scalar()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询指定编码的班级
     */
    pub async fn find_by_code(
        &self,
        schema_name: &String,
        class_code: &String,
    ) -> Result<AdministrativeClasses, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.class_code =  ",
            schema_name
        ));
        builder.push_bind(class_code);
        Ok(builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：获取行政班统计数据
     */
    pub async fn get_statistics(
        &self,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<AdministrativeClassesStatistics, String> {
        //获取能查看的班级列表
        let class_list = self.get_user_class_list(schema_name, user_id).await?;
        //统计班主任ID
        let mut teacher_id_set = HashSet::<Uuid>::new();
        let mut student_id_set = HashSet::<String>::new();
        for classes in &class_list {
            teacher_id_set.insert(classes.teacher_id.clone());
            //查询班内学生ID列表
            let student_id_list = self
                .find_student_id_list_in_classes(schema_name, &classes.id)
                .await?;
            student_id_list.iter().for_each(|student_id| {
                student_id_set.insert(student_id.clone().to_string());
            });
        }
        let data = AdministrativeClassesStatistics {
            total_classes: class_list.len() as i32,
            total_teacher: teacher_id_set.len() as i32,
            total_students: student_id_set.len() as i32,
        };
        Ok(data)
    }

    /**
     * 作者：张瀚
     * 说明：创建行政班
     */
    pub async fn create_classes(
        &self,
        schema_name: &String,
        user_id: &Uuid,
        params: &CreateAdministrativeClassesParams,
    ) -> Result<AdministrativeClasses, String> {
        //权限校验TODO
        //创建班级
        let CreateAdministrativeClassesParams {
            class_name,
            class_code,
            academic_year,
            grade_level_code,
            teacher_id,
        } = params;
        // VALUES ('一年级一班','101',2025,'G1','00000000-0000-0000-0000-ffff00000000'::uuid);
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.administrative_classes (class_name,class_code,academic_year,grade_level_code,teacher_id) VALUES (",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , ")
            .push_bind(class_code)
            .push(" , ")
            .push_bind(academic_year)
            .push(" , ")
            .push_bind(grade_level_code)
            .push(" , ")
            .push_bind(teacher_id)
            .push(" )");
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        //查询
        Ok(self.find_by_code(schema_name, class_code).await?)
    }
}
