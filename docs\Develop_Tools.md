# 研发工具清单

## 开发语言

### 前端开发
- **JavaScript** - 最主流的前端开发语言
- **TypeScript** - 带类型检查的JavaScript超集
- **HTML/CSS** - 网页结构和样式基础
- **React** - Facebook开发的前端框架
- **Vue.js** - 渐进式前端框架

### 后端开发
- **Rust** - 内存安全的系统编程语言
- **Python** - 简洁易学，生态丰富[Develop_Tools.md](Develop_Tools.md)
- **Java** - 企业级应用主流语言
- **Node.js** - 基于JavaScript的服务端运行时

### 移动开发
- **Kotlin/Java** - Android原生开发语言
- **React Native** - Facebook跨平台移动开发框架

## 数据库

### 关系型数据库
- **PostgreSQL** - 功能强大的开源数据库
- **MySQL** - 最流行的开源关系型数据库
- **SQLite** - 轻量级嵌入式数据库

### NoSQL数据库
- **MongoDB** - 文档型数据库
- **Redis** - 内存键值存储
- **Tantivy** - 搜索和分析引擎

### 专用数据库
- **Neo4j** - 图数据库
- **SurrealDB** - 多模态数据库

## 源代码管理

### 版本控制系统
- **Git** - 分布式版本控制系统（最主流）

### 代码托管平台
- **Codeup** - 国内阿里云效代码管理平台

## 测试工具

### 单元测试
- **JUnit** - Java单元测试框架
- **pytest** - Python测试框架
- **Jest** - JavaScript测试框架
- **Mocha** - JavaScript测试框架

### API与集成测试
- **Swagger/OpenAPI** - API文档和测试
- **Postman** - API测试工具
- **JetBrains REST tools** - REST API客户端

### 自动化测试
- **Selenium** - Web自动化测试框架
- **Playwright** - 跨浏览器测试框架

### 性能测试
- **JMeter** - 开源性能测试工具
- **Goose** - 负载测试工具

## Bug管理

### 项目管理与缺陷跟踪
- **Codeup** - 项目管理工具

## 版本管理

### 发布管理
- **Jenkins** - 开源自动化服务器

### 包管理
- **bun** - JavaScript包管理器
- **uv** - Python包管理器
- **Maven** - Java项目管理工具
- **Cargo** - Rust包管理器

### 容器化
- **Docker** - 容器化平台
- **Podman** - 无守护进程容器引擎
- **containerd** - 容器运行时

### 编排工具
- **Kubernetes** - 容器编排平台
- **Docker Compose** - 多容器应用定义工具

## 文档管理

### 技术文档
- **Notion** - 全能知识管理工具
- **CodeUp文档/项目README** - 技术文档发布平台

### 协作文档
- **Microsoft 365** - 微软办公套件
- **钉钉文档** - 钉钉协作平台
- **腾讯文档** - 腾讯在线文档

---