{"rustc": 1842507548689473721, "features": "[\"cors\", \"default\", \"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 12056071534646705333, "deps": [[784494742817713399, "tower_service", false, 14074221061578414422], [1906322745568073236, "pin_project_lite", false, 16220677842723031536], [4121350475192885151, "iri_string", false, 2315945142404624281], [5695049318159433696, "tower", false, 3975352489635114627], [7712452662827335977, "tower_layer", false, 8146032542958474769], [7896293946984509699, "bitflags", false, 17891973186684516612], [9010263965687315507, "http", false, 3133077987240373595], [10629569228670356391, "futures_util", false, 2747797314946402595], [14084095096285906100, "http_body", false, 4683579932501336993], [16066129441945555748, "bytes", false, 92163385925654338]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-fd0ce1006edfb93f\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}