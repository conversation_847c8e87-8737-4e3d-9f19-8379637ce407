use serde::{Deserialize, Serialize};
use uuid::Uuid;
use sqlx::FromRow;


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct HomeworkPapers{
    pub id: Uuid,
    pub homework_id: Uuid,
    pub paper_id: Uuid,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct BindPapersToHomeworkParams{
    pub homework_id: Uuid,
    pub paper_ids: Vec<Uuid>,
}