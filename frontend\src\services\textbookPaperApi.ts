import apiClient from './apiClient';
import { PageParams, PaginatedApiResponse } from '@/types';

const API_BASE_URL = (import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000') + '/api/v1';

// TypeScript Type
export interface TextbookPaper {
  id: string;
  paper_id: string;
  textbook_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateTextbookPaperRequest {
  paper_id: string;
  textbook_id: string;
}

export interface UpdateTextbookPaperRequest extends Partial<CreateTextbookPaperRequest> {}

// API Service
export const textbookPaperApi = {
  async getTextbookPapers(params?: { page?: number; page_size?: number }): Promise<TextbookPaper[]> {
    const response = await apiClient.get(`${API_BASE_URL}/textbook-paper/list`, { params });
    return response.data;
  },

  async getTextbookPaper(id: string): Promise<TextbookPaper> {
    const response = await apiClient.get(`${API_BASE_URL}/textbook-paper/${id}`);
    return response.data;
  },

  async createTextbookPaper(data: CreateTextbookPaperRequest): Promise<TextbookPaper> {
    const response = await apiClient.post(`${API_BASE_URL}/textbook-paper`, data);
    return response.data;
  },

  async updateTextbookPaper(id: string, data: UpdateTextbookPaperRequest): Promise<TextbookPaper> {
    const response = await apiClient.put(`${API_BASE_URL}/textbook-paper/${id}`, data);
    return response.data;
  },

  async deleteTextbookPaper(id: string): Promise<void> {
    await apiClient.delete(`${API_BASE_URL}/textbook-paper/${id}`);
  },
    /**
   * 作者：朱若彪
   * 说明：根据教辅id查询试卷信息
   */
    async getPapersByTextbookId(textbookId: string, params: PageParams): Promise<PaginatedApiResponse<Paper>> {
      return apiClient.get(`${API_BASE_URL}/textbook-paper/papers/${textbookId}`, { params });
    }
    
};
export interface Paper {
  id: string;
  paper_name: string;
  paper_content?: any;
  created_at?: string;
  updated_at?: string;
}