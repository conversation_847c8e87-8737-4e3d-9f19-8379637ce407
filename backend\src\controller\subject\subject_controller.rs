use crate::model::subject::{
    CreateSubjectRequest, SubjectQueryParams,  UpdateSubjectRequest,
};
use crate::model::{Subject};
use crate::service::subject::{PageSubjectParams, SubjectService};
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use axum::response::IntoResponse;
use axum::{
    extract::{Path, Query, State},
    routing::{get, patch, post},
    Json, Router,
};
use serde::Deserialize;
use uuid::Uuid;

pub fn create_router() -> Router<SubjectService> {
    Router::new()
        .route("/pageSubject", post(page_subject))
        .route("/", get(get_subjects))
        .route("/create", post(create_subject))
        .route("/summaries", get(get_subject_summaries))
        .route("/statistics", get(get_subject_statistics))
        .route("/orders", patch(update_subject_orders))
        .route("/check-code", get(check_code_availability))
        .route(
            "/{id}",
            get(get_subject_by_id)
                .put(update_subject)
                .delete(delete_subject),
        )
}

async fn page_subject(
    State(subject_service): State<SubjectService>,
    Json(params): Json<PageSubjectParams>,
) -> Result<PaginatedApiResponse<Subject>, ApiResponse<()>> {
    subject_service.page_subject(params).await
}

/// 获取学科列表（支持分页和查询）
#[axum::debug_handler]
async fn get_subjects(
    State(subject_service): State<SubjectService>,
    Query(params): Query<SubjectQueryParams>,
) -> impl IntoResponse {
    subject_service
        .get_subjects(params)
        .await
        .map(|page_result| {
            responses::paginated_success(
                page_result.data,
                page_result.page as i32,      // 转换类型
                page_result.page_size as i32, // 转换类型
                page_result.total,
                Some("获取学科成功"),
            )
        })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                &format!("获取学科信息失败：{}", e),
                Some("QUERY_SUBJECT_FAILED"),
            )
        })
}

/// 获取学科简要信息列表（用于下拉选择）
#[axum::debug_handler]
async fn get_subject_summaries(
    State(subject_service): State<SubjectService>,
    Query(params): Query<SubjectSummaryQuery>,
) -> impl IntoResponse {
    match subject_service
        .get_subject_summaries(params.is_active)
        .await
    {
        Ok(subject_summaries) => responses::success(subject_summaries, Some("获取学科成功")),
        Err(e) => responses::error(
            format!("获取学科信息失败：{}", e).as_str(),
            Some("QUERY_SUBJECT_FAILED"),
        ),
    }
}

/// 获取单个学科详情
#[axum::debug_handler]
async fn get_subject_by_id(
    State(subject_service): State<SubjectService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match subject_service.get_subject_by_id(id).await {
        Ok(subject) => responses::success(subject, Some("获取学科成功")),
        Err(e) => responses::error(
            format!("获取学科信息失败：{}", e).as_str(),
            Some("QUERY_SUBJECT_FAILED"),
        ),
    }
}

/// 创建学科
#[axum::debug_handler]
async fn create_subject(
    State(subject_service): State<SubjectService>,
    Json(payload): Json<CreateSubjectRequest>,
) -> impl IntoResponse {
    // 验证输入
    if payload.code.trim().is_empty() {
        return responses::error("学科编码不能为空", Some("CREATE_SUBJECT_FAILED"));
    }
    if payload.name.trim().is_empty() {
        return responses::error("学科名称不能为空", Some("CREATE_SUBJECT_FAILED"));
    }
    if payload.order_level < 0 {
        return responses::error("排序值必须为非负数", Some("CREATE_SUBJECT_FAILED"));
    }

    match subject_service.create_subject(payload).await {
        Ok(subject) => responses::success(subject, Some("学科创建成功")),
        Err(e) => responses::error(
            format!("学科创建失败：{}", e).as_str(),
            Some("CREATE_SUBJECT_FAILED"),
        ),
    }
}

/// 更新学科
#[axum::debug_handler]
async fn update_subject(
    State(subject_service): State<SubjectService>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateSubjectRequest>,
) -> impl IntoResponse {
    // 验证输入
    if let Some(ref name) = payload.name {
        if name.trim().is_empty() {
            return responses::error("学科名称不能为空", Some("UPDATE_SUBJECT_FAILED"));
        }
    }
    if let Some(order_level) = payload.order_level {
        if order_level < 0 {
            return responses::error("排序值必须为非负数", Some("UPDATE_SUBJECT_FAILED"));
        }
    }

    match subject_service.update_subject(id, payload).await {
        Ok(subject) => responses::success(subject, Some("学科更新成功")),
        Err(e) => responses::error(
            format!("学科更新失败：{}", e).as_str(),
            Some("UPDATE_SUBJECT_FAILED"),
        ),
    }
}

/// 删除学科（软删除）
#[axum::debug_handler]
async fn delete_subject(
    State(subject_service): State<SubjectService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match subject_service.delete_subject(id).await {
        Ok(_) => responses::success_no_data(Some("删除学科成功")),
        Err(e) => responses::error(
            format!("删除学科失败：{}", e).as_str(),
            Some("DELETE_SUBJECT_FAILED"),
        ),
    }
}

/// 获取学科统计信息
#[axum::debug_handler]
async fn get_subject_statistics(
    State(subject_service): State<SubjectService>,
) -> impl IntoResponse {
    match subject_service.get_subject_statistics().await {
        Ok(statistics) => responses::success(statistics, Some("学科统计成功")),
        Err(e) => responses::error(
            format!("获取学科信息失败：{}", e).as_str(),
            Some("QUERY_SUBJECT_FAILED"),
        ),
    }
}

/// 批量更新学科排序
#[axum::debug_handler]
async fn update_subject_orders(
    State(subject_service): State<SubjectService>,
    Json(payload): Json<UpdateSubjectOrdersRequest>,
) -> impl IntoResponse {
    if payload.orders.is_empty() {
        return responses::error("排序列表不能为空", Some("UPDATE_SUBJECT_FAILED"));
    }

    match subject_service.update_subject_orders(payload.orders).await {
        Ok(subject) => responses::success(subject, Some("排序列表更新成功")),
        Err(e) => responses::error(
            format!("排序列表更新失败：{}", e).as_str(),
            Some("UPDATE_SUBJECT_FAILED"),
        ),
    }
}

/// 检查学科代码是否可用
#[axum::debug_handler]
async fn check_code_availability(
    State(subject_service): State<SubjectService>,
    Query(params): Query<CheckCodeQuery>,
) -> impl IntoResponse {
    if params.code.trim().is_empty() {
        return responses::error("学科代码不能为空", Some("CHECK_SUBJECT_FAILED"));
    }

    match subject_service
        .is_code_available(&params.code, params.exclude_id)
        .await
    {
        Ok(bool) => responses::success(bool, Some("查询成功")),
        Err(e) => responses::error(
            format!("查询失败：{}", e).as_str(),
            Some("CHECK_SUBJECT_FAILED"),
        ),
    }
}

/// 学科简要信息查询参数
#[derive(Debug, Deserialize)]
struct SubjectSummaryQuery {
    pub is_active: Option<bool>,
}

/// 批量更新学科排序请求
#[derive(Debug, Deserialize)]
struct UpdateSubjectOrdersRequest {
    pub orders: Vec<(Uuid, i32)>,
}

/// 检查代码可用性查询参数
#[derive(Debug, Deserialize)]
struct CheckCodeQuery {
    pub code: String,
    pub exclude_id: Option<Uuid>,
}

/// 检查代码可用性响应
#[derive(Debug, serde::Serialize)]
struct CheckCodeResponse {
    pub is_available: bool,
}
