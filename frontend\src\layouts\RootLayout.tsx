
import { NavLink, Outlet } from 'react-router-dom';
import { 
  Sidebar, 
  SidebarContent, 
  SidebarFooter, 
  SidebarGroup, 
  SidebarGroupContent, 
  SidebarGroupLabel, 
  SidebarHeader, 
  SidebarInset, 
  SidebarMenu, 
  SidebarMenuButton, 
  SidebarMenuItem, 
  SidebarProvider, 
  SidebarRail, 
  SidebarTrigger 
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { ThemeToggle } from '@/components/ThemeToggle';
import {Book, FileText, BookCheck, School, Users2, ClipboardCheck, BarChart2, Building2, Shield, BookOpen, GraduationCap, UserCheck, Users, User} from 'lucide-react';
import logoImage from "@/assets/logo.png";
import {NavUser} from "@/layouts/components/nav-user.tsx";

const navItems = [
  { name: '教辅管理', path: '/teaching-aids', icon: Book },
  { name: '考试管理', path: '/exam-management', icon: FileText },
  { name: '作业管理', path: '/homework-management', icon: BookCheck },
  { name: '行政班管理', path: '/administrative-classes', icon: School },
  { name: '教学班管理', path: '/teaching-classes', icon: Users2 },
  { name: '阅卷中心', path: '/grading-center', icon: ClipboardCheck },
  { name: '统计分析', path: '/statistics', icon: BarChart2 },
];

const systemManagementItems = [
    { name: '租户管理', path: '/tenants', icon: Building2 },
    { name: '角色管理', path: '/roles', icon: Shield },
    { name: '学科管理', path: '/subjects', icon: BookOpen },
    { name: '年级管理', path: '/grades', icon: GraduationCap },
    { name: '学生管理', path: '/students', icon: UserCheck },
    { name: '教师管理', path: '/teachers', icon: Users },
    { name: '用户管理', path: '/users', icon: User },
];
export default function RootLayout() {

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <Sidebar collapsible="icon">
          <SidebarHeader>
            <div className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-white border border-gray-200 shadow-sm">
                <img
                    src={logoImage}
                    alt="语文出版社logo"
                    className="w-8 h-8 object-contain"
                />
              </div>
              <div className="flex flex-col group-data-[collapsible=icon]:hidden">
                <h1 className="text-lg font-bold text-sidebar-foreground">Teach Wise</h1>
                <p className="text-xs text-sidebar-foreground/70">语文出版社数智辅教综合服务平台</p>
              </div>
            </div>
          </SidebarHeader>

          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>主要功能</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {navItems.map((item) => (
                    <SidebarMenuItem key={item.name}>
                      <SidebarMenuButton 
                        asChild 
                        tooltip={item.name}
                      >
                        <NavLink 
                          to={item.path}
                        >
                          <item.icon />
                          <span>{item.name}</span>
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarGroup>
              <SidebarGroupLabel>系统管理</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {systemManagementItems.map((item) => (
                      <SidebarMenuItem key={item.name}>
                        <SidebarMenuButton
                            asChild
                            tooltip={item.name}
                        >
                          <NavLink
                              to={item.path}
                          >
                            <item.icon />
                            <span>{item.name}</span>
                          </NavLink>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

          </SidebarContent>

          <SidebarFooter>
            <NavUser user={{ name: '管理员', email: '<EMAIL>', avatar: 'https://avatars.githubusercontent.com/u/10656201?v=4' }} />
          </SidebarFooter>
          
          <SidebarRail />
        </Sidebar>

        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-6">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <div className="flex items-center justify-between flex-1">
              <div className="flex items-center space-x-2">
                <h2 className="text-lg font-semibold">工作台</h2>
              </div>
              <ThemeToggle />
            </div>
          </header>
          
          <main className="flex-1 overflow-auto p-6">
            <div className="max-w-7xl mx-auto">
              <Outlet />
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
