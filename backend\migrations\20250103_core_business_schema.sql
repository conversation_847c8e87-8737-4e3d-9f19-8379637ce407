-- Deep-Mate Core Business Schema Migration
-- Migration: 20250103_core_business_schema.migrations_temp

-- =============================================
-- PUBLIC SCHEMA CORE BUSINESS TABLES
-- =============================================

-- Enhanced subjects table with better organization
CREATE TABLE IF NOT EXISTS public.subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    order_level INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grade levels management
CREATE TABLE IF NOT EXISTS public.grade_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    order_level INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Central question bank (shared across all tenants)
CREATE TABLE IF NOT EXISTS public.question_bank (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_type VARCHAR(30) CHECK (source_type IN ('manual', 'textbook_exercise')),
    source_id UUID,
    question_type VARCHAR(20) NOT NULL,
    question_content TEXT NOT NULL,
    options JSONB DEFAULT '{}',
    correct_answer TEXT,
    answer_content TEXT,
    explanation TEXT,
    difficulty_level VARCHAR(10),
    knowledge_points JSONB DEFAULT '[]',
    tags JSONB DEFAULT '[]',
    estimated_time INTEGER DEFAULT 30,
    subject VARCHAR(50) NOT NULL,
    grade_level INTEGER NOT NULL,
    creator_id UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    status VARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Public exam paper templates
CREATE TABLE IF NOT EXISTS public.exam_papers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    subject VARCHAR(50) NOT NULL,
    grade_level INTEGER NOT NULL,
    total_score DECIMAL(5,2),
    description TEXT,
    structure JSONB, -- Paper structure definition
    creator_id UUID REFERENCES public.users(id),
    version INTEGER DEFAULT 1,
    status VARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Paper questions relationship
CREATE TABLE IF NOT EXISTS public.exam_paper_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paper_id UUID REFERENCES public.exam_papers(id) ON DELETE CASCADE,
    question_id UUID REFERENCES public.question_bank(id) ON DELETE CASCADE,
    question_order INTEGER NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(paper_id, question_order)
);

-- Public textbooks management
CREATE TABLE IF NOT EXISTS public.textbooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    subject_id UUID REFERENCES public.subjects(id),
    grade_level_id UUID REFERENCES public.grade_levels(id),
    publisher VARCHAR(100),
    publication_year INTEGER,
    isbn VARCHAR(20),
    version VARCHAR(50),
    status VARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    creator_id UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Textbook chapters
CREATE TABLE IF NOT EXISTS public.textbook_chapters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    textbook_id UUID REFERENCES public.textbooks(id) ON DELETE CASCADE,
    chapter_number INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    page_start INTEGER,
    page_end INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(textbook_id, chapter_number)
);

-- Textbook exercises
CREATE TABLE IF NOT EXISTS public.textbook_exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    textbook_id UUID REFERENCES public.textbooks(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES public.textbook_chapters(id) ON DELETE CASCADE,
    exercise_number VARCHAR(20) NOT NULL,
    title VARCHAR(200),
    description TEXT,
    page_number INTEGER,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    exercise_type VARCHAR(30),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System configuration
CREATE TABLE IF NOT EXISTS public.system_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification templates
CREATE TABLE IF NOT EXISTS public.notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    variables JSONB DEFAULT '[]', -- Available template variables
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System notifications
CREATE TABLE IF NOT EXISTS public.system_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50) NOT NULL, -- 'all', 'tenant', 'user'
    target_id UUID, -- tenant_id or user_id
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    is_read BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for core business tables
CREATE INDEX IF NOT EXISTS idx_subjects_code ON public.subjects(code);
CREATE INDEX IF NOT EXISTS idx_subjects_active ON public.subjects(is_active);
CREATE INDEX IF NOT EXISTS idx_grade_levels_code ON public.grade_levels(code);
CREATE INDEX IF NOT EXISTS idx_grade_levels_active ON public.grade_levels(is_active);
CREATE INDEX IF NOT EXISTS idx_question_bank_subject ON public.question_bank(subject);
CREATE INDEX IF NOT EXISTS idx_question_bank_grade ON public.question_bank(grade_level);
CREATE INDEX IF NOT EXISTS idx_question_bank_status ON public.question_bank(status);
CREATE INDEX IF NOT EXISTS idx_question_bank_creator ON public.question_bank(creator_id);
CREATE INDEX IF NOT EXISTS idx_exam_papers_subject ON public.exam_papers(subject);
CREATE INDEX IF NOT EXISTS idx_exam_papers_grade ON public.exam_papers(grade_level);
CREATE INDEX IF NOT EXISTS idx_exam_papers_status ON public.exam_papers(status);
CREATE INDEX IF NOT EXISTS idx_exam_paper_questions_paper ON public.exam_paper_questions(paper_id);
CREATE INDEX IF NOT EXISTS idx_exam_paper_questions_question ON public.exam_paper_questions(question_id);
CREATE INDEX IF NOT EXISTS idx_textbooks_subject ON public.textbooks(subject_id);
CREATE INDEX IF NOT EXISTS idx_textbooks_grade ON public.textbooks(grade_level_id);
CREATE INDEX IF NOT EXISTS idx_textbooks_status ON public.textbooks(status);
CREATE INDEX IF NOT EXISTS idx_textbook_chapters_textbook ON public.textbook_chapters(textbook_id);
CREATE INDEX IF NOT EXISTS idx_textbook_exercises_textbook ON public.textbook_exercises(textbook_id);
CREATE INDEX IF NOT EXISTS idx_textbook_exercises_chapter ON public.textbook_exercises(chapter_id);
CREATE INDEX IF NOT EXISTS idx_system_config_key ON public.system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_notification_templates_name ON public.notification_templates(name);
CREATE INDEX IF NOT EXISTS idx_notification_templates_active ON public.notification_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_system_notifications_target ON public.system_notifications(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_system_notifications_created ON public.system_notifications(created_at);

-- Insert default subjects
INSERT INTO public.subjects (code, name, order_level) VALUES
('MATH', '数学', 1),
('CHINESE', '语文', 2),
('ENGLISH', '英语', 3),
('PHYSICS', '物理', 4),
('CHEMISTRY', '化学', 5),
('BIOLOGY', '生物', 6),
('HISTORY', '历史', 7),
('GEOGRAPHY', '地理', 8),
('POLITICS', '政治', 9)
ON CONFLICT (code) DO NOTHING;

-- Insert default grade levels
INSERT INTO public.grade_levels (code, name, order_level) VALUES
('G1', '一年级', 1),
('G2', '二年级', 2),
('G3', '三年级', 3),
('G4', '四年级', 4),
('G5', '五年级', 5),
('G6', '六年级', 6),
('G7', '七年级', 7),
('G8', '八年级', 8),
('G9', '九年级', 9),
('G10', '高一', 10),
('G11', '高二', 11),
('G12', '高三', 12)
ON CONFLICT (code) DO NOTHING;
