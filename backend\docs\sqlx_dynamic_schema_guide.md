# Sqlx 动态 Schema 处理指南

本文档详细说明如何在 Sqlx 中处理动态 schema 的 SQL 查询，避免编译错误，并保持类型安全。

## 问题背景

在多租户应用中，每个租户都有独立的数据库 schema。这带来了以下挑战：

1. **编译时检查限制**: `sqlx::query!` 宏在编译时验证 SQL，但动态 schema 在编译时不存在
2. **类型安全**: 动态构建的 SQL 查询失去了编译时的类型检查
3. **SQL 注入风险**: 动态拼接 SQL 可能引入安全漏洞

## 解决方案概览

我们提供了四种主要的解决方案：

### 1. 使用 `search_path` + 事务（推荐）

**优点**: 保持类型安全，避免 SQL 注入
**缺点**: 需要在事务中执行

```rust
use crate::service::dynamic_query_service::DynamicQueryService;

let service = DynamicQueryService::new(pool);

let students = service
    .execute_in_tenant_schema("tenant_001", |tx| {
        Box::pin(async move {
            // 在这里可以使用 sqlx::query! 宏，因为 search_path 已设置
            let rows = sqlx::query_as!(
                Student,
                "SELECT id, name, student_number FROM students WHERE active = true"
            )
            .fetch_all(&mut **tx)
            .await?;
            
            Ok(rows)
        })
    })
    .await?;
```

### 2. 使用 `sqlx::query` 动态构建

**优点**: 灵活性高，可以完全动态构建查询
**缺点**: 失去编译时类型检查，需要手动处理类型转换

```rust
let table_name = format!("\"{}\".\"students\"", schema_name);
let sql = format!("SELECT * FROM {} WHERE id = $1", table_name);

let row = sqlx::query(&sql)
    .bind(student_id)
    .fetch_optional(&pool)
    .await?;

if let Some(row) = row {
    let student = Student {
        id: row.get("id"),
        name: row.get("name"),
        student_number: row.get("student_number"),
        // ... 手动映射其他字段
    };
}
```

### 3. 使用数据库函数（最安全）

**优点**: 最高的安全性，逻辑在数据库层
**缺点**: 需要编写和维护数据库函数

```sql
-- 在数据库中创建函数
CREATE OR REPLACE FUNCTION get_students_by_tenant(tenant_schema TEXT)
RETURNS TABLE(id UUID, name TEXT, student_number TEXT) AS $$
BEGIN
    RETURN QUERY EXECUTE format('SELECT id, name, student_number FROM %I.students', tenant_schema);
END;
$$ LANGUAGE plpgsql;
```

```rust
let rows = service
    .call_tenant_function("get_students_by_tenant", schema_name, &[])
    .await?;
```

### 4. 预编译查询模板

**优点**: 性能好，相对安全
**缺点**: 灵活性有限

```rust
// 预定义查询模板
const STUDENT_QUERY_TEMPLATE: &str = r#"
    SET search_path TO "{schema}";
    SELECT id, name, student_number FROM students WHERE active = $1;
"#;

let sql = STUDENT_QUERY_TEMPLATE.replace("{schema}", schema_name);
let rows = sqlx::query(&sql)
    .bind(true)
    .fetch_all(&pool)
    .await?;
```

## 最佳实践

### 1. 安全验证

始终验证动态输入的安全性：

```rust
fn validate_schema_name(schema_name: &str) -> Result<()> {
    use regex::Regex;
    static SCHEMA_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[a-z0-9_]+$").unwrap());
    
    if !SCHEMA_REGEX.is_match(schema_name) {
        return Err(anyhow::anyhow!("Invalid schema name"));
    }
    Ok(())
}
```

### 2. 使用引号包围标识符

```rust
// 正确：使用双引号包围 schema 和表名
let table_name = format!("\"{}\".\"{}\"", schema_name, table_name);

// 错误：直接拼接可能导致 SQL 注入
let table_name = format!("{}.{}", schema_name, table_name);
```

### 3. 事务管理

对于需要多个查询的操作，使用事务确保一致性：

```rust
let result = service
    .execute_in_tenant_schema(schema_name, |tx| {
        Box::pin(async move {
            // 多个相关操作在同一事务中
            let student = create_student(&mut **tx, &request).await?;
            update_statistics(&mut **tx, student.id).await?;
            Ok(student)
        })
    })
    .await?;
```

### 4. 错误处理

提供清晰的错误信息：

```rust
.map_err(|e| match e {
    sqlx::Error::RowNotFound => AppError::NotFound("Student not found".to_string()),
    sqlx::Error::Database(db_err) if db_err.code() == Some("23505") => {
        AppError::Conflict("Student number already exists".to_string())
    },
    _ => AppError::DatabaseError(format!("Database operation failed: {}", e)),
})
```

## 性能考虑

### 1. 连接池配置

```rust
let pool = PgPoolOptions::new()
    .max_connections(20)
    .min_connections(5)
    .acquire_timeout(Duration::from_secs(30))
    .connect(&database_url)
    .await?;
```

### 2. 查询优化

- 使用索引优化查询性能
- 避免在循环中执行数据库查询
- 使用批量操作替代单个操作

### 3. 缓存策略

对于频繁查询的数据，考虑使用缓存：

```rust
// 缓存租户的 schema 名称
let schema_name = cache
    .get_or_insert(tenant_id, || async {
        get_tenant_schema(&pool, tenant_id).await
    })
    .await?;
```

## 测试策略

### 1. 单元测试

```rust
#[tokio::test]
async fn test_dynamic_query_validation() {
    let service = DynamicQueryService::new(test_pool()).await;
    
    // 测试有效的 schema 名称
    assert!(service.validate_schema_name("tenant_001").is_ok());
    
    // 测试无效的 schema 名称
    assert!(service.validate_schema_name("tenant'; DROP TABLE users; --").is_err());
}
```

### 2. 集成测试

```rust
#[tokio::test]
async fn test_tenant_data_operations() {
    let pool = setup_test_database().await;
    let service = TenantDataService::new(pool);
    
    // 创建测试租户 schema
    setup_tenant_schema("test_tenant").await;
    
    // 测试学生创建
    let request = CreateStudentRequest {
        name: "Test Student".to_string(),
        student_number: "TEST001".to_string(),
        email: None,
    };
    
    let student = service.create_student("test_tenant", request).await.unwrap();
    assert_eq!(student.name, "Test Student");
    
    // 清理测试数据
    cleanup_tenant_schema("test_tenant").await;
}
```

## 常见问题

### Q: 为什么不能在动态 schema 中使用 `sqlx::query!` 宏？

A: `sqlx::query!` 宏在编译时连接数据库验证 SQL 语法和类型。动态 schema 在编译时不存在，所以验证会失败。

### Q: 如何在保持类型安全的同时使用动态 schema？

A: 使用 `search_path` 方法。在事务中设置 `search_path` 后，可以继续使用 `sqlx::query!` 宏。

### Q: 动态构建的 SQL 如何防止注入攻击？

A: 1) 严格验证所有动态输入；2) 使用参数化查询；3) 使用双引号包围标识符；4) 考虑使用数据库函数。

### Q: 性能方面有什么需要注意的？

A: 1) 避免频繁切换 `search_path`；2) 使用连接池；3) 对频繁查询的数据使用缓存；4) 优化数据库索引。

## 总结

处理动态 schema 的关键是在灵活性、安全性和性能之间找到平衡。推荐的方法是：

1. **首选**: 使用 `search_path` + 事务的方式
2. **备选**: 使用数据库函数处理复杂逻辑
3. **谨慎使用**: 动态构建 SQL（需要严格的安全验证）

通过合理的架构设计和严格的安全措施，可以在多租户环境中安全高效地使用 Sqlx。
