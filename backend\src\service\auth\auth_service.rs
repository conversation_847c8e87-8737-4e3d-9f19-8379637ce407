use crate::model::user::auth::*;
use crate::service::sms::SmsService;
use crate::utils::jwt;
use crate::utils::password::PasswordService;
use crate::utils::error::AppError;
use crate::utils::error_handler::{SqlxResultExt, AnyhowResultExt, errors, AppResult};
use chrono::{DateTime, Duration, Utc};
use ipnetwork::IpNetwork;
use regex::Regex;
use sqlx::{PgPool, Row};
use std::net::IpAddr;
use std::sync::Arc;
use tracing::{error, info, warn};
use uuid::Uuid;

pub struct AuthService {
    db: PgPool,
    sms_service: Arc<SmsService>,
    password_service: Arc<PasswordService>,
    phone_regex: Regex,
}

impl AuthService {
    pub fn new(
        db: PgPool,
        sms_service: Arc<SmsService>,
        password_service: Arc<PasswordService>,
    ) -> Self {
        let phone_regex = Regex::new(r"^\+?[1-9]\d{1,14}$").unwrap();
        
        Self {
            db,
            sms_service,
            password_service,
            phone_regex,
        }
    }

    pub async fn send_verification_code(
        &self,
        phone_number: &str,
        code_type: &str,
    ) -> AuthResult<SendVerificationCodeResponse> {
        // Validate phone number
        let normalized_phone = self.normalize_phone_number(phone_number)?;
        
        // Check if user exists for login/reset codes
        if code_type == "login" || code_type == "reset" {
            let user_exists = self.check_user_exists(&normalized_phone).await?;
            if !user_exists {
                return Err(AuthError::UserNotFound);
            }
        }
        
        // Check if phone is already registered for registration codes
        if code_type == "registration" {
            let user_exists = self.check_user_exists(&normalized_phone).await?;
            if user_exists {
                return Err(AuthError::PhoneAlreadyRegistered);
            }
        }

        // Clean up expired codes
        self.cleanup_expired_codes(&normalized_phone).await?;

        // Check for existing valid code
        if let Some(existing_code) = self.get_valid_code(&normalized_phone, code_type).await? {
            let expires_in = (existing_code.expires_at - Utc::now()).num_seconds();
            return Ok(SendVerificationCodeResponse {
                success: true,
                message: "Verification code already sent".to_string(),
                data: VerificationCodeData {
                    expires_in,
                    can_resend_after: 60, // Can resend after 1 minute
                },
            });
        }

        // Generate new verification code
        let code = self.sms_service.generate_verification_code();
        let expires_at = Utc::now() + Duration::minutes(5);

        // Save code to database
        sqlx::query!(
            r#"
            INSERT INTO public.phone_verification_codes
            (phone_number, verification_code, code_type, expires_at)
            VALUES ($1, $2, $3, $4)
            "#,
            normalized_phone,
            code,
            code_type,
            expires_at
        )
        .execute(&self.db)
        .await?;

        // Send SMS
        let delivery_result = self.sms_service
            .send_verification_code(&normalized_phone, code_type, &code)
            .await?;

        info!(
            "Verification code sent to {} via {}: {}",
            self.mask_phone(&normalized_phone),
            delivery_result.provider,
            delivery_result.message_id
        );

        Ok(SendVerificationCodeResponse {
            success: true,
            message: "Verification code sent successfully".to_string(),
            data: VerificationCodeData {
                expires_in: 300, // 5 minutes
                can_resend_after: 60, // 1 minute
            },
        })
    }

    pub async fn register(&self, request: RegisterRequest) -> AuthResult<RegisterResponse> {
        let normalized_phone = self.normalize_phone_number(&request.phone_number)?;
        
        // Validate password strength
        self.password_service.validate_password_strength(&request.password)?;
        
        // Verify the verification code
        self.verify_code(&normalized_phone, &request.verification_code, "registration").await?;
        
        // Check if user already exists
        if self.check_user_exists(&normalized_phone).await? {
            return Err(AuthError::PhoneAlreadyRegistered);
        }

        // Hash password
        let (password_hash, salt) = self.password_service.hash_password(&request.password)?;

        // Create user
        let user_id = Uuid::new_v4();
        let now = Utc::now();

        sqlx::query!(
            r#"
            INSERT INTO public.users
            (id, phone_number, phone_verified, phone_verified_at, password_hash, salt, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            "#,
            user_id,
            normalized_phone,
            true,
            now,
            password_hash,
            salt,
            now,
            now
        )
        .execute(&self.db)
        .await?;

        // Mark verification code as used
        self.mark_code_as_verified(&normalized_phone, &request.verification_code).await?;

        // Generate tokens
        let (access_token, refresh_token) = self.generate_tokens(user_id, None).await?;

        // Create session
        self.create_session(
            user_id,
            &access_token,
            &refresh_token,
            None,
            request.device_info.as_ref(),
            None,
            None,
        ).await?;

        info!("User registered successfully: {}", self.mask_phone(&normalized_phone));

        Ok(RegisterResponse {
            success: true,
            message: "Registration successful".to_string(),
            data: AuthData {
                user_id,
                access_token,
                refresh_token,
                expires_in: 3600,
                user_profile: UserProfile {
                    username: request.username,
                    phone_number: normalized_phone,
                    phone_verified: true,
                    created_at: now,
                },
            },
        })
    }

    pub async fn login(&self, request: LoginRequest) -> AuthResult<LoginResponse> {
        // Prioritize username login over phone number login
        let user = match (&request.username, &request.phone_number) {
            (Some(username), _) => {
                // Username-based login (primary method)
                self.get_user_by_username(username).await?
                    .ok_or(AuthError::UserNotFound)?
            },
            (None, Some(phone_number)) => {
                // Phone-based login (fallback method)
                let normalized_phone = self.normalize_phone_number(phone_number)?;
                self.get_user_by_phone(&normalized_phone).await?
                    .ok_or(AuthError::UserNotFound)?
            },
            (None, None) => {
                // No identifier provided
                return Err(AuthError::InvalidCredentials);
            }
        };

        // Check if account is locked
        if let Some(locked_until) = user.locked_until {
            if locked_until > Utc::now() {
                return Err(AuthError::AccountLocked);
            }
        }

        // Verify password
        if !self.password_service.verify_password(&request.password, &user.password_hash, &user.salt)? {
            self.increment_failed_attempts(user.id).await?;
            return Err(AuthError::InvalidCredentials);
        }

            // Verify SMS code if provided (optional 2FA)
        if let Some(verification_code) = &request.verification_code {
            self.verify_code(&user.phone_number, verification_code, "login").await?;
            self.mark_code_as_verified(&user.phone_number, verification_code).await?;
        }

        // Reset failed attempts and update last login
        self.reset_failed_attempts_and_update_login(user.id).await?;

        // Get user identities
        let identities = self.get_user_identities(user.id).await?;
        let primary_identity = identities.iter().find(|i| i.is_primary).cloned();

        // Generate tokens
        let (access_token, refresh_token) = self.generate_tokens(user.id, primary_identity.as_ref().map(|i| i.identity_id)).await?;

        // Create session
        let _available_identity_ids: Vec<Uuid> = identities.iter().map(|i| i.identity_id).collect();
        self.create_session(
            user.id,
            &access_token,
            &refresh_token,
            primary_identity.as_ref().map(|i| i.identity_id),
            request.device_info.as_ref(),
            None,
            None,
        ).await?;

        // Log successful login with appropriate identifier
        let identifier = if request.username.is_some() {
            format!("username: {}", user.username)
        } else {
            format!("phone: {}", self.mask_phone(&user.phone_number))
        };
        info!("User logged in successfully with {}", identifier);

        Ok(LoginResponse {
            success: true,
            message: "Login successful".to_string(),
            data: LoginData {
                user_id: user.id,
                access_token,
                refresh_token,
                expires_in: 3600,
                available_identities: identities,
                current_identity: primary_identity,
            },
        })
    }

    pub async fn refresh_token(&self, refresh_token: &str) -> AuthResult<(String, String)> {
        // Validate refresh token
        let claims = jwt::validate_token(refresh_token)?;
        
        // Get session
        let session = self.get_session_by_refresh_token(refresh_token).await?
            .ok_or(AuthError::InvalidCredentials)?;

        if !session.is_active || session.refresh_expires_at < Utc::now() {
            return Err(AuthError::InvalidCredentials);
        }

        // Generate new tokens
        let user_id = Uuid::parse_str(&claims.sub).map_err(|_| AuthError::JwtError(jsonwebtoken::errors::Error::from(jsonwebtoken::errors::ErrorKind::InvalidToken)))?;
        let (new_access_token, new_refresh_token) = self.generate_tokens(
            user_id,
            session.current_identity_id,
        ).await?;

        // Update session with new tokens
        sqlx::query!(
            r#"
            UPDATE public.user_sessions
            SET session_token = $1, refresh_token = $2, last_activity_at = $3
            WHERE id = $4
            "#,
            new_access_token,
            new_refresh_token,
            Utc::now(),
            session.id
        )
        .execute(&self.db)
        .await?;

        Ok((new_access_token, new_refresh_token))
    }

    pub async fn logout(&self, session_token: &str) -> AuthResult<()> {
        sqlx::query!(
            "UPDATE public.user_sessions SET is_active = false WHERE session_token = $1",
            session_token
        )
        .execute(&self.db)
        .await?;

        Ok(())
    }

    // Helper methods
    fn normalize_phone_number(&self, phone: &str) -> AuthResult<String> {
        let cleaned = phone.trim().replace(" ", "").replace("-", "");
        
        if !self.phone_regex.is_match(&cleaned) {
            return Err(AuthError::InvalidPhoneNumber);
        }

        // Add country code if missing (assuming China +86)
        if cleaned.starts_with("1") && cleaned.len() == 11 {
            Ok(format!("+86{}", cleaned))
        } else if cleaned.starts_with("+") {
            Ok(cleaned)
        } else {
            Ok(format!("+{}", cleaned))
        }
    }

    async fn check_user_exists(&self, phone: &str) -> AuthResult<bool> {
        let count: i64 = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM public.users WHERE phone_number = $1",
            phone
        )
        .fetch_one(&self.db)
        .await?
        .unwrap_or(0);

        Ok(count > 0)
    }

    async fn cleanup_expired_codes(&self, phone: &str) -> AuthResult<()> {
        sqlx::query!(
            "DELETE FROM public.phone_verification_codes WHERE phone_number = $1 AND expires_at < $2",
            phone,
            Utc::now()
        )
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn get_valid_code(&self, phone: &str, code_type: &str) -> AuthResult<Option<PhoneVerificationCode>> {
        let code = sqlx::query_as::<_, PhoneVerificationCode>(
            r#"
            SELECT id, phone_number, verification_code, code_type, expires_at, attempts, max_attempts, verified, verified_at, created_at
            FROM public.phone_verification_codes
            WHERE phone_number = $1 AND code_type = $2 AND expires_at > $3 AND verified = false
            ORDER BY created_at DESC
            LIMIT 1
            "#
        )
        .bind(phone)
        .bind(code_type)
        .bind(Utc::now())
        .fetch_optional(&self.db)
        .await?;

        Ok(code)
    }

    async fn verify_code(&self, phone: &str, code: &str, code_type: &str) -> AuthResult<()> {
        let verification_code = sqlx::query_as::<_, PhoneVerificationCode>(
            r#"
            SELECT id, phone_number, verification_code, code_type, expires_at, attempts, max_attempts, verified, verified_at, created_at
            FROM public.phone_verification_codes
            WHERE phone_number = $1 AND verification_code = $2 AND code_type = $3 AND verified = false
            ORDER BY created_at DESC
            LIMIT 1
            "#
        )
        .bind(phone)
        .bind(code)
        .bind(code_type)
        .fetch_optional(&self.db)
        .await?
        .ok_or(AuthError::InvalidVerificationCode)?;

        if verification_code.expires_at < Utc::now() {
            return Err(AuthError::VerificationCodeExpired);
        }

        if verification_code.attempts >= verification_code.max_attempts {
            return Err(AuthError::TooManyAttempts);
        }

        // Increment attempts
        sqlx::query!(
            "UPDATE public.phone_verification_codes SET attempts = attempts + 1 WHERE id = $1",
            verification_code.id
        )
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn mark_code_as_verified(&self, phone: &str, code: &str) -> AuthResult<()> {
        sqlx::query!(
            r#"
            UPDATE public.phone_verification_codes
            SET verified = true, verified_at = $1
            WHERE phone_number = $2 AND verification_code = $3
            "#,
            Utc::now(),
            phone,
            code
        )
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn get_user_by_phone(&self, phone: &str) -> AuthResult<Option<User>> {
        let user = sqlx::query_as::<_, User>(
            "SELECT id, username, phone_number, phone_verified, phone_verified_at, password_hash, salt, created_at, updated_at, last_login_at, is_active, failed_login_attempts, locked_until FROM public.users WHERE phone_number = $1"
        )
        .bind(phone)
        .fetch_optional(&self.db)
        .await?;

        Ok(user)
    }
    
    async fn get_user_by_username(&self, username: &str) -> AuthResult<Option<User>> {
        // First check if the username column exists
        let column_exists_result = sqlx::query(
            "SELECT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = 'users' 
                AND column_name = 'username'
            )"
        )
        .fetch_one(&self.db)
        .await;
        
        // If there's an error or the column doesn't exist, return None
        if let Err(_) = column_exists_result {
            return Ok(None);
        }
        
        let column_exists: bool = column_exists_result.unwrap().get(0);
        if !column_exists {
            // If the column doesn't exist, return None
            return Ok(None);
        }

        // If the column exists, try to get the user
        let user_result = sqlx::query_as::<_, User>(
            "SELECT id, username, phone_number, phone_verified, phone_verified_at, password_hash, salt, created_at, updated_at, last_login_at, is_active, failed_login_attempts, locked_until FROM public.users WHERE username = $1"
        )
        .bind(username)
        .fetch_optional(&self.db)
        .await;
        
        match user_result {
            Ok(user) => Ok(user),
            Err(_) => Ok(None) // If there's an error, return None
        }
    }
    
    async fn check_username_exists(&self, username: &str) -> AuthResult<bool> {
        // First check if the username column exists
        let column_exists_result = sqlx::query(
            "SELECT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = 'users' 
                AND column_name = 'username'
            )"
        )
        .fetch_one(&self.db)
        .await;
        
        // If there's an error or the column doesn't exist, return false
        if let Err(_) = column_exists_result {
            return Ok(false);
        }
        
        let column_exists: bool = column_exists_result.unwrap().get(0);
        if !column_exists {
            // If the column doesn't exist, return false (username doesn't exist)
            return Ok(false);
        }

        // If the column exists, check if the username exists
        let count_result = sqlx::query(
            "SELECT COUNT(*) FROM public.users WHERE username = $1"
        )
        .bind(username)
        .fetch_one(&self.db)
        .await;
        
        // If there's an error, return false
        if let Err(_) = count_result {
            return Ok(false);
        }
        
        let count: i64 = count_result.unwrap().get(0);
        Ok(count > 0)
    }

    async fn increment_failed_attempts(&self, user_id: Uuid) -> AuthResult<()> {
        let result = sqlx::query!(
            r#"
            UPDATE public.users 
            SET failed_login_attempts = failed_login_attempts + 1,
                locked_until = CASE 
                    WHEN failed_login_attempts + 1 >= 5 THEN $2
                    ELSE locked_until
                END
            WHERE id = $1
            RETURNING failed_login_attempts
            "#,
            user_id,
            Utc::now() + Duration::minutes(15) // Lock for 15 minutes after 5 failed attempts
        )
        .fetch_one(&self.db)
        .await?;

        if result.failed_login_attempts.unwrap_or(0) >= 5 {
            warn!("User account locked due to too many failed attempts: {}", user_id);
        }

        Ok(())
    }

    async fn reset_failed_attempts_and_update_login(&self, user_id: Uuid) -> AuthResult<()> {
        sqlx::query!(
            r#"
            UPDATE public.users
            SET failed_login_attempts = 0, locked_until = NULL, last_login_at = $1
            WHERE id = $2
            "#,
            Utc::now(),
            user_id
        )
        .execute(&self.db)
        .await?;

        Ok(())
    }

    async fn get_user_identities(&self, user_id: Uuid) -> AuthResult<Vec<IdentityInfo>> {
        // 获取所有活跃租户的schema
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT id as tenant_id, schema_name, name as tenant_name
            FROM public.tenants 
            WHERE status = 'active'
            ORDER BY created_at
            "#
        )
        .fetch_all(&self.db)
        .await?;

        let mut identities = Vec::new();

        // 遍历所有租户schema，查询用户在每个租户中的身份
        for tenant_record in tenant_schemas {
            let query = format!(
                r#"
                SELECT 
                    ui.id as identity_id,
                    ui.role_id,
                    ui.target_type,
                    ui.target_id,
                    ui.subject,
                    r.code as role_code,
                    r.name as role_name
                FROM "{schema_name}".user_identities ui
                JOIN public.roles r ON ui.role_id = r.id
                WHERE ui.user_id = $1
                ORDER BY ui.created_at DESC
                "#,
                schema_name = tenant_record.schema_name
            );

            let identity_records = sqlx::query(&query)
                .bind(user_id)
                .fetch_all(&self.db)
                .await
                .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist or query fails

            for record in identity_records {
                use sqlx::Row;
                let identity_id: Uuid = record.get("identity_id");
                let role_code: String = record.get("role_code");
                let role_name: String = record.get("role_name");

                identities.push(IdentityInfo {
                    identity_id,
                    tenant_id: tenant_record.tenant_id,  // Remove Some() wrapper
                    tenant_name: Some(tenant_record.tenant_name.clone()),
                    identity_type: role_code,
                    display_name: Some(role_name),  // Wrap with Some()
                    is_primary: false, // Since we don't have is_primary in the new schema
                });
            }
        }

        Ok(identities)
    }

    async fn generate_tokens(&self, user_id: Uuid, _identity_id: Option<Uuid>) -> AuthResult<(String, String)> {
        let access_token = jwt::generate_base_token(user_id)?;
        let refresh_token = jwt::generate_base_token(user_id)?; //FIXME: should be a refresh token
        Ok((access_token, refresh_token))
    }

    async fn create_session(
        &self,
        user_id: Uuid,
        session_token: &str,
        refresh_token: &str,
        current_identity_id: Option<Uuid>,
        device_info: Option<&serde_json::Value>,
        ip_address: Option<IpAddr>,
        user_agent: Option<&str>,
    ) -> AuthResult<Uuid> {
        let session_id = Uuid::new_v4();
        let now = Utc::now();
        let expires_at = now + Duration::hours(1);
        let refresh_expires_at = now + Duration::days(30);

        sqlx::query!(
            r#"
            INSERT INTO public.user_sessions 
            (id, user_id, session_token, refresh_token, current_identity_id, device_info, 
             ip_address, user_agent, expires_at, refresh_expires_at, last_activity_at, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#,
            session_id,
            user_id,
            session_token,
            refresh_token,
            current_identity_id,
            device_info,
            ip_address.map(|ip| IpNetwork::from(ip)),
            user_agent,
            expires_at,
            refresh_expires_at,
            now,
            now
        )
        .execute(&self.db)
        .await?;

        Ok(session_id)
    }

    async fn get_session_by_refresh_token(&self, refresh_token: &str) -> AuthResult<Option<UserSession>> {
        let session = sqlx::query_as::<_, UserSession>(
            "SELECT id, user_id, session_token, refresh_token, current_identity_id, available_identities, device_info, ip_address, user_agent, expires_at, refresh_expires_at, last_activity_at, is_active, created_at FROM public.user_sessions WHERE refresh_token = $1"
        )
        .bind(refresh_token)
        .fetch_optional(&self.db)
        .await?;

        Ok(session)
    }

    fn mask_phone(&self, phone: &str) -> String {
        if phone.len() > 7 {
            let start = &phone[..3];
            let end = &phone[phone.len()-4..];
            format!("{}****{}", start, end)
        } else {
            "***".to_string()
        }
    }
}