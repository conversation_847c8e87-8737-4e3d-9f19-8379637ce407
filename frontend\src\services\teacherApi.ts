/**
 * 教师管理 API 服务
 * 提供教师相关的 API 调用接口
 */

import apiClient from './apiClient';
import {
  Teacher,
  CreateTeacherRequest,
  UpdateTeacherRequest,
  TeacherSearchParams,
  TeacherDetailVO,
  TeacherListVO,
} from '@/types/teacher';

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  code: number;
  message?: string;
  data: T;
  meta?: any;
}

// Paginated response wrapper
export interface PaginatedResponse<T> {
  data: T[];
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

// Teacher API service
export const teachersApi = {
  // Get all teachers with optional search parameters
  async getTeachers(params?: TeacherSearchParams): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 0) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/v1/teachers?${queryString}` : '/api/v1/teachers';
    
    return apiClient.get<ApiResponse<PaginatedResponse<TeacherListVO>>>(url);
  },

  // Get teacher by ID
  async getTeacherById(id: string): Promise<ApiResponse<Teacher>> {
    return apiClient.get<ApiResponse<Teacher>>(`/api/v1/teachers/${id}`);
  },

  // Get teacher detail with related information
  async getTeacherDetail(id: string): Promise<ApiResponse<TeacherDetailVO>> {
    return apiClient.get<ApiResponse<TeacherDetailVO>>(`/api/v1/teachers/${id}/detail`);
  },

  // Create a new teacher
  async createTeacher(teacher: CreateTeacherRequest): Promise<ApiResponse<Teacher>> {
    return apiClient.post<ApiResponse<Teacher>>('/api/v1/teachers', teacher);
  },

  // Update an existing teacher
  async updateTeacher(id: string, teacher: UpdateTeacherRequest): Promise<ApiResponse<Teacher>> {
    return apiClient.put<ApiResponse<Teacher>>(`/api/v1/teachers/${id}`, teacher);
  },

  // Delete a teacher
  async deleteTeacher(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(`/api/v1/teachers/${id}`);
  },

  // Toggle teacher status (active/inactive)
  async toggleTeacherStatus(id: string, isActive: boolean): Promise<ApiResponse<Teacher>> {
    return apiClient.patch<ApiResponse<Teacher>>(`/api/v1/teachers/${id}/toggle-status`, {
      is_active: isActive,
    });
  },

  // Check if employee ID is available (for creation)
  async checkEmployeeIdAvailability(employeeId: string): Promise<ApiResponse<{ is_available: boolean }>> {
    return apiClient.get<ApiResponse<{ is_available: boolean }>>(
      `/api/v1/teachers/check-employee-id/${encodeURIComponent(employeeId)}`
    );
  },

  // Get teachers by employment status
  async getTeachersByStatus(status: string, params?: Omit<TeacherSearchParams, 'employment_status'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, employment_status: status });
  },

  // Get teachers by subject group
  async getTeachersBySubjectGroup(subjectGroupId: number, params?: Omit<TeacherSearchParams, 'subject_group_id'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, subject_group_id: subjectGroupId });
  },

  // Get teachers by grade level
  async getTeachersByGradeLevel(gradeLevelId: number, params?: Omit<TeacherSearchParams, 'grade_level_id'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, grade_level_id: gradeLevelId });
  },

  // Get teachers who teach a specific subject
  async getTeachersBySubject(subject: string, params?: Omit<TeacherSearchParams, 'teaching_subject'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, teaching_subject: subject });
  },

  // Get active teachers only
  async getActiveTeachers(params?: Omit<TeacherSearchParams, 'is_active'>): Promise<ApiResponse<PaginatedResponse<TeacherListVO>>> {
    return this.getTeachers({ ...params, is_active: true });
  },

  // Get teachers count by employment status
  async getTeachersCountByStatus(): Promise<ApiResponse<Record<string, number>>> {
    return apiClient.get<ApiResponse<Record<string, number>>>('/api/v1/teachers/count/by-status');
  },

  // Get teachers count by subject group
  async getTeachersCountBySubjectGroup(): Promise<ApiResponse<Record<string, number>>> {
    return apiClient.get<ApiResponse<Record<string, number>>>('/api/v1/teachers/count/by-subject-group');
  },

  // Export teachers data
  async exportTeachers(params?: TeacherSearchParams): Promise<Blob> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 0) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/v1/teachers/export?${queryString}` : '/api/v1/teachers/export';
    
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });
    
    return response.data;
  },

  // Batch operations
  async batchUpdateTeachers(updates: Array<{ id: string; data: UpdateTeacherRequest }>): Promise<ApiResponse<Teacher[]>> {
    return apiClient.post<ApiResponse<Teacher[]>>('/api/v1/teachers/batch-update', { updates });
  },

  async batchDeleteTeachers(ids: string[]): Promise<ApiResponse<void>> {
    return apiClient.post<ApiResponse<void>>('/api/v1/teachers/batch-delete', { ids });
  },

  async batchToggleStatus(ids: string[], isActive: boolean): Promise<ApiResponse<Teacher[]>> {
    return apiClient.post<ApiResponse<Teacher[]>>('/api/v1/teachers/batch-toggle-status', { 
      ids, 
      is_active: isActive 
    });
  },
};

// 导出默认教师API
export default teachersApi;