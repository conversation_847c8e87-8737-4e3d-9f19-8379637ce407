import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power,
  PowerOff,
} from 'lucide-react';
import { TeacherTableProps } from '@/types/teacher';
import { cn } from '@/lib/utils';

const TeacherTable: React.FC<TeacherTableProps> = ({
  teachers,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  pagination,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case '在职':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case '试用期':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case '停职':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case '离职':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      case '退休':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getTitleColor = (title?: string) => {
    if (!title) return 'bg-gray-100 text-gray-600';
    
    const titleColors = {
      '教授': 'bg-red-100 text-red-800',
      '副教授': 'bg-orange-100 text-orange-800',
      '讲师': 'bg-yellow-100 text-yellow-800',
      '助教': 'bg-green-100 text-green-800',
      '高级教师': 'bg-purple-100 text-purple-800',
      '一级教师': 'bg-blue-100 text-blue-800',
      '二级教师': 'bg-indigo-100 text-indigo-800',
      '三级教师': 'bg-pink-100 text-pink-800',
    };
    return titleColors[title as keyof typeof titleColors] || 'bg-gray-100 text-gray-600';
  };

  // const formatSubjects = (subjects?: string[]) => {
  //   if (!subjects || subjects.length === 0) return '未设置';
  //   if (subjects.length <= 3) {
  //     return subjects.join('、');
  //   }
  //   return subjects.slice(0, 3).join('、') + '等';
  // };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>工号</TableHead>
                <TableHead>姓名</TableHead>
                <TableHead>联系电话</TableHead>
                <TableHead>在职状态</TableHead>
                <TableHead>职称</TableHead>
                <TableHead>任教学科</TableHead>
                <TableHead>班主任班级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 9 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (!teachers || teachers.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 text-lg mb-2">暂无教师数据</div>
        <div className="text-gray-400 text-sm">点击&#34;新增教师&#34;创建第一个教师档案</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border overflow-auto max-h-[70vh] relative">
        <Table className="min-w-full">
          {/* 固定表头 */}
          <TableHeader className="sticky top-0 bg-white z-10 shadow-sm">
            <TableRow>
              <TableHead className="sticky left-0 bg-white z-20 min-w-[120px] text-center">姓名</TableHead>
              <TableHead className="text-center min-w-[120px]">工号</TableHead>
              <TableHead className="text-center min-w-[150px]">工作单位</TableHead>
              <TableHead className="text-center min-w-[80px]">性别</TableHead>
              <TableHead className="text-center min-w-[120px]">出生日期</TableHead>
              <TableHead className="text-center min-w-[180px]">身份证号</TableHead>
              <TableHead className="text-center min-w-[120px]">联系电话</TableHead>
              <TableHead className="text-center min-w-[180px]">电子邮箱</TableHead>
              <TableHead className="text-center min-w-[100px]">在职状态</TableHead>
              <TableHead className="text-center min-w-[100px]">职称</TableHead>
              <TableHead className="text-center min-w-[120px]">入职日期</TableHead>
              <TableHead className="text-center min-w-[120px]">最高学历</TableHead>
              <TableHead className="text-center min-w-[150px]">毕业学校</TableHead>
              {/*<TableHead className="text-center min-w-[200px]">任教学科</TableHead>*/}
              <TableHead className="text-center min-w-[100px]">激活状态</TableHead>
              <TableHead className="sticky right-0 bg-white z-20 min-w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>

          {/* 表格内容 */}
          <TableBody>
            {teachers.map((teacher) => (
              <TableRow key={teacher.id} className="hover:bg-gray-50">
                {/* 固定首列 - 姓名 */}
                <TableCell className="sticky left-0 bg-white z-10 text-center">
                  <div className="flex items-center justify-center">
                    {teacher.name}
                  </div>
                </TableCell>

                <TableCell className="text-center">
                  <Badge variant="outline" className="text-xs">
                    {teacher.employee_id}
                  </Badge>
                </TableCell>

                <TableCell className="text-center">
                  {teacher.office_location || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.gender || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.date_of_birth || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.id_card_number || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.phone || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.email || '-'}
                </TableCell>

                <TableCell className="text-center">
                  <Badge 
                    variant="secondary"
                    className={cn("text-xs", getStatusColor(teacher.employment_status))}
                  >
                    {teacher.employment_status}
                  </Badge>
                </TableCell>
                
                <TableCell className="text-center">
                  {teacher.title ? (
                    <Badge 
                      variant="secondary"
                      className={cn("text-xs", getTitleColor(teacher.title))}
                    >
                      {teacher.title}
                    </Badge>
                  ) : (
                    <span className="text-gray-400 text-xs">未设置</span>
                  )}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.hire_date || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.highest_education || '-'}
                </TableCell>

                <TableCell className="text-center">
                  {teacher.graduation_school || '-'}
                </TableCell>


                {/*<TableCell className="text-center">*/}
                {/*  <div className="flex items-center justify-center">*/}
                {/*    <span className="text-sm truncate max-w-[180px]" title={teacher.teaching_subjects?.join('、') || ''}>*/}
                {/*      {formatSubjects(teacher.teaching_subjects)}*/}
                {/*    </span>*/}
                {/*  </div>*/}
                {/*</TableCell>*/}

                <TableCell className="text-center">
                  <Badge 
                    variant={teacher.is_active ? "default" : "secondary"}
                    className={cn(
                      "text-xs",
                      teacher.is_active 
                        ? "bg-green-100 text-green-800 hover:bg-green-200" 
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                  >
                    {teacher.is_active ? '启用' : '禁用'}
                  </Badge>
                </TableCell>
                
                <TableCell className="sticky right-0 bg-white z-10">
                  <div className="flex items-center justify-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        {/*<DropdownMenuItem */}
                        {/*  onSelect={() => onViewDetail(teacher)}*/}
                        {/*  className="cursor-pointer"*/}
                        {/*>*/}
                        {/*  <Eye className="mr-2 h-4 w-4" />*/}
                        {/*  查看详情*/}
                        {/*</DropdownMenuItem>*/}
                        
                        <DropdownMenuItem 
                          onClick={() => onEdit(teacher)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => onToggleStatus(teacher.id, !teacher.is_active)}
                          className="cursor-pointer"
                        >
                          {teacher.is_active ? (
                            <>
                              <PowerOff className="mr-2 h-4 w-4" />
                              禁用
                            </>
                          ) : (
                            <>
                              <Power className="mr-2 h-4 w-4" />
                              启用
                            </>
                          )}
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => onDelete(teacher.id)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.total > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>共 {pagination.total} 条记录</span>
            <div className="flex items-center space-x-2">
              <span>每页</span>
              <select
                className="border rounded px-2 py-1 text-sm"
                value={pagination.pageSize}
                onChange={e => pagination.onChange(1, Number(e.target.value))}
              >
                {[10, 20, 50, 100].map(size => (
                  <option key={size} value={size}>{size} 条/页</option>
                ))}
              </select>
              <span>跳转到</span>
              <input
                type="number"
                min={1}
                max={Math.ceil(pagination.total / pagination.pageSize)}
                defaultValue={pagination.current}
                onBlur={e => {
                  let page = Number(e.target.value);
                  if (page < 1) page = 1;
                  if (page > Math.ceil(pagination.total / pagination.pageSize)) {
                    page = Math.ceil(pagination.total / pagination.pageSize);
                  }
                  if (page !== pagination.current) {
                    pagination.onChange(page, pagination.pageSize);
                  }
                }}
                className="border rounded px-2 py-1 w-16 text-sm text-center"
              />
              <span>页</span>
            </div>
          </div>

          <div className="flex items-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (pagination.current > 1) {
                        pagination.onChange(pagination.current - 1, pagination.pageSize);
                      }
                    }}
                    className={pagination.current === 1 ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
                {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={page === pagination.current}
                      onClick={() => pagination.onChange(page, pagination.pageSize)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                        pagination.onChange(pagination.current + 1, pagination.pageSize);
                      }
                    }}
                    className={
                      pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                        ? 'pointer-events-none opacity-50'
                        : ''
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default TeacherTable;