import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Pagination } from '@/components/ui/pagination';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  User,
  Phone,
  Power,
  PowerOff,
  GraduationCap,
  Users
} from 'lucide-react';
import { TeacherTableProps, TeacherListVO } from '@/types/teacher';
import { cn } from '@/lib/utils';

const TeacherTable: React.FC<TeacherTableProps> = ({
  teachers,
  loading,
  onEdit,
  onDelete,
  onViewDetail,
  onToggleStatus,
  pagination,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case '在职':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case '试用期':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case '停职':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case '离职':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      case '退休':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getTitleColor = (title?: string) => {
    if (!title) return 'bg-gray-100 text-gray-600';
    
    const titleColors = {
      '教授': 'bg-red-100 text-red-800',
      '副教授': 'bg-orange-100 text-orange-800',
      '讲师': 'bg-yellow-100 text-yellow-800',
      '助教': 'bg-green-100 text-green-800',
      '高级教师': 'bg-purple-100 text-purple-800',
      '一级教师': 'bg-blue-100 text-blue-800',
      '二级教师': 'bg-indigo-100 text-indigo-800',
      '三级教师': 'bg-pink-100 text-pink-800',
    };
    return titleColors[title as keyof typeof titleColors] || 'bg-gray-100 text-gray-600';
  };

  const formatSubjects = (subjects?: string[]) => {
    if (!subjects || subjects.length === 0) return '未设置';
    if (subjects.length <= 3) {
      return subjects.join('、');
    }
    return subjects.slice(0, 3).join('、') + '等';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>工号</TableHead>
                <TableHead>姓名</TableHead>
                <TableHead>联系电话</TableHead>
                <TableHead>在职状态</TableHead>
                <TableHead>职称</TableHead>
                <TableHead>任教学科</TableHead>
                <TableHead>班主任班级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 9 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (teachers.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 text-lg mb-2">暂无教师数据</div>
        <div className="text-gray-400 text-sm">点击"新增教师"创建第一个教师档案</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[120px]">工号</TableHead>
              <TableHead className="w-[100px]">姓名</TableHead>
              <TableHead className="w-[120px]">联系电话</TableHead>
              <TableHead className="w-[80px] text-center">在职状态</TableHead>
              <TableHead className="w-[80px] text-center">职称</TableHead>
              <TableHead className="w-[150px]">任教学科</TableHead>
              <TableHead className="w-[120px]">班主任班级</TableHead>
              <TableHead className="w-[80px] text-center">状态</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {teachers.map((teacher) => (
              <TableRow key={teacher.id} className="hover:bg-gray-50">
                <TableCell className="font-mono">
                  <Badge variant="outline" className="text-xs">
                    {teacher.employee_id}
                  </Badge>
                </TableCell>
                
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-2 text-gray-400" />
                    {teacher.name}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    {teacher.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="w-3 h-3 mr-1" />
                        {teacher.phone}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell className="text-center">
                  <Badge 
                    variant="secondary"
                    className={cn("text-xs", getStatusColor(teacher.employment_status))}
                  >
                    {teacher.employment_status}
                  </Badge>
                </TableCell>
                
                <TableCell className="text-center">
                  {teacher.title ? (
                    <Badge 
                      variant="secondary"
                      className={cn("text-xs", getTitleColor(teacher.title))}
                    >
                      {teacher.title}
                    </Badge>
                  ) : (
                    <span className="text-gray-400 text-xs">未设置</span>
                  )}
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center">
                    <GraduationCap className="w-4 h-4 mr-1 text-gray-400" />
                    <span className="text-sm truncate max-w-[130px]" title={teacher.teaching_subjects?.join('、')}>
                      {formatSubjects(teacher.teaching_subjects)}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center">
                    {teacher.homeroom_class_name ? (
                      <>
                        <Users className="w-4 h-4 mr-1 text-gray-400" />
                        <Badge variant="outline" className="text-xs">
                          {teacher.homeroom_class_name}
                        </Badge>
                      </>
                    ) : (
                      <span className="text-gray-400 text-sm">未担任</span>
                    )}
                  </div>
                </TableCell>
                
                <TableCell className="text-center">
                  <Badge 
                    variant={teacher.is_active ? "default" : "secondary"}
                    className={cn(
                      "text-xs",
                      teacher.is_active 
                        ? "bg-green-100 text-green-800 hover:bg-green-200" 
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                  >
                    {teacher.is_active ? '启用' : '禁用'}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center justify-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem 
                          onClick={() => onViewDetail(teacher)}
                          className="cursor-pointer"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => onEdit(teacher)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => onToggleStatus(teacher.id, !teacher.is_active)}
                          className="cursor-pointer"
                        >
                          {teacher.is_active ? (
                            <>
                              <PowerOff className="mr-2 h-4 w-4" />
                              禁用
                            </>
                          ) : (
                            <>
                              <Power className="mr-2 h-4 w-4" />
                              启用
                            </>
                          )}
                        </DropdownMenuItem>
                        
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem 
                          onClick={() => onDelete(teacher.id)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.total > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            显示第 {((pagination.current - 1) * pagination.pageSize) + 1} - {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条记录
          </div>
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={pagination.onChange}
            showSizeChanger={true}
            showQuickJumper={true}
            size="sm"
          />
        </div>
      )}
    </div>
  );
};

export default TeacherTable;