use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// 权限数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Permission {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub resource: String,      // 资源类型
    pub action: String,        // 操作类型
    pub scope: String,         // 权限范围
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 角色权限关联
#[derive(Debug, Clone, FromRow)]
pub struct RolePermission {
    pub id: Uuid,
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub created_at: DateTime<Utc>,
}

/// 权限查询参数
#[derive(Debug, Deserialize)]
pub struct PermissionQueryParams {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub search: Option<String>,
    pub resource: Option<String>,
    pub action: Option<String>,
}

/// 权限树节点（按资源分组）
#[derive(Debug, Serialize)]
pub struct PermissionTreeNode {
    pub resource: String,
    pub permissions: Vec<Permission>,
}

/// 权限检查请求
#[derive(Debug, Deserialize)]
pub struct CheckPermissionRequest {
    pub resource: String,
    pub action: String,
}

/// 权限检查响应
#[derive(Debug, Serialize)]
pub struct CheckPermissionResponse {
    pub has_permission: bool,
    pub reason: Option<String>,
}