import { ApiResponse } from '@/types';
import apiClient from './apiClient';

// 批次图片类型
export interface BatchImage {
  id: string;
  url: string;
  page_number: number;
  student_id?: string;
  student_name?: string;
  is_error?: boolean;
  status?: 'normal' | 'ocr_error' | 'blank' | 'duplicate' | 'pending';
  created_at: string;
  updated_at: string;
}

// 批次类型
export interface Batch {
  batch_no: string;
  page_count: number;
  scan_time: string;
  scanner: string;
  image_count: number;
  images: BatchImage[];
  status: 'processing' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
}

// 扫描统计类型
export interface ScanStats {
  total_scanned: number;
  pending_processing: number;
  processing_complete: number;
  exception_count: number;
}

// 学生搜索结果类型
export interface StudentSearchResult {
  id: string;
  student_id: string;
  name: string;
  class: string;
  grade: string;
}

// 批次查询参数
export interface BatchQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  status?: 'processing' | 'completed' | 'error';
  scanner?: string;
  start_date?: string;
  end_date?: string;
}

// 图片操作请求类型
export interface ImageOperationRequest {
  image_id: string;
  operation_type: 'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add';
  page_number?: number;
  student_id?: string;
  student_name?: string;
  is_error?: boolean;
  file?: File;
}

// 批次列表响应类型
export interface BatchListResponse {
  batches: Batch[];
  total: number;
  page: number;
  page_size: number;
}

// 扫描API
export const scanApi = {
  /**
   * 获取扫描统计数据
   */
  getScanStats: async (tenantId: string): Promise<ApiResponse<ScanStats>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/scan/stats`);
  },

  /**
   * 获取批次列表
   */
  getBatches: async (tenantId: string, params?: BatchQueryParams): Promise<ApiResponse<BatchListResponse>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/scan/batches`, { params });
  },

  /**
   * 获取批次详情
   */
  getBatchDetail: async (tenantId: string, batchNo: string): Promise<ApiResponse<Batch>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/scan/batches/${batchNo}`);
  },

  /**
   * 刷新批次数据
   */
  refreshBatches: async (tenantId: string): Promise<ApiResponse<BatchListResponse>> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/scan/batches/refresh`);
  },

  /**
   * 搜索学生信息
   */
  searchStudents: async (tenantId: string, query: string): Promise<ApiResponse<StudentSearchResult[]>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/scan/students/search`, {
      params: { q: query }
    });
  },

  /**
   * 上传图片
   */
  uploadImage: async (tenantId: string, batchNo: string, file: File, metadata?: {
    page_number?: number;
    student_id?: string;
    student_name?: string;
  }): Promise<ApiResponse<BatchImage>> => {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });
    }
    
    return apiClient.post(`/api/v1/tenant/${tenantId}/scan/batches/${batchNo}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * 替换图片
   */
  replaceImage: async (tenantId: string, imageId: string, file: File): Promise<ApiResponse<BatchImage>> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return apiClient.put(`/api/v1/tenant/${tenantId}/scan/images/${imageId}/replace`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * 更新图片页码
   */
  updateImagePage: async (tenantId: string, imageId: string, pageNumber: number): Promise<ApiResponse<BatchImage>> => {
    return apiClient.put(`/api/v1/tenant/${tenantId}/scan/images/${imageId}/page`, {
      page_number: pageNumber
    });
  },

  /**
   * 更新图片学生信息
   */
  updateImageStudent: async (tenantId: string, imageId: string, studentData: {
    student_id?: string;
    student_name?: string;
    is_error?: boolean;
  }): Promise<ApiResponse<BatchImage>> => {
    return apiClient.put(`/api/v1/tenant/${tenantId}/scan/images/${imageId}/student`, studentData);
  },

  /**
   * 删除图片
   */
  deleteImage: async (tenantId: string, imageId: string): Promise<ApiResponse<boolean>> => {
    return apiClient.delete(`/api/v1/tenant/${tenantId}/scan/images/${imageId}`);
  },

  /**
   * 获取图片详情
   */
  getImageDetail: async (tenantId: string, imageId: string): Promise<ApiResponse<BatchImage>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/scan/images/${imageId}`);
  },

  /**
   * 批量操作图片
   */
  batchOperateImages: async (tenantId: string, operations: ImageOperationRequest[]): Promise<ApiResponse<BatchImage[]>> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/scan/images/batch-operation`, {
      operations
    });
  },

  /**
   * 获取批次的页码选项
   */
  getBatchPageOptions: async (tenantId: string, batchNo: string): Promise<ApiResponse<number[]>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/scan/batches/${batchNo}/page-options`);
  },
};

export default scanApi;
