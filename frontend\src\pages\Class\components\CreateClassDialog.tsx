import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ClassType, Classes } from '@/types/classess';
import { MultiSelect, Option } from '@/components/ui/multi-select';
import { GradeLevelSummary } from '@/types/grade';
import { CreateAdministrativeClassesParams } from '@/types/administrativeClasses';

interface CreateClassDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    gradeLevels: GradeLevelSummary[];
    onCreate: (params: CreateAdministrativeClassesParams) => Promise<void>;
}

const CreateClassDialog: React.FC<CreateClassDialogProps> = ({ open, onOpenChange,  gradeLevels, onCreate }) => {
    // 本地表单状态
    const [classForm, setClassForm] = useState<CreateAdministrativeClassesParams>({
        class_name: '',
        class_code: '',
        academic_year: new Date().getFullYear(),
        grade_level_code: "",
        teacher_id: ''
    });
    //const [subjectCodes, setSubjectCodes] = useState<string[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [selectedGradeLevel, setSelectedGradeLevel] = useState<GradeLevelSummary | null>(null);

    const handleSubmit = async () => {
        if (!classForm.class_name || !classForm.class_code || !classForm.grade_level_code || !classForm.teacher_id || !classForm.academic_year) {
            setError('请填写所有必填项');
            return;
        }
        try {
            await onCreate(classForm);
            setClassForm({ ...classForm, class_name: '', class_code: '', grade_level_code: '', teacher_id: '', academic_year: new Date().getFullYear() });
            setError(null);
            onOpenChange(false);
        } catch {
            setError('创建班级失败');
        }
    };

    // ...表单渲染和学科多选逻辑同原来

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogTrigger asChild>
                <Button>新建班级</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>新建班级</DialogTitle>
                    <DialogDescription>请填写班级的基本信息</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">班级名称 *</Label>
                        <Input id="name" value={String(classForm.class_name)} onChange={e => setClassForm({ ...classForm, class_name: e.target.value })} placeholder="请输入班级名称" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="code">班级编号 *</Label>
                        <Input id="code" value={String(classForm.class_code)} onChange={e => setClassForm({ ...classForm, class_code: e.target.value })} placeholder="请输入班级编号" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="grade_level_code">年级 *</Label>
                        <Select value={String(classForm.grade_level_code)} onValueChange={value => setClassForm({ ...classForm, grade_level_code: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择年级" />
                            </SelectTrigger>
                            <SelectContent>
                                {gradeLevels.map(grade => (
                                    <SelectItem key={grade.id} value={grade.id}>{grade.name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className='space-y-2'>
                        <Label htmlFor="teacher_id">班主任 *</Label>
                        <Input id="teacher_id" value={String(classForm.teacher_id)} onChange={e => setClassForm({ ...classForm, teacher_id: e.target.value })} placeholder="请输入班主任" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="school_year">学年 *</Label>
                        <Input id="school_year" type="number" value={String(classForm.academic_year)} onChange={e => setClassForm({ ...classForm, academic_year: Number(e.target.value) })} placeholder="请输入学年" />
                    </div>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
                    <Button onClick={handleSubmit}>创建班级</Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default CreateClassDialog;