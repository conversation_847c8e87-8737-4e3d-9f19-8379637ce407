use crate::service::task_queue::{Status, TaskQueueState};
use axum::extract::{Path, State};
use axum::routing::{get, post};
use axum::{Json, Router};
use serde::Deserialize;
use crate::service::task_queue::grader_task::GraderTask;
use crate::service::task_queue::ocr_task::OcrTask;
use crate::service::task_queue::tracer_task::TracerTask;

pub fn create_router() -> Router<TaskQueueState> {
    Router::new()
        .route("/status/{queue_name}", get(status_handler))
        .route("/set_worker/{queue_name}", post(set_worker_handler))
        .route("/add_ocr_task", post(add_ocr_task_handler))
        .route("/add_grader_task", post(add_grader_task_handler))
        .route("/add_tracer_task", post(add_trace_task_handler))
}

async fn status_handler(State(state): State<TaskQueueState>, Path(queue_name): Path<String>) -> Json<Status> {
    let queue = match queue_name.as_str() {
        "ocr" => state.ocr,
        _ => state.grader
    };
    let data = queue.status();
    Json(data)
}
#[derive(Debug, Deserialize)]
struct SetWorkerRequest {
    size: usize,
}
async fn set_worker_handler(State(state): State<TaskQueueState>, Path(queue_name): Path<String>, Json(payload): Json<SetWorkerRequest>) -> Result<String, String> {
    let queue = match queue_name.as_str() {
        "ocr" => state.ocr,
        _ => state.grader
    };
    queue.set_worker_size(payload.size).await;
    Ok(format!("set_worker {}", payload.size))
}
async fn add_ocr_task_handler(State(state): State<TaskQueueState>, Json(payload): Json<OcrTask>) -> Result<String, String> {
    let queue = state.ocr;
    queue.add_task(payload).await.map_err(|e| e.to_string())?;
    Ok("add task success".to_string())
}
async fn add_grader_task_handler(State(state): State<TaskQueueState>, Json(payload): Json<GraderTask>) -> Result<String, String> {
    let queue = state.grader;
    queue.add_task(payload).await.map_err(|e| e.to_string())?;
    Ok("add task success".to_string())
}
async fn add_trace_task_handler(State(state): State<TaskQueueState>, Json(payload): Json<TracerTask>) -> Result<String, String> {
    let queue = state.tracer;
    queue.add_task(payload).await.map_err(|e| e.to_string())?;
    Ok("add task success".to_string())
}