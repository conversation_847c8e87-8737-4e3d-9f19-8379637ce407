use crate::service::grading::grading_service::GradingService;
use crate::web_server::AppState;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use uuid::Uuid;
use crate::model::grading::grading::{AIGradingRecordResponse, AnswerCardBlockResponse, CardBlockGradingRecordResponse, CreateAnswerCardBlocksRequest, GradingAssignmentListResponse, GradingAssignmentQueryParams, GradingAssignmentRequest, GradingAssignmentResponse, GradingControlRequest, GradingControlStateResponse, GradingRecordResponse, GradingStatisticsQueryParams, GradingStatisticsResponse, HandleScanExceptionRequest, PaperScanBatchRequest, PaperScanListResponse, PaperScanQueryParams, PaperScanResponse, ProcessAIGradingRequest, ScanExceptionResponse, SubmitCardBlockGradingRequest, SubmitGradingRecordRequest};
use crate::utils::api_response::ApiResponse;

pub async fn upload_paper_scans_handler(
    State(state): State<AppState>,
    Json(request): Json<PaperScanBatchRequest>,
) -> Result<Json<ApiResponse<Vec<PaperScanResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.upload_paper_scans(tenant_id, request).await {
        Ok(scans) => Ok(Json(ApiResponse::success(scans, None))),
        Err(e) => {
            eprintln!("Error uploading paper scans: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn get_paper_scan_handler(
    State(state): State<AppState>,
    Path(scan_id): Path<Uuid>,
) -> Result<Json<ApiResponse<PaperScanResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.get_paper_scan(tenant_id, scan_id).await {
        Ok(Some(scan)) => Ok(Json(ApiResponse::success(scan, None))),
        Ok(None) => Ok(Json(ApiResponse::error("Paper scan not found".to_string(), None))),
        Err(e) => {
            eprintln!("Error getting paper scan: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}   

pub async fn list_paper_scans_handler(
    State(state): State<AppState>,
    Query(params): Query<PaperScanQueryParams>,
) -> Result<Json<ApiResponse<PaperScanListResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.list_paper_scans(tenant_id, params).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error listing paper scans: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn create_answer_card_blocks_handler(
    State(state): State<AppState>,
    Json(request): Json<CreateAnswerCardBlocksRequest>,
) -> Result<Json<ApiResponse<Vec<AnswerCardBlockResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.create_answer_card_blocks(tenant_id, request).await {
        Ok(blocks) => Ok(Json(ApiResponse::success(blocks, None))),
        Err(e) => {
            eprintln!("Error creating answer card blocks: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn assign_grading_tasks_handler(
    State(state): State<AppState>,
    Json(request): Json<GradingAssignmentRequest>,
) -> Result<Json<ApiResponse<Vec<GradingAssignmentResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.assign_grading_tasks(tenant_id, request).await {
        Ok(assignments) => Ok(Json(ApiResponse::success(assignments, None))),
        Err(e) => {
            eprintln!("Error assigning grading tasks: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn get_grading_assignments_handler(
    State(state): State<AppState>,
    Query(params): Query<GradingAssignmentQueryParams>,
) -> Result<Json<ApiResponse<GradingAssignmentListResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.get_grading_assignments(tenant_id, params).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error getting grading assignments: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(),None)))
        }
    }
}

pub async fn submit_grading_record_handler(
    State(state): State<AppState>,
    Json(request): Json<SubmitGradingRecordRequest>,
) -> Result<Json<ApiResponse<GradingRecordResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.submit_grading_record(tenant_id, request).await {
        Ok(record) => Ok(Json(ApiResponse::success(record, None))),
        Err(e) => {
            eprintln!("Error submitting grading record: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn submit_card_block_grading_handler(
    State(state): State<AppState>,
    Json(request): Json<SubmitCardBlockGradingRequest>,
) -> Result<Json<ApiResponse<CardBlockGradingRecordResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.submit_card_block_grading(tenant_id, request).await {
        Ok(record) => Ok(Json(ApiResponse::success(record, None))),
        Err(e) => {
            eprintln!("Error submitting card block grading: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn process_ai_grading_handler(
    State(state): State<AppState>,
    Json(request): Json<ProcessAIGradingRequest>,
) -> Result<Json<ApiResponse<Vec<AIGradingRecordResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.process_ai_grading(tenant_id, request).await {
        Ok(records) => Ok(Json(ApiResponse::success(records, None))),
        Err(e) => {
            eprintln!("Error processing AI grading: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn control_grading_process_handler(
    State(state): State<AppState>,
    Json(request): Json<GradingControlRequest>,
) -> Result<Json<ApiResponse<GradingControlStateResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.control_grading_process(tenant_id, request).await {
        Ok(state) => Ok(Json(ApiResponse::success(state, None))),
        Err(e) => {
            eprintln!("Error controlling grading process: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn handle_scan_exception_handler(
    State(state): State<AppState>,
    Json(request): Json<HandleScanExceptionRequest>,
) -> Result<Json<ApiResponse<ScanExceptionResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.handle_scan_exception(tenant_id, request).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error handling scan exception: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn get_grading_statistics_handler(
    State(state): State<AppState>,
    Query(params): Query<GradingStatisticsQueryParams>,
) -> Result<Json<ApiResponse<GradingStatisticsResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.get_grading_statistics(tenant_id, params).await {
        Ok(stats) => Ok(Json(ApiResponse::success(stats, None))),
        Err(e) => {
            eprintln!("Error getting grading statistics: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}