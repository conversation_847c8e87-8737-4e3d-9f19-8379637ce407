#!/bin/bash
# 测试登录接口的脚本

# 从.env文件加载环境变量
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# 设置API基础URL
API_URL="http://localhost:3000/api/v1/auth"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}===== 测试登录接口 =====${NC}"

# 测试默认管理员登录
echo -e "\n${YELLOW}1. 测试默认管理员登录 (admin/admin123)${NC}"
admin_response=$(curl -s -X POST "${API_URL}/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo "响应: $admin_response"

# 提取token
admin_token=$(echo $admin_response | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$admin_token" ]; then
  echo -e "${GREEN}✓ 管理员登录成功，获取到token${NC}"
else
  echo -e "${RED}✗ 管理员登录失败${NC}"
fi

# 测试错误密码
echo -e "\n${YELLOW}2. 测试错误密码${NC}"
wrong_password_response=$(curl -s -X POST "${API_URL}/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "wrongpassword"
  }')

echo "响应: $wrong_password_response"

if echo $wrong_password_response | grep -q "INVALID_CREDENTIALS"; then
  echo -e "${GREEN}✓ 错误密码测试通过，返回了正确的错误信息${NC}"
else
  echo -e "${RED}✗ 错误密码测试失败${NC}"
fi

# 测试不存在的用户
echo -e "\n${YELLOW}3. 测试不存在的用户${NC}"
nonexistent_user_response=$(curl -s -X POST "${API_URL}/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "nonexistent_user",
    "password": "anypassword"
  }')

echo "响应: $nonexistent_user_response"

if echo $nonexistent_user_response | grep -q "USER_NOT_FOUND"; then
  echo -e "${GREEN}✓ 不存在用户测试通过，返回了正确的错误信息${NC}"
else
  echo -e "${RED}✗ 不存在用户测试失败${NC}"
fi

# 如果获取到了token，测试获取当前用户信息
if [ -n "$admin_token" ]; then
  echo -e "\n${YELLOW}4. 测试获取当前用户信息${NC}"
  me_response=$(curl -s -X GET "${API_URL}/me" \
    -H "Authorization: Bearer $admin_token")
  
  echo "响应: $me_response"
  
  if echo $me_response | grep -q "user_id"; then
    echo -e "${GREEN}✓ 获取当前用户信息成功${NC}"
  else
    echo -e "${RED}✗ 获取当前用户信息失败${NC}"
  fi
  
  # 测试刷新token
  echo -e "\n${YELLOW}5. 测试刷新token${NC}"
  refresh_token=$(echo $admin_response | grep -o '"refresh_token":"[^"]*"' | cut -d'"' -f4)
  
  if [ -n "$refresh_token" ]; then
    refresh_response=$(curl -s -X POST "${API_URL}/refresh" \
      -H "Content-Type: application/json" \
      -d "{
        \"refresh_token\": \"$refresh_token\"
      }")
    
    echo "响应: $refresh_response"
    
    if echo $refresh_response | grep -q "access_token"; then
      echo -e "${GREEN}✓ 刷新token成功${NC}"
    else
      echo -e "${RED}✗ 刷新token失败${NC}"
    fi
  else
    echo -e "${RED}✗ 未获取到refresh_token，无法测试刷新token${NC}"
  fi
  
  # 测试登出
  echo -e "\n${YELLOW}6. 测试登出${NC}"
  logout_response=$(curl -s -X POST "${API_URL}/logout" \
    -H "Authorization: Bearer $admin_token")
  
  echo "响应: $logout_response"
  
  if echo $logout_response | grep -q "success\":true"; then
    echo -e "${GREEN}✓ 登出成功${NC}"
  else
    echo -e "${RED}✗ 登出失败${NC}"
  fi
fi

echo -e "\n${YELLOW}===== 测试完成 =====${NC}"