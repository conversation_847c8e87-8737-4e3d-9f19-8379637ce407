-- Migration: Migrate user_identities from public schema to tenant schemas
-- Migration: 20250125_migrate_user_identities_to_tenant_schema
-- 
-- Development migration: Drop public.user_identities table and create tenant-specific tables
-- following PRD 6.3.2 technical architecture specification.

-- =============================================
-- 1. Drop existing public.user_identities table (development cleanup)
-- =============================================

-- Drop related tables first
DROP TABLE IF EXISTS public.identity_binding_suggestions CASCADE;
DROP TABLE IF EXISTS public.user_identities CASCADE;