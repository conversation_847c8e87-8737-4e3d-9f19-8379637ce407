import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MultiSelect, Option } from '@/components/ui/multi-select';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ClassesApi, CreateClassParams, UpdateClassParams } from '@/services/classesApi';
import subjectApi from '@/services/subjectApi';
import { Edit, ListCollapse, RefreshCw, Trash2, Upload } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AdministrativeClassesApi } from '@/services/administrativeClassesApi';
import ClassStatisticsCards from './components/ClassStatisticsCards';
import CreateClassDialog from './components/CreateClassDialog';
import { GradeLevelSummary } from '@/types/grade';
import gradeApi from '@/services/gradeApi';
import { AdministrativeClasses, AdministrativeClassesDetail, CreateAdministrativeClassesParams } from '@/types/administrativeClasses';


const AdministrativeClassesPage: React.FC = () => {
  // State management
  const [classes, setClasses] = useState<AdministrativeClassesDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Statistics
  const [totalClasses, setTotalClasses] = useState(0);
  const [totalTeachers, setTotalTeachers] = useState(0);
  const [totalStudents, setTotalStudents] = useState(0);
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState<AdministrativeClassesDetail | null>(null);

  // Form state
  const [subjects, setSubjects] = useState<any[]>([]);
  const [gradeLevels, setGradeLevels] = useState<GradeLevelSummary[]>([]);
  const [classForm, setClassForm] = useState<CreateAdministrativeClassesParams>({
    class_name: '',
    class_code: '',
    academic_year: new Date().getFullYear(),
    grade_level_code: "",
    teacher_id: ''
  });
  const [selectedSubjects, setSelectedSubjects] = useState<Option[]>([]);
  // Get tenant ID from auth context (mock for now)
  const tenantId = 'tenant_zhanghan';
  useEffect(() => {
    loadInitialData();
    //示例方法
    // AdministrativeClassesApi.getStatistics(tenantId).then(res => {
    //   const { success, data, message } = res
    //   if (!success) {
    //     return setError(message)
    //   }
    //   console.log("getStatistics:", data);
    // })
    // AdministrativeClassesApi.getUserClassList(tenantId).then(res => {
    //   const { success, data, message } = res
    //   if (!success) {
    //     return setError(message)
    //   }
    //   console.log("getUserClassList:", data);
    //   setClasses(data || []);
    // })
    // AdministrativeClassesApi.createClasses(tenantId, {
    //   class_name: "二年级一班",
    //   class_code: "104",
    //   academic_year: 2025,
    //   grade_level_code: "G2",
    //   teacher_id: "00000000-0000-0000-0000-ffff00000000"
    // }).then(res => {
    //   const { success, data, message } = res
    //   if (!success) {
    //     return setError(message)
    //   }
    //   console.log("createClasses:", data);
    // })
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      //获取行政班列表
      AdministrativeClassesApi.getUserClassList(tenantId).then(res => {
        const { success, data, message } = res
        if (!success) {
          return setError(message)
        }
        console.log("getUserClassList:", data);
        setClasses(data || []);
      })
      //科目列表
      const subjectsData = await subjectApi.pageSubject({
        page: 1,
        page_size: 1000,
        order_direction: 'asc',
      });
      //年级列表
      const gradeLevelsData = await gradeApi.getGradeSummaries();
      //获取统计数据
      AdministrativeClassesApi.getStatistics(tenantId).then(stats => {
        const { success, data, message } = stats
        if (!success) {
          return setError(message)
        }
        console.log("getStatistics:", data);
        setTotalClasses(data?.total_classes || 0);
        setTotalTeachers(data?.total_teacher || 0);
        setTotalStudents(data?.total_students || 0);
      })
      setSubjects(Array.isArray(subjectsData.data) ? subjectsData.data : []);
      setGradeLevels(Array.isArray(gradeLevelsData.data) ? gradeLevelsData.data : []);
      setError(null);
    } catch (err) {
      setError('加载数据失败');
      setClasses([]);
    } finally {
      setLoading(false);
    }
  };
  //创建班级
  const handleCreateClass = async  () =>{
    try{
      AdministrativeClassesApi.createClasses(tenantId, {
        class_name: "二年级一班",
        class_code: "104",
        academic_year: 2025,
        grade_level_code: "G2",
        teacher_id: "00000000-0000-0000-0000-ffff00000000"
      }).then(res => {
        const { success, data, message } = res
        if (!success) {
          return setError(message)
        }
        console.log("createClasses:", data);
      })
      loadInitialData();
    } catch {
      setError('创建班级失败');
    }
  }

  // 编辑班级
  const handleUpdateClass = async () => {
    if (!selectedClass) return;
    try {
      await AdministrativeClassesApi.updateClasses(tenantId, {
        
      } as UpdateClassParams);
      setIsEditDialogOpen(false);
      setSelectedClass(null);
      resetForm();
      setSelectedSubjects([]);
      loadInitialData();
    } catch (err) {
      setError('编辑班级失败');
    }
  };

  // 删除班级
  const handleDeleteClass = async () => {
    if (!selectedClass) return;
    try {
      await fakeDeleteClass(String(selectedClass.id));
      setIsDeleteDialogOpen(false);
      setSelectedClass(null);
      loadInitialData();
    } catch (err) {
      setError('删除班级失败');
    }
  };

  //  删除班级
  const fakeDeleteClass = async (id: string) => {
    await ClassesApi.deleteClasses(tenantId, { id: id });
    return Promise.resolve();
  };

  // 批量导入班级（需补全接口）
  const handleBatchImport = async () => {
    // TODO: 实现批量导入功能
    alert('批量导入功能待实现');
  };

  const resetForm = () => {
    setClassForm({
      class_name: '',
      class_code: '',
      academic_year: new Date().getFullYear(),
      grade_level_code: "",
      teacher_id: ""
    });
    setSelectedSubjects([]);
  };

  const openViewDialog = (cls: String) => {
    // TODO: 查看班级详细信息
    setIsViewDialogOpen(true);
  };

  const openEditDialog = (cls: AdministrativeClassesDetail) => {
    setSelectedClass(cls);
    setClassForm({
      class_name: String(cls.class_name),
      class_code: String(cls.class_code),
      academic_year: Number(cls.academic_year),
      grade_level_code: String(cls.grade_level_code),
      teacher_id: String(cls.teacher_id),
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (cls: AdministrativeClassesDetail) => {
    setSelectedClass(cls);
    setIsDeleteDialogOpen(true);
  };


  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }
  const getOperationButtons = (cls: AdministrativeClassesDetail) => {
    return (
      <div className="flex gap-1">
        {/* <Button variant="ghost" size="sm" onClick={() => navigate(`/classesDetail/${cls.classes.id}`)}>
          <ListCollapse className="h-4 w-4" />
        </Button> */}
        <Button variant="ghost" size="sm" onClick={() => openEditDialog(cls)}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(cls)}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }
  //------------------------------------------------------------------组件部分
  function RefreshButton() {
    return <Button variant="outline" onClick={() => {
      loadInitialData()
    }}>
      <RefreshCw className="h-4 w-4 mr-2" />
      刷新
    </Button>
  }
  function BatchImportButton() {
    return <Button variant="outline" onClick={handleBatchImport}>
      <Upload className="h-4 w-4 mr-2" />
      批量导入
    </Button>
  }

  return (
    <div className="space-y-6">
      {error && (
        <AlertDialog>
          <AlertDialogDescription>{error}</AlertDialogDescription>
        </AlertDialog>
      )}
      {/* 顶部按钮栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">班级管理</h1>
          <p className="text-muted-foreground">创建、管理和监控班级</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton />
        </div>
      </div>
      {/* 统计卡片 */}
      <ClassStatisticsCards
        totalClasses={totalClasses}
        totalTeachers={totalTeachers}
        totalStudents={totalStudents}
      />
      {/* 班级列表 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>行政班列表</CardTitle>
            <CardDescription>当前共有 {classes.length} 个行政班</CardDescription>
          </div>
          <div className="flex gap-2">
            <CreateClassDialog
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
              gradeLevels={gradeLevels}
              onCreate={handleCreateClass}
            />
            <BatchImportButton />
          </div>
        </CardHeader>
        <CardContent>
          <Table className="w-full table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead>班级名称</TableHead>
                <TableHead>班级编号</TableHead>
                {/* <TableHead>班级类型</TableHead> */}
                <TableHead>班主任</TableHead>
                <TableHead>班级人数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
          </Table>
          <ScrollArea className="h-[300px] border-none mt-4">
            <Table className="w-full table-fixed ">
              <TableBody>
                {classes.map(cls => (
                  <TableRow key={cls.id as string}>
                    <TableCell className="font-medium">{cls.class_name}</TableCell>
                    <TableCell>{cls.class_code}</TableCell>
                    <TableCell>{cls.teacher_name || '-'}</TableCell>
                    <TableCell>{cls.total_student.toString() || '-'}</TableCell>
                    <TableCell>{getOperationButtons(cls)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>编辑班级</DialogTitle>
            <DialogDescription>修改班级信息</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_name">班级名称</Label>
              <Input id="edit_name" value={String(classForm.class_name)} onChange={e => setClassForm({ ...classForm, class_name: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_code">班级编号</Label>
              <Input id="edit_code" value={String(classForm.class_code)} onChange={e => setClassForm({ ...classForm, class_code: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_grade_level_code">年级</Label>
              <Select value={classForm.grade_level_code ? String(classForm.grade_level_code) : undefined} onValueChange={value => setClassForm({ ...classForm, grade_level_code: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="选择年级" />
                </SelectTrigger>
                <SelectContent>
                  {gradeLevels.map(grade => (
                    <SelectItem key={grade.id} value={grade.id}>{grade.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_teacher_id">班主任</Label>
              <Input id="edit_teacher_id" value={String(classForm.teacher_id)} onChange={e => setClassForm({ ...classForm, teacher_id: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_school_year">学年</Label>
              <Input id="edit_school_year" type="number" value={String(classForm.academic_year)} onChange={e => setClassForm({ ...classForm, academic_year: Number(e.target.value) })} />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleUpdateClass}>保存</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* 删除班级对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>你确定要删除班级 {selectedClass?.class_name} 吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
            <Button variant="destructive" onClick={handleDeleteClass}>删除</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdministrativeClassesPage;