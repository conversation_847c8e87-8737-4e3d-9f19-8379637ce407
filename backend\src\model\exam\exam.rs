use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Exam {
    pub id: Uuid,
    pub name: String,
    pub exam_type: String, // 'single' or 'joint'
    pub grade_level: String,
    pub exam_nature: String, // 'formal', 'mock', 'practice'
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub expected_collection_time: Option<DateTime<Utc>>,
    pub scan_start_time: Option<DateTime<Utc>>,
    pub grading_mode: String, // 'intelligent', 'manual_then_scan'
    pub quality_control: String, // 'single', 'double_blind'
    pub ai_confidence_threshold: Option<f32>,
    pub manual_review_ratio: Option<f32>,
    pub status: String, // 'draft', 'published', 'in_progress', 'completed'
    pub created_by: Uuid,
    pub tenant_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct ExamSubject {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub subject_id: Uuid,
    pub paper_template_id: Option<Uuid>,
    pub total_score: f32,
    pub pass_score: Option<f32>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ExamClass {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub class_id: Uuid,
    pub class_type: String, // 'administrative' or 'teaching'
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ExamStudent {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub class_id: Uuid,
    pub seat_number: Option<String>,
    pub is_absent: bool,
    pub absent_reason: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct JointExam {
    pub id: Uuid,
    pub main_exam_id: Uuid,
    pub organizer_tenant_id: Uuid,
    pub participant_tenant_id: Uuid,
    pub invitation_status: String, // 'pending', 'accepted', 'rejected'
    pub invitation_sent_at: DateTime<Utc>,
    pub responded_at: Option<DateTime<Utc>>,
    pub sync_status: String, // 'pending', 'synced', 'failed'
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GradingAssignment {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub grader_id: Uuid,
    pub subject_id: Uuid,
    pub question_id: Option<Uuid>,
    pub assignment_type: String, // 'manual', 'ai', 'review'
    pub workload: i32,
    pub completed_count: i32,
    pub status: String, // 'assigned', 'in_progress', 'completed'
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreateExamRequest {
    pub name: String,
    pub exam_type: String,
    pub grade_level: String,
    pub exam_nature: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub expected_collection_time: Option<DateTime<Utc>>,
    pub scan_start_time: Option<DateTime<Utc>>,
    pub grading_mode: String,
    pub quality_control: String,
    pub ai_confidence_threshold: Option<f32>,
    pub manual_review_ratio: Option<f32>,
    pub subjects: Vec<ExamSubjectRequest>,
    pub classes: Vec<ExamClassRequest>,
    pub selected_students: Option<Vec<Uuid>>,
}

#[derive(Debug, Deserialize)]
pub struct ExamSubjectRequest {
    pub subject_id: Uuid,
    pub paper_template_id: Option<Uuid>,
    pub total_score: f32,
    pub pass_score: Option<f32>,
}

#[derive(Debug, Deserialize)]
pub struct ExamClassRequest {
    pub class_id: Uuid,
    pub class_type: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateExamRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub expected_collection_time: Option<DateTime<Utc>>,
    pub scan_start_time: Option<DateTime<Utc>>,
    pub grading_mode: Option<String>,
    pub quality_control: Option<String>,
    pub ai_confidence_threshold: Option<f32>,
    pub manual_review_ratio: Option<f32>,
    pub status: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct JointExamInvitationRequest {
    pub participant_tenant_ids: Vec<Uuid>,
    pub exam_id: Uuid,
    pub invitation_message: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct JointExamResponseRequest {
    pub invitation_id: Uuid,
    pub response: String, // 'accept' or 'reject'
    pub response_message: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ExamResponse {
    pub id: Uuid,
    pub name: String,
    pub exam_type: String,
    pub grade_level: String,
    pub exam_nature: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub expected_collection_time: Option<DateTime<Utc>>,
    pub scan_start_time: Option<DateTime<Utc>>,
    pub grading_mode: String,
    pub quality_control: String,
    pub ai_confidence_threshold: Option<f32>,
    pub manual_review_ratio: Option<f32>,
    pub status: String,
    pub created_by: Uuid,
    pub tenant_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub subjects: Vec<ExamSubject>,
    pub classes: Vec<ExamClass>,
    pub student_count: i64,
    pub joint_exam_info: Option<Vec<JointExam>>,
}

#[derive(Debug, Serialize)]
pub struct ExamListResponse {
    pub exams: Vec<ExamResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
}

#[derive(Debug, Deserialize)]
pub struct ExamQueryParams {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub name: Option<String>,
    pub exam_type: Option<String>,
    pub grade_level: Option<String>,
    pub status: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct ExamStatistics {
    pub total_exams: i64,
    pub completed_exams: i64,
    pub in_progress_exams: i64,
    pub draft_exams: i64,
    pub total_students: i64,
    pub total_papers: i64,
    pub grading_progress: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamStatisticsResponse {
    pub exam_id: Uuid,
    pub total_participants: i64,
    pub completed_participants: i64,
    pub average_score: f32,
    pub highest_score: f32,
    pub lowest_score: f32,
    pub pass_rate: f32,
    pub score_distribution: Vec<ScoreDistribution>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScoreDistribution {
    pub score_range: String,
    pub count: i64,
    pub percentage: f32,
}