import React, { useState, useRef, useCallback, useEffect } from "react";

interface DraggableImageProps {
  src: string;
  alt: string;
  className?: string;
  zoomLevel: number;
  onZoomChange: (zoom: number) => void;
}

const DraggableImage: React.FC<DraggableImageProps> = ({
  src,
  alt,
  className = "",
  zoomLevel,
  onZoomChange,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理鼠标按下
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    },
    [position]
  );

  // 处理鼠标移动
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDragging) return;

      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      setPosition({ x: newX, y: newY });
    },
    [isDragging, dragStart]
  );

  // 处理鼠标释放
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 处理滚轮缩放
  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      const newZoom = Math.max(0.5, Math.min(3, zoomLevel + delta));
      onZoomChange(newZoom);
    },
    [zoomLevel, onZoomChange]
  );

  // 组件卸载时重置状态
  useEffect(() => {
    return () => {
      // 组件卸载时的清理工作
      setPosition({ x: 0, y: 0 });
      setIsDragging(false);
    };
  }, []);

  // 当src改变时重置位置（新图片时重置）
  useEffect(() => {
    setPosition({ x: 0, y: 0 });
    setIsDragging(false);
  }, [src]);

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      onWheel={handleWheel}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      style={{
        cursor: isDragging ? "grabbing" : zoomLevel > 1 ? "grab" : "default",
      }}
    >
      <img
        ref={imageRef}
        src={src}
        alt={alt}
        className="max-w-full max-h-full object-contain transition-transform select-none"
        style={{
          transform: `scale(${zoomLevel}) translate(${
            position.x / zoomLevel
          }px, ${position.y / zoomLevel}px)`,
          transformOrigin: "center center",
        }}
        onMouseDown={handleMouseDown}
        onDragStart={(e) => e.preventDefault()} // 防止默认拖拽行为
      />

      {/* 提示文本和重置按钮 */}
      {zoomLevel > 1 && (
        <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          拖拽移动图片
        </div>
      )}

    </div>
  );
};

export default DraggableImage;
