import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  User, 
  Phone, 
  Mail, 
  Calendar,
  GraduationCap,
  Users,
  MapPin,
  FileText,
  Loader2
} from 'lucide-react';
import { TeacherDetailProps } from '@/types/teacher';
import { cn } from '@/lib/utils';

const TeacherDetail: React.FC<TeacherDetailProps> = ({
  teacher,
  open,
  onClose,
  loading = false,
}) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '未设置';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '在职':
        return 'bg-green-100 text-green-800';
      case '试用期':
        return 'bg-blue-100 text-blue-800';
      case '停职':
        return 'bg-yellow-100 text-yellow-800';
      case '离职':
        return 'bg-gray-100 text-gray-800';
      case '退休':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTitleColor = (title?: string) => {
    if (!title) return 'bg-gray-100 text-gray-600';
    
    const titleColors = {
      '教授': 'bg-red-100 text-red-800',
      '副教授': 'bg-orange-100 text-orange-800',
      '讲师': 'bg-yellow-100 text-yellow-800',
      '助教': 'bg-green-100 text-green-800',
      '高级教师': 'bg-purple-100 text-purple-800',
      '一级教师': 'bg-blue-100 text-blue-800',
      '二级教师': 'bg-indigo-100 text-indigo-800',
      '三级教师': 'bg-pink-100 text-pink-800',
    };
    return titleColors[title as keyof typeof titleColors] || 'bg-gray-100 text-gray-600';
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!teacher) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px]">
          <div className="text-center py-8">
            <div className="text-gray-500">教师信息加载失败</div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            教师详情 - {teacher.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <User className="w-5 h-5 mr-2" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">工号：</span>
                    <Badge variant="outline">{teacher.employee_id}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">姓名：</span>
                    <span className="font-medium">{teacher.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">性别：</span>
                    <span>{teacher.gender || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">出生日期：</span>
                    <span>{formatDate(teacher.date_of_birth)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">身份证号：</span>
                    <span className="font-mono text-sm">{teacher.id_card_number || '未设置'}</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">联系电话：</span>
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-1 text-gray-400" />
                      <span>{teacher.phone || '未设置'}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">邮箱：</span>
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 mr-1 text-gray-400" />
                      <span className="text-sm">{teacher.email || '未设置'}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">入职日期：</span>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                      <span>{formatDate(teacher.hire_date)}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">在职状态：</span>
                    <Badge className={cn("text-xs", getStatusColor(teacher.employment_status))}>
                      {teacher.employment_status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">账户状态：</span>
                    <Badge variant={teacher.is_active ? "default" : "secondary"}>
                      {teacher.is_active ? '启用' : '禁用'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Professional Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <GraduationCap className="w-5 h-5 mr-2" />
                职业信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">职称：</span>
                    {teacher.title ? (
                      <Badge className={cn("text-xs", getTitleColor(teacher.title))}>
                        {teacher.title}
                      </Badge>
                    ) : (
                      <span className="text-gray-400">未设置</span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">最高学历：</span>
                    <span>{teacher.highest_education || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">毕业院校：</span>
                    <span>{teacher.graduation_school || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">专业：</span>
                    <span>{teacher.major || '未设置'}</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">班主任班级：</span>
                    {teacher.homeroom_class_name ? (
                      <Badge variant="outline">{teacher.homeroom_class_name}</Badge>
                    ) : (
                      <span className="text-gray-400">未担任</span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">年级：</span>
                    <span>{teacher.grade_level_name || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">学科组：</span>
                    <span>{teacher.subject_group_name || '未设置'}</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-gray-600">办公地点：</span>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                      <span>{teacher.office_location || '未设置'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Teaching Subjects */}
          {teacher.teaching_subjects && teacher.teaching_subjects.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <FileText className="w-5 h-5 mr-2" />
                  任教学科
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {teacher.teaching_subjects.map((subject) => (
                    <Badge key={subject} variant="secondary" className="text-sm">
                      {subject}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bio */}
          {teacher.bio && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">个人简介</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 whitespace-pre-wrap">
                  {teacher.bio}
                </p>
              </CardContent>
            </Card>
          )}

          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">系统信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">创建时间：</span>
                  <span>{formatDate(teacher.created_at)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">更新时间：</span>
                  <span>{formatDate(teacher.updated_at)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TeacherDetail;