use std::sync::Arc;
use reqwest::Client;
use redis::{Client as RedisClient, AsyncCommands};
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use tracing::log;
use crate::utils::redis_client::RedisPool;
use super::grader_task::GraderTask;
use super::{grader_task, Task, REDIS_HASH_QUEUE_PREFIX, REDIS_TASK_QUEUE_PREFIX};


#[derive(Clone, Deserialize, Serialize)]
pub struct OcrTask {
    pub score_id: String,
    pub tenant: String,
    ocr_workflow: String,
    pub grade_workflow: String,
    urls: Vec<String>,
    pub question_content: Option<String>,
    pub standard_answer: String,
    pub score: f64,
}
#[derive(Serialize)]
struct Payload {
    #[serde(rename = "type")]
    name: String,
    images: Vec<ImagePayload>,
}
impl Payload {
    pub fn new(id: String, urls: &Vec<String>) -> Self {
        let images = urls.iter().map(|url| {
            ImagePayload { id: id.clone(), url: url.clone() }
        }).collect::<Vec<_>>();
        Payload {
            name: "image".to_string(),
            images,
        }
    }
}
#[derive(Serialize)]
struct ImagePayload {
    id: String,
    url: String,
}
impl Task for OcrTask {
    fn get_id(&self) -> (String, String) {
        (self.score_id.clone(), self.tenant.clone())
    }

    // 获取OCR结果，修改内容，添加到score任务队列
    async fn execute(&mut self, client: Client, db: PgPool, redis_pool: RedisPool) -> Result<(), String> {
        let url = format!("http://192.168.4.9:3000/api/run_workflow_json/{}", self.ocr_workflow);
        let builder = client.post(url);
        let response = builder.json(&Payload::new(self.score_id.clone(), &self.urls)).send().await.map_err(|e| e.to_string())?;
        if !response.status().is_success() {
            return Err(response.text().await.map_err(|e| e.to_string())?);
        }
        let data = response.json::<serde_json::Value>().await.map_err(|e| e.to_string())?;
        let data = data.as_object().and_then(|d| d.get("results")).and_then(|v| v.as_array()).ok_or("results error")?;
        let contents: Vec<_> = data.into_iter().map(|choice| {
            let timeout = choice.get("timeout_feedback").and_then(|v| v.as_bool()).unwrap_or(false);
            let content = choice.get("content").and_then(|v| v.as_str()).unwrap_or("");
            (content, timeout)
        }).collect();
        let (content, timeout) = contents.iter().fold((String::new(), false), |mut acc, &s| {
            acc.0.push_str(s.0);
            acc.1 = acc.1 || s.1;
            acc
        });
        log::info!("{}", content);
        if !timeout {
            let grader_task = GraderTask::from_ocr_task_and_answer(self.clone(), content);
            let queue_key = format!("{}{}{}", REDIS_TASK_QUEUE_PREFIX, super::GRADER_KEY, self.tenant);
            let hash_key = format!("{}{}", REDIS_HASH_QUEUE_PREFIX, super::GRADER_KEY);
            let task_json = serde_json::to_string(&grader_task).map_err(|e| e.to_string())?;
            let mut conn = redis_pool.get().await.map_err(|e| e.to_string())?;
            conn.rpush(queue_key, self.score_id.clone()).await.map_err(|e| e.to_string())?;
            conn.hset(hash_key, self.score_id.clone(), task_json).await.map_err(|e| e.to_string())?;
        } else {
            // TODO 更新数据库 ocr失败
        }
        Ok(())
    }
}