{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"any\", \"bigdecimal\", \"chrono\", \"default\", \"derive\", \"ipnetwork\", \"json\", \"macros\", \"migrate\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlx-macros\", \"sqlx-postgres\", \"tls-rustls-ring\", \"tls-rustls-ring-webpki\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 2241668132362809309, "path": 5377607611344627311, "deps": [[3276107248499827220, "sqlx_macros", false, 8949805199544868072], [3890005768372083955, "sqlx_postgres", false, 7959477906611451513], [10776111606377762245, "sqlx_core", false, 14492270989401568896]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-c24cfd30c19aca9f\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}