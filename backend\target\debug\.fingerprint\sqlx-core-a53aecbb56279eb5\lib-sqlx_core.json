{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"_tls-rustls-ring-webpki\", \"any\", \"bigdecimal\", \"chrono\", \"crc\", \"default\", \"ipnetwork\", \"json\", \"migrate\", \"offline\", \"rustls\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-native-certs\", \"serde\", \"serde_json\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 13907316459256012398, "path": 3480538788890136120, "deps": [[5103565458935487, "futures_io", false, 11116556168572213635], [40386456601120721, "percent_encoding", false, 5790721750244072186], [788558663644978524, "crossbeam_queue", false, 10942872575570451], [1099106214093768284, "hashbrown", false, 14462020412429748787], [1162433738665300155, "crc", false, 3416034769855464140], [1303438375223863970, "hashlink", false, 4443055139974990347], [3150220818285335163, "url", false, 2391176627508818527], [3646857438214563691, "futures_intrusive", false, 6733012454172784439], [3666196340704888985, "smallvec", false, 2504562632125106072], [3722963349756955755, "once_cell", false, 16113133773885095981], [3796357749340587960, "ipnetwork", false, 18222268581993448532], [5986029879202738730, "log", false, 7908971307997031226], [6493259146304816786, "indexmap", false, 2253232489500946077], [7620660491849607393, "futures_core", false, 4917151281608340555], [8156804143951879168, "webpki_roots", false, 11784017332949466457], [8319709847752024821, "uuid", false, 758134225158171114], [8606274917505247608, "tracing", false, 14064863493795061905], [9061476533697426406, "event_listener", false, 10587043555908589729], [9538054652646069845, "tokio", false, 6155195535907693298], [9689903380558560274, "serde", false, 12799672664599880943], [9857275760291862238, "sha2", false, 9059138104002160914], [9897246384292347999, "chrono", false, 16553689631928166696], [10629569228670356391, "futures_util", false, 2747797314946402595], [10806645703491011684, "thiserror", false, 4148965604242105395], [12170264697963848012, "either", false, 18233587740899168208], [13077212702700853852, "base64", false, 11698229905019321626], [14619257664405537057, "rustls", false, 3153825069346841260], [14647456484942590313, "bigdecimal", false, 10765323667720961305], [15367738274754116744, "serde_json", false, 10257594307601572992], [15932120279885307830, "memchr", false, 7712901758548144593], [16066129441945555748, "bytes", false, 92163385925654338], [16973251432615581304, "tokio_stream", false, 13597153077512903041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-a53aecbb56279eb5\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}