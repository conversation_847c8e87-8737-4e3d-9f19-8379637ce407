import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { GradeLevelFormProps, GradeLevelFormData, GradeLevelFormErrors } from '@/types/grade';

const GradeForm: React.FC<GradeLevelFormProps> = ({
  grade,
  open,
  onClose,
  onSubmit,
  loading = false
}) => {
  const [formData, setFormData] = useState<GradeLevelFormData>({
    code: '',
    name: '',
    description: '',
    order_level: 1,
  });
  
  const [errors, setErrors] = useState<GradeLevelFormErrors>({});
  const [isActive, setIsActive] = useState(true);

  // Initialize form data when grade changes
  useEffect(() => {
    if (grade) {
      setFormData({
        id: grade.id,
        code: grade.code,
        name: grade.name,
        description: grade.description || '',
        order_level: grade.order_level,
      });
      setIsActive(grade.is_active);
    } else {
      setFormData({
        code: '',
        name: '',
        description: '',
        order_level: 1,
      });
      setIsActive(true);
    }
    setErrors({});
  }, [grade, open]);

  // Validate form data
  const validateForm = (): boolean => {
    const newErrors: GradeLevelFormErrors = {};

    if (!formData.code.trim()) {
      newErrors.code = '年级代码不能为空';
    } else if (formData.code.length > 20) {
      newErrors.code = '年级代码不能超过20个字符';
    }

    if (!formData.name.trim()) {
      newErrors.name = '年级名称不能为空';
    } else if (formData.name.length > 100) {
      newErrors.name = '年级名称不能超过100个字符';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = '描述不能超过500个字符';
    }

    if (formData.order_level < 0) {
      newErrors.order_level = '排序级别不能为负数';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const submitData = {
        ...formData,
        ...(grade ? { is_active: isActive } : {}),
      };
      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof GradeLevelFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {grade ? '编辑年级' : '新增年级'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Grade Code */}
          <div className="space-y-2">
            <Label htmlFor="code">年级代码 *</Label>
            <Input
              id="code"
              value={formData.code}
              onChange={(e) => handleInputChange('code', e.target.value)}
              placeholder="请输入年级代码，如：G1, G2..."
              disabled={loading || !!grade} // Disable editing code for existing grades
            />
            {errors.code && (
              <p className="text-sm text-red-600">{errors.code}</p>
            )}
          </div>

          {/* Grade Name */}
          <div className="space-y-2">
            <Label htmlFor="name">年级名称 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入年级名称，如：一年级、二年级..."
              disabled={loading}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Order Level */}
          <div className="space-y-2">
            <Label htmlFor="order_level">排序级别 *</Label>
            <Input
              id="order_level"
              type="number"
              min="0"
              value={formData.order_level}
              onChange={(e) => handleInputChange('order_level', parseInt(e.target.value) || 0)}
              placeholder="请输入排序级别"
              disabled={loading}
            />
            {errors.order_level && (
              <p className="text-sm text-red-600">{errors.order_level}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="请输入年级描述（可选）"
              rows={3}
              disabled={loading}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          {/* Status (only for editing) */}
          {grade && (
            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={isActive}
                onCheckedChange={setIsActive}
                disabled={loading}
              />
              <Label htmlFor="is_active">启用状态</Label>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? '保存中...' : (grade ? '更新' : '创建')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default GradeForm;