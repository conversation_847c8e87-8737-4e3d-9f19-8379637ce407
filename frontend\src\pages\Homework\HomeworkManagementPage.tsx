import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Calendar,
  Clock,
  Users,
  FileText,
  CheckCircle2,
  AlertCircle,
  Play,
  Square,
  Upload,
  RefreshCw,
  Eye
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import {
  homeworkApi,
  HomeworkResponse,
  HomeworkCreateRequest,
  HomeworkStatistics,
  HomeworkQueryParams,
  subjectsApi,
  gradeLevelsApi,
  GradeLevel,
} from '@/services/homeworkApi';
import { ClassesApi } from '@/services/classesApi';

/**
 * 作者：张瀚
 * 说明：作业管理页面（管理员版本）
 */
export const HomeworkManagementPage: React.FC = () => {
  const [statistics, setStatistics] = useState<HomeworkStatistics | undefined>(undefined);

  useEffect(() => {
    loadInitialData();
  }, []);

  /**
   * 作者：张瀚
   * 说明：加载初始数据
   */
  const loadInitialData = async () => {
    const [statsDataRes] = await Promise.all([
      homeworkApi.getStatistics(tenantId),
      // subjectsApi.getSubjects(),
      // gradeLevelsApi.getGradeLevels(),
    ]);
    console.log(statsDataRes);

    setStatistics(statsDataRes.data);
    // setSubjects(subjectsData);
    // setGradeLevels(gradeLevelsData);
  };
  //----------------------------------------------------以下部分AI生成内容待定
  // State management
  const [homeworks, setHomeworks] = useState<HomeworkResponse[]>([
    {
      id: 'mock-1',
      name: '期中作业1',
      homework_type: 'midterm',
      grade_level: '初一',
      homework_nature: 'formal',
      description: '这是一个mock的期中作业',
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 86400000).toISOString(),
      expected_collection_time: '',
      scan_start_time: '',
      grading_mode: 'intelligent',
      quality_control: 'single',
      ai_confidence_threshold: 0.95,
      manual_review_ratio: 0.05,
      status: 'draft',
      created_by: 'mock-user',
      tenant_id: 'mock-tenant',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      subjects: [
        {
          id: 'subj-1',
          homework_id: 'mock-1',
          subject_id: 'math',
          paper_template_id: 'paper-1',
          total_score: 100,
          pass_score: 60,
          created_at: new Date().toISOString()
        }
      ],
      classes: [
        {
          id: 'class-1',
          homework_id: 'mock-1',
          class_id: 'class-1',
          class_type: '行政班',
          created_at: new Date().toISOString()
        }
      ],
      student_count: 30
    }
  ]);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [gradeFilter, setGradeFilter] = useState('all');

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedHomework, setSelectedHomework] = useState<HomeworkResponse | null>(null);

  // Form states
  const [subjects, setSubjects] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [gradeLevels, setGradeLevels] = useState<GradeLevel[]>([]);

  // Create homework form
  const [homeworkForm, setHomeworkForm] = useState<HomeworkCreateRequest>({
    name: '',
    homework_type: '',
    grade_level: '',
    homework_nature: 'formal',
    description: '',
    start_time: '',
    end_time: '',
    expected_collection_time: '',
    scan_start_time: '',
    grading_mode: 'intelligent',
    quality_control: 'single',
    ai_confidence_threshold: 0.95,
    manual_review_ratio: 0.05,
    subjects: [],
    classes: [],
    selected_students: []
  });

  // Get tenant ID from auth context (mock for now)
  const tenantId = '00000000-0000-0000-0000-000000000001';
  const navigate = useNavigate();



  // Load homeworks when filters change
  useEffect(() => {
    // loadHomeworks();
  }, [currentPage, searchTerm, statusFilter, typeFilter, gradeFilter]);



  const loadHomeworks = async () => {
    try {
      const params: HomeworkQueryParams = {
        page: currentPage,
        page_size: 10,
        name: searchTerm || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
        homework_type: typeFilter === 'all' ? undefined : typeFilter,
        grade_level: gradeFilter === 'all' ? undefined : gradeFilter
      };

      const data = await homeworkApi.getHomeworks(tenantId, params);
      setHomeworks(data.homeworks);
      setTotalPages(Math.ceil(data.total / 10));
    } catch (err) {
      console.error('Error loading homeworks:', err);
    }
  };

  const handleCreateHomework = async () => {
    try {
      if (!homeworkForm.name || !homeworkForm.homework_type || !homeworkForm.grade_level) {
        console.error('Please fill in all required fields');
        return;
      }

      await homeworkApi.createHomework(tenantId, homeworkForm);
      setIsCreateDialogOpen(false);
      resetForm();
      loadHomeworks();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      console.error('Failed to create homework');
      console.error('Error creating homework:', err);
    }
  };

  const handleUpdateHomework = async () => {
    if (!selectedHomework) return;

    try {
      await homeworkApi.updateHomework(tenantId, selectedHomework.id, {
        name: homeworkForm.name,
        description: homeworkForm.description,
        start_time: homeworkForm.start_time,
        end_time: homeworkForm.end_time
      });
      setIsEditDialogOpen(false);
      setSelectedHomework(null);
      resetForm();
      loadHomeworks();
    } catch (err) {
      console.error('Failed to update homework');
      console.error('Error updating homework:', err);
    }
  };

  const handleDeleteHomework = async () => {
    if (!selectedHomework) return;

    try {
      await homeworkApi.deleteHomework(tenantId, selectedHomework.id);
      setIsDeleteDialogOpen(false);
      setSelectedHomework(null);
      loadHomeworks();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      console.error('Failed to delete homework');
      console.error('Error deleting homework:', err);
    }
  };

  const handlePublishHomework = async (homeworkId: string) => {
    try {
      await homeworkApi.publishHomework(tenantId, homeworkId);
      loadHomeworks();
    } catch (err) {
      console.error('Failed to publish homework');
      console.error('Error publishing homework:', err);
    }
  };

  const handleStartHomework = async (homeworkId: string) => {
    try {
      await homeworkApi.startHomework(tenantId, homeworkId);
      loadHomeworks();
    } catch (err) {
      console.error('Failed to start homework');
      console.error('Error starting homework:', err);
    }
  };

  const handleCompleteHomework = async (homeworkId: string) => {
    try {
      await homeworkApi.completeHomework(tenantId, homeworkId);
      loadHomeworks();
    } catch (err) {
      console.error('Failed to complete homework');
      console.error('Error completing homework:', err);
    }
  };

  const resetForm = () => {
    setHomeworkForm({
      name: '',
      homework_type: '',
      grade_level: '',
      homework_nature: 'formal',
      description: '',
      start_time: '',
      end_time: '',
      expected_collection_time: '',
      scan_start_time: '',
      grading_mode: 'intelligent',
      quality_control: 'single',
      ai_confidence_threshold: 0.95,
      manual_review_ratio: 0.05,
      subjects: [],
      classes: [],
      selected_students: []
    });
  };

  const openEditDialog = (homework: HomeworkResponse) => {
    setSelectedHomework(homework);
    setHomeworkForm({
      name: homework.name,
      homework_type: homework.homework_type,
      grade_level: homework.grade_level,
      homework_nature: homework.homework_nature,
      description: homework.description || '',
      start_time: homework.start_time,
      end_time: homework.end_time,
      expected_collection_time: homework.expected_collection_time || '',
      scan_start_time: homework.scan_start_time || '',
      grading_mode: homework.grading_mode,
      quality_control: homework.quality_control,
      ai_confidence_threshold: homework.ai_confidence_threshold || 0.95,
      manual_review_ratio: homework.manual_review_ratio || 0.05,
      subjects: homework.subjects.map(s => ({
        subject_id: s.subject_id,
        paper_template_id: s.paper_template_id,
        total_score: s.total_score,
        pass_score: s.pass_score
      })),
      classes: homework.classes.map(c => ({
        class_id: c.class_id,
        class_type: c.class_type
      })),
      selected_students: []
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (homework: HomeworkResponse) => {
    setSelectedHomework(homework);
    setIsDeleteDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: '草稿', icon: FileText },
      published: { variant: 'default' as const, label: '已发布', icon: CheckCircle2 },
      in_progress: { variant: 'outline' as const, label: '进行中', icon: Clock },
      completed: { variant: 'destructive' as const, label: '已完成', icon: Square }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getActionButtons = (homework: HomeworkResponse) => {
    return (
      <div className="flex gap-1">
        <Button variant="ghost" size="sm" onClick={() => openEditDialog(homework)}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => navigate(`/homeworks/${homework.id}`)}>
          <Eye className="h-4 w-4" />
        </Button>
        {homework.status === 'draft' && (
          <Button variant="ghost" size="sm" onClick={() => handlePublishHomework(homework.id)}>
            <Upload className="h-4 w-4" />
          </Button>
        )}
        {homework.status === 'published' && (
          <Button variant="ghost" size="sm" onClick={() => handleStartHomework(homework.id)}>
            <Play className="h-4 w-4" />
          </Button>
        )}
        {homework.status === 'in_progress' && (
          <Button variant="ghost" size="sm" onClick={() => handleCompleteHomework(homework.id)}>
            <Square className="h-4 w-4" />
          </Button>
        )}
        {homework.status === 'draft' && (
          <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(homework)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    );
  };

  //------------------------------------------------------------------组件部分
  function RefreshButton() {
    return <Button variant="outline" onClick={() => {
      loadInitialData()
      loadHomeworks()
    }}>
      <RefreshCw className="h-4 w-4 mr-2" />
      刷新
    </Button>
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">作业管理</h1>
          <p className="text-muted-foreground">创建、管理和监控作业</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton />
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建作业
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建作业</DialogTitle>
                <DialogDescription>
                  请填写作业的基本信息
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">作业名称 *</Label>
                    <Input
                      id="name"
                      value={homeworkForm.name}
                      onChange={(e) => setHomeworkForm({ ...homeworkForm, name: e.target.value })}
                      placeholder="请输入作业名称"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="homework_type">作业类型 *</Label>
                    <Select value={homeworkForm.homework_type} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, homework_type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择作业类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="midterm">期中作业</SelectItem>
                        <SelectItem value="final">期末作业</SelectItem>
                        <SelectItem value="unit">单元作业</SelectItem>
                        <SelectItem value="quiz">随堂练习</SelectItem>
                        <SelectItem value="mock">模拟作业</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="grade_level">年级 *</Label>
                    <Select value={homeworkForm.grade_level} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, grade_level: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        {gradeLevels.map(grade => (
                          <SelectItem key={grade.id} value={grade.name}>{grade.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="homework_nature">作业性质</Label>
                    <Select value={homeworkForm.homework_nature} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, homework_nature: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择作业性质" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="formal">正式作业</SelectItem>
                        <SelectItem value="mock">模拟作业</SelectItem>
                        <SelectItem value="practice">练习</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">作业说明</Label>
                  <Textarea
                    id="description"
                    value={homeworkForm.description}
                    onChange={(e) => setHomeworkForm({ ...homeworkForm, description: e.target.value })}
                    placeholder="请输入作业说明"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start_time">开始时间 *</Label>
                    <Input
                      id="start_time"
                      type="datetime-local"
                      value={homeworkForm.start_time}
                      onChange={(e) => setHomeworkForm({ ...homeworkForm, start_time: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end_time">结束时间 *</Label>
                    <Input
                      id="end_time"
                      type="datetime-local"
                      value={homeworkForm.end_time}
                      onChange={(e) => setHomeworkForm({ ...homeworkForm, end_time: e.target.value })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="grading_mode">评阅模式</Label>
                    <Select value={homeworkForm.grading_mode} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, grading_mode: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择评阅模式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="intelligent">智能评阅</SelectItem>
                        <SelectItem value="manual_then_scan">先阅后扫</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quality_control">质量控制</Label>
                    <Select value={homeworkForm.quality_control} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, quality_control: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择质量控制方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="single">单次评阅</SelectItem>
                        <SelectItem value="double_blind">双评机制</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {homeworkForm.grading_mode === 'intelligent' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="ai_confidence_threshold">AI置信度阈值</Label>
                      <Input
                        id="ai_confidence_threshold"
                        type="number"
                        min="0"
                        max="1"
                        step="0.01"
                        value={homeworkForm.ai_confidence_threshold}
                        onChange={(e) => setHomeworkForm({ ...homeworkForm, ai_confidence_threshold: parseFloat(e.target.value) })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="manual_review_ratio">人工复核比例</Label>
                      <Input
                        id="manual_review_ratio"
                        type="number"
                        min="0"
                        max="1"
                        step="0.01"
                        value={homeworkForm.manual_review_ratio}
                        onChange={(e) => setHomeworkForm({ ...homeworkForm, manual_review_ratio: parseFloat(e.target.value) })}
                      />
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateHomework}>
                  创建作业
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总作业数</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_homeworks || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中作业</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.in_progress_homeworks || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成作业</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.completed_homeworks || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总参与学生</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_students || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索作业..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="published">已发布</SelectItem>
            <SelectItem value="in_progress">进行中</SelectItem>
            <SelectItem value="completed">已完成</SelectItem>
          </SelectContent>
        </Select>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="midterm">期中作业</SelectItem>
            <SelectItem value="final">期末作业</SelectItem>
            <SelectItem value="unit">单元作业</SelectItem>
            <SelectItem value="quiz">随堂练习</SelectItem>
            <SelectItem value="mock">模拟作业</SelectItem>
          </SelectContent>
        </Select>
        <Select value={gradeFilter} onValueChange={setGradeFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部年级</SelectItem>
            {gradeLevels.map(grade => (
              <SelectItem key={grade.id} value={grade.name}>{grade.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Homework List */}
      <Card>
        <CardHeader>
          <CardTitle>作业列表</CardTitle>
          <CardDescription>
            当前共有 {homeworks.length} 份作业
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>作业名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>年级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>开始时间</TableHead>
                <TableHead>参与学生</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {homeworks.map((homework) => (
                <TableRow key={homework.id}>
                  <TableCell className="font-medium">{homework.name}</TableCell>
                  <TableCell>{homework.homework_type}</TableCell>
                  <TableCell>{homework.grade_level}</TableCell>
                  <TableCell>{getStatusBadge(homework.status)}</TableCell>
                  <TableCell>{new Date(homework.start_time).toLocaleDateString()}</TableCell>
                  <TableCell>{homework.student_count}人</TableCell>
                  <TableCell>{getActionButtons(homework)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(currentPage > 1 ? currentPage - 1 : 1);
                  }}
                />
              </PaginationItem>
              {[...Array(totalPages)].map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink
                    href="#"
                    isActive={currentPage === i + 1}
                    onClick={(e) => {
                      e.preventDefault();
                      setCurrentPage(i + 1);
                    }}
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(currentPage < totalPages ? currentPage + 1 : totalPages);
                  }}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>编辑作业</DialogTitle>
            <DialogDescription>
              修改作业信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_name">作业名称</Label>
              <Input
                id="edit_name"
                value={homeworkForm.name}
                onChange={(e) => setHomeworkForm({ ...homeworkForm, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_description">作业说明</Label>
              <Textarea
                id="edit_description"
                value={homeworkForm.description}
                onChange={(e) => setHomeworkForm({ ...homeworkForm, description: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_start_time">开始时间</Label>
                <Input
                  id="edit_start_time"
                  type="datetime-local"
                  value={homeworkForm.start_time}
                  onChange={(e) => setHomeworkForm({ ...homeworkForm, start_time: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_end_time">结束时间</Label>
                <Input
                  id="edit_end_time"
                  type="datetime-local"
                  value={homeworkForm.end_time}
                  onChange={(e) => setHomeworkForm({ ...homeworkForm, end_time: e.target.value })}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleUpdateHomework}>
              保存
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              你确定要删除作业 {selectedHomework?.name} 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteHomework}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
