use axum::{
    extract::{Extension, Path, Query, State},
    response::<PERSON><PERSON>,
    http::StatusCode,
    handler::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::base::PageParams;
use crate::utils::api_response::ApiResponse;
use crate::model::analysis::*;
use crate::service::analysis::analysis_service::AnalysisService;
use crate::web_server::AppState;
use crate::utils::jwt::Claims;
use anyhow::Context;
use crate::model::analysis::analysis::{ClassAnalysisResponse, CreateStudentProfileLevelRequest, CreateStudentProfileTagRequest, GenerateLearningRecordRequest, LearningRecord, QuestionAnalysisResponse, StudentAnalysisResponse, StudentProfileLevel, StudentProfileTag, UpdateStudentProfileLevelRequest};

// Helper function to get the analysis service
async fn get_analysis_service(state: &AppState, tenant_id: Uuid) -> anyhow::Result<AnalysisService> {
    Ok(AnalysisService::new(state.db.clone()))
}

/// 用户上下文信息
#[derive(Debug, Clone)]
pub struct UserContext {
    pub tenant_id: Uuid,
    pub user_id: Uuid,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
}

// Helper function to extract user context from claims
fn extract_user_context(claims: &Claims) -> Result<(Uuid, Uuid), StatusCode> {
    let tenant = claims.tenant.as_ref().ok_or_else(|| {
        eprintln!("Error: User not associated with any tenant");
        StatusCode::FORBIDDEN
    })?;

    let tenant_id = tenant.tenant_id;
    let user_id = claims.sub.parse().map_err(|_| {
        eprintln!("Error: Invalid user ID format");
        StatusCode::BAD_REQUEST
    })?;

    Ok((tenant_id, user_id))
}

// 更高级的用户上下文提取函数
fn extract_full_user_context(claims: &Claims) -> Result<UserContext, StatusCode> {
    let tenant = claims.tenant.as_ref().ok_or_else(|| {
        eprintln!("Error: User not associated with any tenant");
        StatusCode::FORBIDDEN
    })?;

    let user_id = claims.sub.parse().map_err(|_| {
        eprintln!("Error: Invalid user ID format");
        StatusCode::BAD_REQUEST
    })?;

    Ok(UserContext {
        tenant_id: tenant.tenant_id,
        user_id,
        roles: tenant.roles.clone(),
        permissions: tenant.permissions.clone(),
    })
}

#[axum::debug_handler]
pub async fn create_student_profile_level_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateStudentProfileLevelRequest>,
) -> Result<Json<ApiResponse<StudentProfileLevel>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, creator_id) = extract_user_context(&claims)?;

    let result = async {
        let service = AnalysisService::new(state.db);
        service.create_student_profile_level(tenant_id, creator_id, request).await
    }.await;

    match result {
        Ok(level) => Ok(Json(ApiResponse::success(level, None))),
        Err(e) => {
            eprintln!("Error creating student profile level: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

#[axum::debug_handler]
pub async fn update_student_profile_level_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(level_id): Path<Uuid>,
    Json(request): Json<UpdateStudentProfileLevelRequest>,
) -> Result<Json<ApiResponse<StudentProfileLevel>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, _user_id) = extract_user_context(&claims)?;

    let result = async {
        let service = get_analysis_service(&state, tenant_id).await?;
        service.update_student_profile_level(tenant_id, level_id, request).await
    }.await;

    match result {
        Ok(Some(level)) => Ok(Json(ApiResponse::success(level, None))),
        Ok(None) => Ok(Json(ApiResponse::error("Student profile level not found".to_string(), None))),
        Err(e) => {
            eprintln!("Error updating student profile level: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

#[axum::debug_handler]
pub async fn create_student_profile_tag_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<CreateStudentProfileTagRequest>,
) -> Result<Json<ApiResponse<StudentProfileTag>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, creator_id) = extract_user_context(&claims)?;

    let result = async {
        let service = get_analysis_service(&state, tenant_id).await?;
        service.create_student_profile_tag(tenant_id, creator_id, request).await
    }.await;

    match result {
        Ok(tag) => Ok(Json(ApiResponse::success(tag, None))),
        Err(e) => {
            eprintln!("Error creating student profile tag: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

#[axum::debug_handler]
pub async fn generate_learning_records_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(request): Json<GenerateLearningRecordRequest>,
) -> Result<Json<ApiResponse<Vec<LearningRecord>>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, _user_id) = extract_user_context(&claims)?;

    let result = async {
        let service = get_analysis_service(&state, tenant_id).await?;
        service.generate_learning_records(tenant_id, request).await
    }.await;

    match result {
        Ok(records) => Ok(Json(ApiResponse::success(records, None))),
        Err(e) => {
            eprintln!("Error generating learning records: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

#[axum::debug_handler]
pub async fn get_student_analysis_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path((student_id, exam_id)): Path<(Uuid, Uuid)>,
) -> Result<Json<ApiResponse<StudentAnalysisResponse>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, _user_id) = extract_user_context(&claims)?;

    let result = async {
        let service = get_analysis_service(&state, tenant_id).await?;
        service.get_student_analysis(tenant_id, student_id, exam_id).await
    }.await;

    match result {
        Ok(analysis) => Ok(Json(ApiResponse::success(analysis, None))),
        Err(e) => {
            eprintln!("Error getting student analysis: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

#[axum::debug_handler]
pub async fn get_class_analysis_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path((class_id, exam_id)): Path<(Uuid, Uuid)>,
) -> Result<Json<ApiResponse<ClassAnalysisResponse>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, _user_id) = extract_user_context(&claims)?;

    let result = async {
        let service = get_analysis_service(&state, tenant_id).await?;
        service.get_class_analysis(tenant_id, class_id, exam_id).await
    }.await;

    match result {
        Ok(analysis) => Ok(Json(ApiResponse::success(analysis, None))),
        Err(e) => {
            eprintln!("Error getting class analysis: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

#[axum::debug_handler]
pub async fn get_question_analysis_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path((question_id, exam_id)): Path<(Uuid, Uuid)>,
) -> Result<Json<ApiResponse<QuestionAnalysisResponse>>, StatusCode> {
    // 使用辅助函数优雅地提取用户上下文
    let (tenant_id, _user_id) = extract_user_context(&claims)?;

    let result = async {
        let service = get_analysis_service(&state, tenant_id).await?;
        service.get_question_analysis(tenant_id, question_id, exam_id).await
    }.await;

    match result {
        Ok(analysis) => Ok(Json(ApiResponse::success(analysis, None))),
        Err(e) => {
            eprintln!("Error getting question analysis: {:?}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}