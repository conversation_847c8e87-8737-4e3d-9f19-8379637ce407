use crate::service::paper::paper::PaperService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use std::{
    collections::{HashMap, HashSet},
    fs,
    io::Cursor,
    sync::Arc,
};

use crate::{
    middleware::auth_middleware::AuthExtractor,
    model::{
        base::PageParams,
        teaching_aids::textbooks::{
            CreateAuthorizationRequest, CreateChapterRequest, CreateTeachingAidRequest,
            ImportProgressResponse, TeachingAid, TeachingAidAuthorizationResponse,
            TeachingAidChapter, TeachingAidQuery, TeachingAidStatsResponse, Textbook,
            UpdateTeachingAidRequest,
        },
        textbooks::{
            ChapterJson, ImportTeachingAidRequest, TeachingAidChapter2, TeachingAidPopularSubject,
            TeachingAidRecentImport, TextbookJson,
        },
    },
    service::{
        grade::grade_service::GradeService,
        storage::{storage_service::UploadOptions, StorageService},
        subject::SubjectService,
    },
    utils::{
        api_response::{responses, ApiResponse},
        error::AppError,
    },
    web_server::AppState,
};

use crate::service::teaching_aids::teaching_aids_service::TeachingAidsService;
use axum::extract::multipart::MultipartError;
use axum::{
    extract::{Multipart, Path, Query, State},
    response::Json,
    routing::{delete, get, post, put},
    Router,
};
use axum::extract::DefaultBodyLimit;
use bytes::Bytes;
use chrono::Utc;
use tempfile::Builder as TempFileBuilder;
use tracing::{error, info};
use uuid::Uuid;
use walkdir::WalkDir;
use zip::ZipArchive;

pub struct TeachingAidsRouteState {
    pub teaching_aids_service: Arc<TeachingAidsService>,
    pub storage_service: Arc<dyn StorageService>,
    pub subject_service: SubjectService,
    pub grade_service: GradeService,
    pub paper_service: Arc<PaperService>,
    pub textbook_paper_service: Arc<TextbookPaperService>,
}

pub(crate) fn create_router(app_state: &AppState) -> Router {
    let state = Arc::new(TeachingAidsRouteState {
        teaching_aids_service: app_state.teaching_aids_service.clone(),
        storage_service: app_state.storage_service.clone(),
        subject_service: app_state.subject_service.clone(),
        grade_service: app_state.grade_service.clone(),
        paper_service: app_state.paper_service.clone(),
        textbook_paper_service: app_state.textbook_paper_service.clone(),
    });
    Router::new()
        .route("/textbook", post(create_textbook_handler))
        .route("/textbook/{id}", delete(delete_textbook_handler))
        .route("/textbook/{id}", put(update_textbook_handler))
        .route("/textbook/{id}", get(get_textbook_handler))
        .route("/textbook/list", get(list_teaching_aids_handler))
        .route(
            "/textbook/{textbook_id}/chapters",
            get(get_chapters_handler),
        )
        .route("/stats", get(get_teaching_aid_stats_handler))
        .route("/import/{id}", get(get_import_progress_handler))
        .route("/import/history", get(get_import_history_handler))
        .route("/import", post(import_from_zip_handler)
            .layer(DefaultBodyLimit::max(10 * 1024 * 1024)))
        .with_state(state)
}

pub async fn import_from_zip_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    AuthExtractor(auth_context): AuthExtractor,
    mut multipart: Multipart,
) -> Result<ApiResponse<ImportProgressResponse>, AppError> {
    let mut file_data = None;
    let mut metadata: Option<ImportTeachingAidRequest> = None;
    let mut file_name = String::new();

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        error!("Failed to read multipart file: {}", e.status());
        AppError::BadRequest("Failed to read multipart file".to_string())
    })? {
        let name = field.name().unwrap_or("").to_string();
        if name == "file" {
            file_name = field.file_name().unwrap_or("default_file_name").to_string();
            match field.bytes().await {
                Ok(bytes) => {
                    file_data = Some(bytes);
                }
                Err(err) => {
                    error!("Failed to read multipart file: {}", err);
                }
            }
        } else if name == "metadata" {
            let data = field.bytes().await.unwrap();
            metadata = Some(serde_json::from_slice(&data)?);
        }
    }

    let file_data = file_data
        .ok_or_else(|| AppError::BadRequest("Missing file in multipart request".to_string()))?;

    let mut archive = ZipArchive::new(Cursor::new(file_data)).map_err(|e| {
        error!("Failed to read zip archive: {}", e);
        AppError::BadRequest("Invalid ZIP file format".to_string())
    })?;

    // 创建一个临时目录
    let temp_dir = TempFileBuilder::new()
        .prefix("textbook-import-")
        .tempdir()
        .map_err(|e| {
            error!("Failed to create temp directory: {}", e);
            AppError::InternalServerError("Failed to create temporary directory".to_string())
        })?;
    let temp_path = temp_dir.path().to_path_buf();
    info!("Temporary directory created at: {:?}", temp_path);

    archive.extract(&temp_path).map_err(|e| {
        error!("Failed to extract zip file: {}", e);
        AppError::InternalServerError("Failed to extract ZIP file".to_string())
    })?;
    info!("Successfully extracted ZIP to temp directory.");

    let storage_service = &state.storage_service;
    let textbook_id = Uuid::new_v4();
    let mut image_url_map = HashMap::new();
    let images_path = temp_path.join("images");

    // 递归遍历目录并上传所有图片，保持目录结构
    if images_path.is_dir() {
        for entry in WalkDir::new(&images_path)
            .into_iter()
            .filter_map(Result::ok)
        {
            let path = entry.path();
            if path.is_file() {
                // 获取相对于根目录的路径（保留目录结构）
                let relative_path = path.strip_prefix(&images_path).unwrap();
                let full_filename = relative_path.to_str().unwrap();

                // 构建包含目录结构的完整文件名
                let filename = full_filename.replace("\\", "/"); // 确保路径分隔符统一

                let content = fs::read(path).unwrap();

                let upload_options = UploadOptions {
                    preserve_filename: true,
                    prefix: Some(format!("textbook-import/textbooks/{}/images", textbook_id)),
                };

                let file_info = storage_service
                    .upload(&filename, Bytes::from(content), upload_options)
                    .await
                    .map_err(|e| {
                        error!("Failed to upload image {}: {}", filename, e);
                        AppError::InternalServerError(format!(
                            "Failed to upload image: {}",
                            filename
                        ))
                    })?;

                // 使用完整文件名作为键（保留目录结构）
                image_url_map.insert(filename.to_string(), file_info.url);
            }
        }
    }
    info!("Successfully uploaded {} images.", image_url_map.len());

    let textbook_json_path = temp_path.join("textbook.json");
    let chapters_json_path = temp_path.join("chapters.json");

    let textbook_data: TextbookJson =
        serde_json::from_str(&fs::read_to_string(textbook_json_path).unwrap())?;
    let mut chapters_content_str = fs::read_to_string(chapters_json_path).unwrap();

    chapters_content_str = chapters_content_str.replace("/api/oss/images/", "");
    chapters_content_str = chapters_content_str.replace("api/oss/images/", "");
    for (original_name, new_url) in &image_url_map {
        chapters_content_str = chapters_content_str.replace(original_name, new_url);
    }

    let chapters_data: Vec<ChapterJson> = serde_json::from_str(&chapters_content_str)?;

    // 父子结构，需要重新设置
    let parent_id_set: HashSet<String> = chapters_data
        .into_iter()
        .filter_map(|cd| cd.parent_id)
        .collect();
    for parent_id in parent_id_set.into_iter() {
        chapters_content_str =
            chapters_content_str.replace(&parent_id, &Uuid::new_v4().to_string());
    }

    let chapters_data: Vec<ChapterJson> = serde_json::from_str(&chapters_content_str)?;

    info!("Successfully parsed and processed textbook and chapter JSON data.");

    let subject_id = state
        .subject_service
        .get_subject_by_code(&textbook_data.subject)
        .await
        .map(|op| op.map(|subject| subject.id))
        .unwrap_or(None);

    let grade_id = state
        .grade_service
        .get_grade_by_code(&textbook_data.grade)
        .await
        .map(|op| op.map(|grade| grade.id))
        .unwrap_or(None);

    let new_textbook = Textbook {
        id: textbook_id,
        title: textbook_data.title,
        subject_id,
        grade_level_id: grade_id,
        publisher: textbook_data.publisher,
        publication_year: textbook_data.publish_year,
        isbn: None,
        version: Some("1.0".to_string()),
        status: Some("published".to_string()),
        creator_id: Some(auth_context.user_id),
        created_at: Some(Utc::now()),
        updated_at: Some(Utc::now()),
    };

    let new_chapters: Vec<TeachingAidChapter2> = chapters_data
        .into_iter()
        .map(|c| {
            let mut parent_id = None;
            if let Some(p_id) = &c.parent_id {
                let uuid = Uuid::parse_str(p_id).unwrap();
                parent_id = Some(uuid);
            }
            return TeachingAidChapter2 {
                id: Uuid::parse_str(&c.id).unwrap_or_else(|_| Uuid::new_v4()),
                textbook_id,
                parent_id,
                chapter_number: c.sequence,
                title: c.title,
                description: None,
                content: Some(c.content),
                metadata: c.metadata,
                creator_id: Some(auth_context.user_id),
                created_at: Some(Utc::now()),
                updated_at: Some(Utc::now()),
            };
        })
        .collect();

    let answer_sheets_path = temp_path.join("answer_sheets");
    let mut answer_sheets = Vec::new();
    if answer_sheets_path.is_dir() {
        for entry in fs::read_dir(answer_sheets_path)
            .map_err(|e| AppError::InternalServerError(e.to_string()))?
        {
            let entry = entry.map_err(|e| AppError::InternalServerError(e.to_string()))?;
            let path = entry.path();

            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("json") {
                let file_content = fs::read_to_string(&path)
                    .map_err(|e| AppError::InternalServerError(e.to_string()))?;
                let answer_sheet_data: serde_json::Value = serde_json::from_str(&file_content)?;
                let title = answer_sheet_data["title"]
                    .as_str()
                    .unwrap_or_default()
                    .to_string();
                let content = answer_sheet_data["content"].clone();
                answer_sheets.push((title, content));
            }
        }
    }

    state
        .teaching_aids_service
        .create_textbook_with_chapters(new_textbook, new_chapters, answer_sheets)
        .await?;

    info!("Successfully committed textbook and chapter data to the database.");

    // 导入记录入库
    let response = ApiResponse::success(
        ImportProgressResponse {
            id: textbook_id,
            filename: file_name.to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
        Some("Import successful".to_string()),
    );

    Ok(response)
}

pub async fn create_textbook_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    AuthExtractor(auth_context): AuthExtractor,
    Json(request): Json<CreateTeachingAidRequest>,
) -> Result<ApiResponse<TeachingAid>, AppError> {
    // Convert request to Textbook
    let textbook = Textbook {
        id: Uuid::new_v4(),
        title: request.title,
        subject_id: request.subject,
        grade_level_id: request.grade_level,
        publisher: request.publisher,
        publication_year: None,
        isbn: None,
        version: request.version,
        status: Some("draft".to_string()),
        creator_id: Some(auth_context.user_id),
        created_at: Some(Utc::now()),
        updated_at: Some(Utc::now()),
    };

    match state.teaching_aids_service.create_textbook(textbook).await {
        Ok(textbook) => {
            let subject = state
                .subject_service
                .get_subject_by_id(textbook.subject_id.unwrap_or(Uuid::new_v4()))
                .await
                .map(|op| op.map(|subject| subject.name))
                .unwrap_or(None);

            let grade_level = state
                .grade_service
                .get_grade_by_id(textbook.grade_level_id.unwrap_or(Uuid::new_v4()))
                .await
                .map(|op| op.map(|grade| grade.name))
                .unwrap_or(None);

            let aid = TeachingAid {
                id: textbook.id,
                title: textbook.title,
                description: request.description,
                author: request.author,
                publisher: textbook.publisher,
                subject,
                grade_level,
                version: textbook.version,
                status: textbook.status,
                creator_id: textbook.creator_id,
                created_at: textbook.created_at,
                updated_at: textbook.updated_at,
            };
            Ok(responses::success(
                aid,
                Some("Textbook created successfully"),
            ))
        }
        Err(e) => {
            error!("Error creating textbook: {}", e);
            Err(AppError::InternalServerError(
                "Failed to create textbook".to_string(),
            ))
        }
    }
}

pub async fn get_textbook_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(id): Path<Uuid>,
) -> Result<ApiResponse<TeachingAid>, AppError> {
    match state.teaching_aids_service.get_textbook(id).await {
        Ok(textbook) => {
            let aid = TeachingAid {
                id: textbook.id,
                title: textbook.title,
                description: None, // Not available in textbook model
                author: None,      // Not available in textbook model
                publisher: textbook.publisher,
                subject: textbook.subject,
                grade_level: textbook.grade_level,
                version: textbook.version,
                status: textbook.status,
                creator_id: textbook.creator_id,
                created_at: textbook.created_at,
                updated_at: textbook.updated_at,
            };
            Ok(ApiResponse::success(aid, None))
        }
        Err(e) => {
            eprintln!("Error getting teaching aid: {}", e);
            Err(AppError::InternalServerError(
                "Failed to get textbook".to_string(),
            ))
        }
    }
}

pub async fn list_teaching_aids_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Query(params): Query<TeachingAidQuery>,
) -> Result<ApiResponse<Vec<TeachingAid>>, AppError> {
    let page_params = PageParams {
        page: Some(params.page.unwrap_or(1)),
        page_size: Some(params.page_size.unwrap_or(20)),
    };

    match state.teaching_aids_service.get_textbooks().await {
        Ok(textbooks) => {
            let aids: Vec<TeachingAid> = textbooks
                .into_iter()
                .map(|textbook| TeachingAid {
                    id: textbook.id,
                    title: textbook.title,
                    description: None,
                    author: None,
                    publisher: textbook.publisher,
                    subject: textbook.subject,
                    grade_level: textbook.grade_level,
                    version: textbook.version,
                    status: textbook.status,
                    creator_id: textbook.creator_id,
                    created_at: textbook.created_at,
                    updated_at: textbook.updated_at,
                })
                .collect();
            Ok(ApiResponse::success(aids, None))
        }
        Err(err) => {
            error!("Failed to list textbooks: {}", err);
            Err(AppError::InternalServerError(
                "Failed to list textbooks".to_string(),
            ))
        }
    }
}

pub async fn update_textbook_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(aid_id): Path<Uuid>,
    Json(request): Json<UpdateTeachingAidRequest>,
) -> Result<ApiResponse<TeachingAid>, AppError> {
    // Get existing textbook first
    match state.teaching_aids_service.get_textbook(aid_id).await {
        Ok(mut textbook) => {
            // Update fields if provided
            if let Some(title) = request.title {
                textbook.title = title;
            }
            if let Some(publisher) = request.publisher {
                textbook.publisher = Some(publisher);
            }
            if let Some(version) = request.version {
                textbook.version = Some(version);
            }
            if let Some(status) = request.status {
                textbook.status = Some(status);
            }
            // if let Some(author) = request.author {
            //     textbook.author = Some(author);
            // }
            // if let Some(description) = &request.description {
            //     textbook.description = Some(description.clone());
            // }
            if let Some(subject_id) = request.subject {
                textbook.subject_id = Some(subject_id);
            }
            if let Some(grade_level_id) = request.grade_level {
                textbook.grade_level_id = Some(grade_level_id);
            }

            textbook.updated_at = Some(chrono::Utc::now());

            match state
                .teaching_aids_service
                .update_textbook(textbook.id, textbook.clone().into())
                .await
            {
                Ok(_) => {
                    let aid = TeachingAid {
                        id: textbook.id,
                        title: textbook.title,
                        description: request.description,
                        author: None,
                        publisher: textbook.publisher,
                        subject: textbook.subject,
                        grade_level: textbook.grade_level,
                        version: textbook.version,
                        status: textbook.status,
                        creator_id: textbook.creator_id,
                        created_at: textbook.created_at,
                        updated_at: textbook.updated_at,
                    };
                    Ok(ApiResponse::success(aid, None))
                }
                Err(e) => {
                    eprintln!("Error updating textbook: {}", e);
                    Err(AppError::InternalServerError(
                        "Failed to update textbook".to_string(),
                    ))
                }
            }
        }
        Err(e) => {
            eprintln!("Error finding textbook: {}", e);
            Err(AppError::InternalServerError(
                "textbook not found".to_string(),
            ))
        }
    }
}

pub async fn delete_textbook_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(aid_id): Path<Uuid>,
) -> Result<ApiResponse<()>, AppError> {
    match state.teaching_aids_service.delete_textbook(aid_id).await {
        Ok(_) => Ok(ApiResponse::success((), None)),
        Err(e) => {
            eprintln!("Error deleting teaching aid: {}", e);
            Err(AppError::InternalServerError(
                "Failed to delete textbook".to_string(),
            ))
        }
    }
}

pub async fn create_chapter_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(aid_id): Path<Uuid>,
    Json(request): Json<CreateChapterRequest>,
) -> Result<ApiResponse<TeachingAidChapter>, AppError> {
    let chapter = TeachingAidChapter {
        id: Uuid::new_v4(),
        textbook_id: aid_id,
        chapter_number: request.chapter_number,
        title: request.title,
        content: request.content,
        knowledge_points: request.knowledge_points,
        created_at: chrono::Utc::now(),
    };

    // For now, just return the chapter as if it was created
    // TODO: Implement actual chapter creation in service
    Ok(ApiResponse::success(
        chapter,
        Some("Chapter created successfully".to_string()),
    ))
}

pub async fn get_chapters_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(textbook_id): Path<Uuid>,
) -> Result<ApiResponse<Vec<TeachingAidChapter2>>, AppError> {
    match state
        .teaching_aids_service
        .get_chapters_by_textbook_id(textbook_id)
        .await
    {
        Ok(chapters) => Ok(ApiResponse::success(chapters, None)),
        Err(err) => {
            error!(
                "Failed to get chapters, textbook_id is {}: {}",
                textbook_id, err
            );
            Err(AppError::InternalServerError(format!(
                "Failed to get chapters, textbook_id is {}",
                textbook_id
            )))
        }
    }
}

pub async fn get_teaching_aid_stats_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
) -> Result<ApiResponse<TeachingAidStatsResponse>, AppError> {
    let popular_subjects = vec![
        TeachingAidPopularSubject {
            subject: "物理".to_string(),
            count: 1,
        },
        TeachingAidPopularSubject {
            subject: "数学".to_string(),
            count: 1,
        },
    ];

    let recent_imports = vec![
        TeachingAidRecentImport {
            id: "1".to_string(),
            title: "xxx练习册".to_string(),
            status: "draft".to_string(),
            count: 1,
            imported_at: Utc::now(),
        },
        TeachingAidRecentImport {
            id: "2".to_string(),
            title: "yyy练习册".to_string(),
            status: "published".to_string(),
            count: 1,
            imported_at: Utc::now(),
        },
    ];

    let stats = TeachingAidStatsResponse {
        total_textbooks: 2,
        total_chapters: 10,
        total_exercises: 60,
        total_authorized_tenants: 1,
        popular_subjects: Some(popular_subjects),
        recent_imports: Some(recent_imports),
    };
    Ok(ApiResponse::success(stats, None))
}

pub async fn get_chapter_handler(
    State(_state): State<Arc<TeachingAidsRouteState>>,
    Path(chapter_id): Path<Uuid>,
) -> Result<ApiResponse<TeachingAidChapter>, AppError> {
    // Return mock chapter for now
    let chapter = TeachingAidChapter {
        id: chapter_id,
        textbook_id: Uuid::new_v4(),
        chapter_number: 1,
        title: "Mock Chapter".to_string(),
        content: Some("Mock content".to_string()),
        knowledge_points: Some(serde_json::json!({})),
        created_at: chrono::Utc::now(),
    };
    Ok(ApiResponse::success(chapter, None))
}

pub async fn delete_chapter_handler(
    State(_state): State<Arc<TeachingAidsRouteState>>,
    Path(_chapter_id): Path<Uuid>,
) -> Result<ApiResponse<()>, AppError> {
    // Mock deletion for now
    Ok(ApiResponse::success(
        (),
        Some("Chapter deleted successfully".to_string()),
    ))
}

pub async fn create_authorization_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Json(request): Json<CreateAuthorizationRequest>,
) -> Result<ApiResponse<TeachingAidAuthorizationResponse>, AppError> {
    // For now, just return success since the service method doesn't exist
    // TODO: Implement proper authorization creation
    let auth = TeachingAidAuthorizationResponse {
        id: Uuid::new_v4(),
        textbook_id: request.textbook_id,
        tenant_id: request.tenant_id,
        granted_by: Uuid::new_v4(),
        created_at: chrono::Utc::now(),
    };
    Ok(ApiResponse::success(auth, None))
}

pub async fn get_authorizations_handler(
    State(_state): State<Arc<TeachingAidsRouteState>>,
    Path(_aid_id): Path<Uuid>,
) -> Result<ApiResponse<Vec<TeachingAidAuthorizationResponse>>, AppError> {
    // Return empty list for now
    let auths: Vec<TeachingAidAuthorizationResponse> = vec![];
    Ok(ApiResponse::success(auths, None))
}

pub async fn delete_authorization_handler(
    State(_state): State<Arc<TeachingAidsRouteState>>,
    Path(_auth_id): Path<Uuid>,
) -> Result<ApiResponse<()>, AppError> {
    // Mock deletion for now
    Ok(ApiResponse::success(
        (),
        Some("Authorization deleted successfully".to_string()),
    ))
}

pub async fn get_import_progress_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Path(import_id): Path<Uuid>,
) -> Result<ApiResponse<ImportProgressResponse>, AppError> {
    let progress = ImportProgressResponse {
        id: import_id,
        filename: "mock_import.xlsx".to_string(),
        status: "completed".to_string(),
        total_records: 100,
        processed_records: 100,
        success_count: 95,
        error_count: 5,
        started_at: Utc::now(),
        completed_at: Some(Utc::now()),
    };
    Ok(ApiResponse::success(progress, None))
}

pub async fn get_import_history_handler(
    State(state): State<Arc<TeachingAidsRouteState>>,
    Query(_params): Query<PageParams>,
) -> Result<ApiResponse<Vec<ImportProgressResponse>>, AppError> {
    // Return empty history for now
    let history: Vec<ImportProgressResponse> = vec![
        ImportProgressResponse {
            id: Uuid::new_v4(),
            filename: "mock_import.xlsx".to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
        ImportProgressResponse {
            id: Uuid::new_v4(),
            filename: "mock_import.xlsx".to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
    ];
    Ok(ApiResponse::success(history, None))
}
