use std::sync::Arc;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    model::{
        base::PageParams,
        teaching_aids::textbooks::{
            CreateAuthorizationRequest, CreateChapterRequest, CreateTeachingAidRequest,
            ImportProgressResponse, TeachingAid, TeachingAidAuthorizationResponse,
            TeachingAidChapter, TeachingAidQuery, TeachingAidStatsResponse, Textbook,
            UpdateTeachingAidRequest,
        },
        textbooks::{TeachingAidPopularSubject, TeachingAidRecentImport},
    },
    service::teaching_aids::TeachingAidsService,
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Json},
    routing::{get, post},
    Router,
};
use chrono::Utc;
use uuid::Uuid;

pub(crate) fn create_router() -> Router<Arc<TeachingAidsService>> {
    Router::new()
        .route("/textbook/create", post(create_textbook_handler))
        .route("/textbook/list", get(list_teaching_aids_handler))
        .route("/stats", get(get_teaching_aid_stats_handler))
        .route("/import/{id}", get(get_import_progress_handler))
        .route("/import/history", get(get_import_history_handler))
}

pub async fn create_textbook_handler(
    State(service): State<Arc<TeachingAidsService>>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(request): Json<CreateTeachingAidRequest>,
) -> impl IntoResponse {
    // Convert request to Textbook
    let textbook = Textbook {
        id: Uuid::new_v4(),
        title: request.title,
        subject_id: request.subject_id,
        grade_level_id: request.grade_level_id,
        publisher: request.publisher,
        publication_year: None,
        isbn: None,
        version: request.version,
        status: Some("draft".to_string()),
        creator_id: Some(admin_context.user_id),
        created_at: Some(chrono::Utc::now()),
        updated_at: Some(chrono::Utc::now()),
    };

    match service.create_textbook(textbook).await {
        Ok(textbook) => {
            let aid = TeachingAid {
                id: textbook.id,
                title: textbook.title,
                description: request.description,
                author: request.author,
                publisher: textbook.publisher,
                subject_id: textbook.subject_id,
                grade_level_id: textbook.grade_level_id,
                version: textbook.version,
                status: textbook.status,
                creator_id: textbook.creator_id,
                created_at: textbook.created_at,
                updated_at: textbook.updated_at,
            };
            responses::success(aid, Some("Teaching aid created successfully"))
        }
        Err(e) => {
            eprintln!("Error creating teaching aid: {}", e);
            responses::error("Failed to create teaching aid", Some("INTERNAL_ERROR"))
        }
    }
}

pub async fn get_teaching_aid_handler(
    State(state): State<AppState>,
    Path(aid_id): Path<Uuid>,
) -> Result<Json<ApiResponse<TeachingAid>>, StatusCode> {
    let service = TeachingAidsService::new(state.db.clone());

    match service.get_textbook(aid_id).await {
        Ok(textbook) => {
            let aid = TeachingAid {
                id: textbook.id,
                title: textbook.title,
                description: None, // Not available in textbook model
                author: None,      // Not available in textbook model
                publisher: textbook.publisher,
                subject_id: textbook.subject_id,
                grade_level_id: textbook.grade_level_id,
                version: textbook.version,
                status: textbook.status,
                creator_id: textbook.creator_id,
                created_at: textbook.created_at,
                updated_at: textbook.updated_at,
            };
            Ok(Json(ApiResponse::success(aid, None)))
        }
        Err(e) => {
            eprintln!("Error getting teaching aid: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn list_teaching_aids_handler(
    State(service): State<Arc<TeachingAidsService>>,
    Query(params): Query<TeachingAidQuery>,
) -> Result<Json<ApiResponse<Vec<TeachingAid>>>, StatusCode> {
    let page_params = PageParams {
        page: Some(params.page.unwrap_or(1)),
        page_size: Some(params.page_size.unwrap_or(20)),
    };

    let textbooks = service.get_textbooks().await.unwrap();
    let aids: Vec<TeachingAid> = textbooks
        .into_iter()
        .map(|textbook| TeachingAid {
            id: textbook.id,
            title: textbook.title,
            description: None,
            author: None,
            publisher: textbook.publisher,
            subject_id: textbook.subject_id,
            grade_level_id: textbook.grade_level_id,
            version: textbook.version,
            status: textbook.status,
            creator_id: textbook.creator_id,
            created_at: textbook.created_at,
            updated_at: textbook.updated_at,
        })
        .collect();
    Ok(Json(ApiResponse::success(aids, None)))
}

pub async fn update_teaching_aid_handler(
    State(state): State<AppState>,
    Path(aid_id): Path<Uuid>,
    Json(request): Json<UpdateTeachingAidRequest>,
) -> Result<Json<ApiResponse<TeachingAid>>, StatusCode> {
    let service = TeachingAidsService::new(state.db.clone());

    // Get existing textbook first
    match service.get_textbook(aid_id).await {
        Ok(mut textbook) => {
            // Update fields if provided
            if let Some(title) = request.title {
                textbook.title = title;
            }
            if let Some(publisher) = request.publisher {
                textbook.publisher = Some(publisher);
            }
            if let Some(version) = request.version {
                textbook.version = Some(version);
            }
            if let Some(status) = request.status {
                textbook.status = Some(status);
            }
            textbook.updated_at = Some(chrono::Utc::now());

            match service.update_textbook(textbook.id, textbook.clone()).await {
                Ok(_) => {
                    let aid = TeachingAid {
                        id: textbook.id,
                        title: textbook.title,
                        description: request.description,
                        author: None,
                        publisher: textbook.publisher,
                        subject_id: textbook.subject_id,
                        grade_level_id: textbook.grade_level_id,
                        version: textbook.version,
                        status: textbook.status,
                        creator_id: textbook.creator_id,
                        created_at: textbook.created_at,
                        updated_at: textbook.updated_at,
                    };
                    Ok(Json(ApiResponse::success(aid, None)))
                }
                Err(e) => {
                    eprintln!("Error updating teaching aid: {}", e);
                    Ok(Json(ApiResponse::error(e.to_string(), None)))
                }
            }
        }
        Err(e) => {
            eprintln!("Error finding teaching aid: {}", e);
            Ok(Json(ApiResponse::error(
                "Teaching aid not found".to_string(),
                Some("NOT_FOUND".to_string()),
            )))
        }
    }
}

pub async fn delete_teaching_aid_handler(
    State(state): State<AppState>,
    Path(aid_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    let service = TeachingAidsService::new(state.db.clone());

    match service.delete_textbook(aid_id).await {
        Ok(_) => Ok(Json(ApiResponse::success((), None))),
        Err(e) => {
            eprintln!("Error deleting teaching aid: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

// Removed duplicate - using the one below

pub async fn create_chapter_handler(
    State(state): State<AppState>,
    Path(aid_id): Path<Uuid>,
    Json(request): Json<CreateChapterRequest>,
) -> Result<Json<ApiResponse<TeachingAidChapter>>, StatusCode> {
    let service = TeachingAidsService::new(state.db.clone());

    let chapter = TeachingAidChapter {
        id: Uuid::new_v4(),
        textbook_id: aid_id,
        chapter_number: request.chapter_number,
        title: request.title,
        content: request.content,
        knowledge_points: request.knowledge_points,
        created_at: chrono::Utc::now(),
    };

    // For now, just return the chapter as if it was created
    // TODO: Implement actual chapter creation in service
    Ok(Json(ApiResponse::success(
        chapter,
        Some("Chapter created successfully".to_string()),
    )))
}

pub async fn get_chapters_handler(
    State(_state): State<AppState>,
    Path(_aid_id): Path<Uuid>,
) -> Result<Json<ApiResponse<Vec<TeachingAidChapter>>>, StatusCode> {
    // For now, return empty list
    // TODO: Implement actual chapter retrieval
    let chapters: Vec<TeachingAidChapter> = vec![];
    Ok(Json(ApiResponse::success(chapters, None)))
}

pub async fn get_teaching_aid_stats_handler(
    State(service): State<Arc<TeachingAidsService>>,
) -> Result<Json<ApiResponse<TeachingAidStatsResponse>>, StatusCode> {
    let popular_subjects = vec![
        TeachingAidPopularSubject {
            subject: "物理".to_string(),
            count: 1,
        },
        TeachingAidPopularSubject {
            subject: "数学".to_string(),
            count: 1,
        },
    ];

    let recent_imports = vec![
        TeachingAidRecentImport {
            id: "1".to_string(),
            title: "xxx练习册".to_string(),
            status: "draft".to_string(),
            count: 1,
            imported_at: Utc::now(),
        },
        TeachingAidRecentImport {
            id: "2".to_string(),
            title: "yyy练习册".to_string(),
            status: "published".to_string(),
            count: 1,
            imported_at: Utc::now(),
        },
    ];

    let stats = TeachingAidStatsResponse {
        total_textbooks: 2,
        total_chapters: 10,
        total_exercises: 60,
        total_authorized_tenants: 1,
        popular_subjects: Some(popular_subjects),
        recent_imports: Some(recent_imports),
    };
    Ok(Json(ApiResponse::success(stats, None)))
}

pub async fn get_chapter_handler(
    State(_state): State<AppState>,
    Path(chapter_id): Path<Uuid>,
) -> Result<Json<ApiResponse<TeachingAidChapter>>, StatusCode> {
    // Return mock chapter for now
    let chapter = TeachingAidChapter {
        id: chapter_id,
        textbook_id: Uuid::new_v4(),
        chapter_number: 1,
        title: "Mock Chapter".to_string(),
        content: Some("Mock content".to_string()),
        knowledge_points: Some(serde_json::json!({})),
        created_at: chrono::Utc::now(),
    };
    Ok(Json(ApiResponse::success(chapter, None)))
}

pub async fn delete_chapter_handler(
    State(_state): State<AppState>,
    Path(_chapter_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // Mock deletion for now
    Ok(Json(ApiResponse::success(
        (),
        Some("Chapter deleted successfully".to_string()),
    )))
}

pub async fn create_authorization_handler(
    State(state): State<AppState>,
    Json(request): Json<CreateAuthorizationRequest>,
) -> Result<Json<ApiResponse<TeachingAidAuthorizationResponse>>, StatusCode> {
    let service = TeachingAidsService::new(state.db.clone());

    // For now, just return success since the service method doesn't exist
    // TODO: Implement proper authorization creation
    let auth = TeachingAidAuthorizationResponse {
        id: Uuid::new_v4(),
        textbook_id: request.textbook_id,
        tenant_id: request.tenant_id,
        granted_by: Uuid::new_v4(),
        created_at: chrono::Utc::now(),
    };
    Ok(Json(ApiResponse::success(auth, None)))
}

pub async fn get_authorizations_handler(
    State(_state): State<AppState>,
    Path(_aid_id): Path<Uuid>,
) -> Result<Json<ApiResponse<Vec<TeachingAidAuthorizationResponse>>>, StatusCode> {
    // Return empty list for now
    let auths: Vec<TeachingAidAuthorizationResponse> = vec![];
    Ok(Json(ApiResponse::success(auths, None)))
}

pub async fn delete_authorization_handler(
    State(_state): State<AppState>,
    Path(_auth_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // Mock deletion for now
    Ok(Json(ApiResponse::success(
        (),
        Some("Authorization deleted successfully".to_string()),
    )))
}

pub async fn get_import_progress_handler(
    State(service): State<Arc<TeachingAidsService>>,
    Path(import_id): Path<Uuid>,
) -> Result<Json<ApiResponse<ImportProgressResponse>>, StatusCode> {
    let progress = ImportProgressResponse {
        id: import_id,
        filename: "mock_import.xlsx".to_string(),
        status: "completed".to_string(),
        total_records: 100,
        processed_records: 100,
        success_count: 95,
        error_count: 5,
        started_at: Utc::now(),
        completed_at: Some(Utc::now()),
    };
    Ok(Json(ApiResponse::success(progress, None)))
}

pub async fn get_import_history_handler(
    State(service): State<Arc<TeachingAidsService>>,
    Query(_params): Query<PageParams>,
) -> Result<Json<ApiResponse<Vec<ImportProgressResponse>>>, StatusCode> {
    // Return empty history for now
    let history: Vec<ImportProgressResponse> = vec![
        ImportProgressResponse {
            id: Uuid::new_v4(),
            filename: "mock_import.xlsx".to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
        ImportProgressResponse {
            id: Uuid::new_v4(),
            filename: "mock_import.xlsx".to_string(),
            status: "completed".to_string(),
            total_records: 100,
            processed_records: 100,
            success_count: 95,
            error_count: 5,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
        },
    ];
    Ok(Json(ApiResponse::success(history, None)))
}
