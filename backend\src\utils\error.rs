use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;

pub type Result<T> = anyhow::Result<T>;

#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Forbidden: {0}")]
    Forbidden(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Internal server error: {0}")]
    InternalServerError(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("Token error: {0}")]
    TokenError(String),

    #[error("Password error: {0}")]
    PasswordError(String),

    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("Authentication error: {0}")]
    AuthError(#[from] crate::model::user::auth::AuthError),

    #[error("Anyhow error: {0}")]
    AnyhowError(#[from] anyhow::Error),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::DatabaseError(e) => (StatusCode::INTERNAL_SERVER_ERROR, e.to_string()),
            AppError::ConfigError(e) => (StatusCode::INTERNAL_SERVER_ERROR, e),
            AppError::NotFound(e) => (StatusCode::NOT_FOUND, e),
            AppError::InvalidInput(e) => (StatusCode::BAD_REQUEST, e),
            AppError::BadRequest(e) => (StatusCode::BAD_REQUEST, e),
            AppError::Unauthorized(e) => (StatusCode::UNAUTHORIZED, e),
            AppError::Forbidden(e) => (StatusCode::FORBIDDEN, e),
            AppError::Conflict(e) => (StatusCode::CONFLICT, e),
            AppError::InternalServerError(e) => (StatusCode::INTERNAL_SERVER_ERROR, e),
            AppError::ServiceUnavailable(e) => (StatusCode::SERVICE_UNAVAILABLE, e),
            AppError::TokenError(e) => (StatusCode::UNAUTHORIZED, e),
            AppError::PasswordError(e) => (StatusCode::BAD_REQUEST, e),
            AppError::SerializationError(e) => (StatusCode::INTERNAL_SERVER_ERROR, e.to_string()),
            AppError::AuthError(e) => (StatusCode::UNAUTHORIZED, e.to_string()),
            AppError::AnyhowError(e) => (StatusCode::INTERNAL_SERVER_ERROR, e.to_string()),
        };

        let body = Json(json!({ "error": error_message }));
        (status, body).into_response()
    }
}
