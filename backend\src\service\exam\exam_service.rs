use crate::model::exam::exam::*;
use crate::model::exam::exam::{Exam, ExamClass, ExamResponse, ExamStatistics, ExamStatisticsResponse, ExamSubject, JointExam, JointExamInvitationRequest, ScoreDistribution};
use crate::utils::error_handler::AppResult;
use anyhow::Result;
use sqlx::{PgPool, Row};
use uuid::Uuid;

pub struct ExamService {
    pool: PgPool,
}

impl ExamService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    fn schema_name(tenant_id: Uuid) -> String {
        format!("tenant_{}", tenant_id.to_string().replace('-', "_"))
    }

    pub async fn create_exam(
        &self,
        tenant_id: Uuid,
        creator_id: Uuid,
        request: CreateExamRequest,
    ) -> AppResult<ExamResponse> {
        let mut tx = self.pool.begin().await?;
        let schema = Self::schema_name(tenant_id);

        // Create main exam record
        let exam_id = Uuid::new_v4();
        let exam_query = format!(
            r#"
            INSERT INTO {}.exams (
                id, name, type, grade_level, exam_nature, description,
                start_time, end_time, expected_collection_time, scan_start_time,
                grading_mode, quality_control, ai_confidence_threshold,
                manual_review_ratio, created_by, status
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, 'draft'
            ) RETURNING *
            "#,
            schema
        );
        let exam = sqlx::query_as::<_, Exam>(&exam_query)
            .bind(exam_id)
            .bind(&request.name)
            .bind(&request.exam_type)
            .bind(&request.grade_level)
            .bind(&request.exam_nature)
            .bind(&request.description)
            .bind(request.start_time)
            .bind(request.end_time)
            .bind(request.expected_collection_time)
            .bind(request.scan_start_time)
            .bind(&request.grading_mode)
            .bind(&request.quality_control)
            .bind(request.ai_confidence_threshold)
            .bind(request.manual_review_ratio)
            .bind(creator_id)
            .fetch_one(&mut *tx)
            .await?;

        // Create exam subjects
        let mut subjects = Vec::new();
        for subject_req in &request.subjects {
            let subject_query = format!(
                r#"
                INSERT INTO {}.exam_subjects (
                    id, exam_id, subject_id, paper_template_id, total_score, pass_score
                ) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *
                "#,
                schema
            );
            let subject = sqlx::query_as::<_, ExamSubject>(&subject_query)
                .bind(Uuid::new_v4())
                .bind(exam_id)
                .bind(subject_req.subject_id)
                .bind(subject_req.paper_template_id)
                .bind(subject_req.total_score)
                .bind(subject_req.pass_score)
                .fetch_one(&mut *tx)
                .await?;
            subjects.push(subject);
        }

        // Create exam classes
        let mut classes = Vec::new();
        for class_req in &request.classes {
            let class_query = format!(
                r#"
                INSERT INTO {}.exam_classes (
                    id, exam_id, class_id, class_type
                ) VALUES ($1, $2, $3, $4) RETURNING *
                "#,
                schema
            );
            let class = sqlx::query_as::<_, ExamClass>(&class_query)
                .bind(Uuid::new_v4())
                .bind(exam_id)
                .bind(class_req.class_id)
                .bind(&class_req.class_type)
                .fetch_one(&mut *tx)
                .await?;
            classes.push(class);
        }

        // Create exam students
        if let Some(selected_students) = &request.selected_students {
            for student_id in selected_students {
                let student_query = format!(
                    r#"
                    INSERT INTO {}.exam_students (
                        id, exam_id, student_id, class_id
                    ) VALUES ($1, $2, $3, $4)
                    "#,
                    schema
                );
                sqlx::query(&student_query)
                    .bind(Uuid::new_v4())
                    .bind(exam_id)
                    .bind(student_id)
                    .bind(request.classes.get(0).map(|c| c.class_id))
                    .execute(&mut *tx)
                    .await?;
            }
        } else {
            // Auto-enroll all students from selected classes
            for class_req in &request.classes {
                let student_query = format!(
                    r#"
                    INSERT INTO {0}.exam_students (id, exam_id, student_id, class_id)
                    SELECT gen_random_uuid(), $1, s.id, $2
                    FROM {0}.students s
                    JOIN {0}.student_teaching_classes stc ON s.id = stc.student_id
                    WHERE stc.class_id = $2
                    "#,
                    schema
                );
                sqlx::query(&student_query)
                    .bind(exam_id)
                    .bind(class_req.class_id)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        tx.commit().await?;

        // Get student count
        let count_query = format!(
            "SELECT COUNT(*)::bigint FROM {}.exam_students WHERE exam_id = $1",
            schema
        );
        let student_count = sqlx::query_scalar::<_, i64>(&count_query)
            .bind(exam_id)
            .fetch_one(&self.pool)
            .await?;

        Ok(ExamResponse {
            id: exam.id,
            name: exam.name,
            exam_type: exam.exam_type,
            grade_level: exam.grade_level,
            exam_nature: exam.exam_nature,
            description: exam.description,
            start_time: exam.start_time,
            end_time: exam.end_time,
            expected_collection_time: exam.expected_collection_time,
            scan_start_time: exam.scan_start_time,
            grading_mode: exam.grading_mode,
            quality_control: exam.quality_control,
            ai_confidence_threshold: exam.ai_confidence_threshold,
            manual_review_ratio: exam.manual_review_ratio,
            status: exam.status,
            created_by: exam.created_by,
            tenant_id,
            created_at: exam.created_at,
            updated_at: exam.updated_at,
            subjects,
            classes,
            student_count,
            joint_exam_info: None,
        })
    }

    pub async fn get_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<Option<ExamResponse>> {
        let schema = Self::schema_name(tenant_id);
        let exam_query = format!("SELECT * FROM {}.exams WHERE id = $1", schema);
        let exam = sqlx::query_as::<_, Exam>(&exam_query)
            .bind(exam_id)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(exam) = exam {
            let subjects_query = format!("SELECT * FROM {}.exam_subjects WHERE exam_id = $1", schema);
            let subjects = sqlx::query_as::<_, ExamSubject>(&subjects_query)
                .bind(exam_id)
                .fetch_all(&self.pool)
                .await?;

            let classes_query = format!("SELECT * FROM {}.exam_classes WHERE exam_id = $1", schema);
            let classes = sqlx::query_as::<_, ExamClass>(&classes_query)
                .bind(exam_id)
                .fetch_all(&self.pool)
                .await?;

            let student_count_query = format!(
                "SELECT COUNT(*)::bigint FROM {}.exam_students WHERE exam_id = $1",
                schema
            );
            let student_count = sqlx::query_scalar::<_, i64>(&student_count_query)
                .bind(exam_id)
                .fetch_one(&self.pool)
                .await?;

            let joint_exam_info_query = format!(
                "SELECT * FROM {}.joint_exams WHERE main_exam_id = $1",
                schema
            );
            let joint_exam_info = sqlx::query_as::<_, JointExam>(&joint_exam_info_query)
                .bind(exam_id)
                .fetch_all(&self.pool)
                .await?;

            Ok(Some(ExamResponse {
                id: exam.id,
                name: exam.name,
                exam_type: exam.exam_type,
                grade_level: exam.grade_level,
                exam_nature: exam.exam_nature,
                description: exam.description,
                start_time: exam.start_time,
                end_time: exam.end_time,
                expected_collection_time: exam.expected_collection_time,
                scan_start_time: exam.scan_start_time,
                grading_mode: exam.grading_mode,
                quality_control: exam.quality_control,
                ai_confidence_threshold: exam.ai_confidence_threshold,
                manual_review_ratio: exam.manual_review_ratio,
                status: exam.status,
                created_by: exam.created_by,
                tenant_id,
                created_at: exam.created_at,
                updated_at: exam.updated_at,
                subjects,
                classes,
                student_count,
                joint_exam_info: Some(joint_exam_info),
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_exam(
        &self,
        tenant_id: Uuid,
        exam_id: Uuid,
        request: UpdateExamRequest,
    ) -> Result<Option<ExamResponse>> {
        let schema = Self::schema_name(tenant_id);
        let mut query = sqlx::QueryBuilder::new(format!("UPDATE {}.exams SET updated_at = NOW()", schema));
        let mut has_updates = false;

        if let Some(name) = &request.name {
            query.push(", name = ");
            query.push_bind(name);
            has_updates = true;
        }

        if let Some(description) = &request.description {
            query.push(", description = ");
            query.push_bind(description);
            has_updates = true;
        }

        if let Some(start_time) = request.start_time {
            query.push(", start_time = ");
            query.push_bind(start_time);
            has_updates = true;
        }

        if let Some(end_time) = request.end_time {
            query.push(", end_time = ");
            query.push_bind(end_time);
            has_updates = true;
        }

        if let Some(status) = &request.status {
            query.push(", status = ");
            query.push_bind(status);
            has_updates = true;
        }

        if !has_updates {
            return self.get_exam(tenant_id, exam_id).await;
        }

        query.push(" WHERE id = ");
        query.push_bind(exam_id);
        query.push(" RETURNING *");

        let exam = query
            .build_query_as::<Exam>()
            .fetch_optional(&self.pool)
            .await?;

        if exam.is_some() {
            self.get_exam(tenant_id, exam_id).await
        } else {
            Ok(None)
        }
    }

    pub async fn delete_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
        let schema = Self::schema_name(tenant_id);
        let query = format!("DELETE FROM {}.exams WHERE id = $1", schema);
        let result = sqlx::query(&query)
            .bind(exam_id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn list_exams(
        &self,
        tenant_id: Uuid,
        params: ExamQueryParams,
    ) -> Result<ExamListResponse> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;
        let schema = Self::schema_name(tenant_id);

        let mut query = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.exams WHERE 1=1", schema));
        let mut count_query = sqlx::QueryBuilder::new(format!("SELECT COUNT(*) FROM {}.exams WHERE 1=1", schema));

        if let Some(name) = &params.name {
            query.push(" AND name ILIKE ");
            query.push_bind(format!("%{}%", name));
            count_query.push(" AND name ILIKE ");
            count_query.push_bind(format!("%{}%", name));
        }

        if let Some(exam_type) = &params.exam_type {
            query.push(" AND type = ");
            query.push_bind(exam_type);
            count_query.push(" AND type = ");
            count_query.push_bind(exam_type);
        }

        if let Some(grade_level) = &params.grade_level {
            query.push(" AND grade_level = ");
            query.push_bind(grade_level);
            count_query.push(" AND grade_level = ");
            count_query.push_bind(grade_level);
        }

        if let Some(status) = &params.status {
            query.push(" AND status = ");
            query.push_bind(status);
            count_query.push(" AND status = ");
            count_query.push_bind(status);
        }

        if let Some(start_date) = params.start_date {
            query.push(" AND start_time >= ");
            query.push_bind(start_date);
            count_query.push(" AND start_time >= ");
            count_query.push_bind(start_date);
        }

        if let Some(end_date) = params.end_date {
            query.push(" AND end_time <= ");
            query.push_bind(end_date);
            count_query.push(" AND end_time <= ");
            count_query.push_bind(end_date);
        }

        query.push(" ORDER BY created_at DESC LIMIT ");
        query.push_bind(page_size);
        query.push(" OFFSET ");
        query.push_bind(offset);

        let exams = query
            .build_query_as::<Exam>()
            .fetch_all(&self.pool)
            .await?;

        let total = count_query
            .build_query_scalar::<i64>()
            .fetch_one(&self.pool)
            .await?;

        let mut exam_responses = Vec::new();
        for exam in exams {
            if let Some(exam_response) = self.get_exam(tenant_id, exam.id).await? {
                exam_responses.push(exam_response);
            }
        }

        Ok(ExamListResponse {
            exams: exam_responses,
            total,
            page,
            page_size,
        })
    }

    pub async fn create_joint_exam_invitation(
        &self,
        tenant_id: Uuid,
        request: JointExamInvitationRequest,
    ) -> Result<Vec<JointExam>> {
        let mut tx = self.pool.begin().await?;
        let mut invitations = Vec::new();
        let schema = Self::schema_name(tenant_id);

        for participant_tenant_id in &request.participant_tenant_ids {
            let query = format!(
                r#"
                INSERT INTO {}.joint_exams (
                    id, main_exam_id, organizer_tenant_id, participant_tenant_id,
                    invitation_status, invitation_sent_at
                ) VALUES ($1, $2, $3, $4, 'pending', NOW()) RETURNING *
                "#,
                schema
            );
            let invitation = sqlx::query_as::<_, JointExam>(&query)
                .bind(Uuid::new_v4())
                .bind(request.exam_id)
                .bind(tenant_id)
                .bind(participant_tenant_id)
                .fetch_one(&mut *tx)
                .await?;

            invitations.push(invitation);
        }

        tx.commit().await?;
        Ok(invitations)
    }

    pub async fn respond_to_joint_exam_invitation(
        &self,
        tenant_id: Uuid,
        request: JointExamResponseRequest,
    ) -> Result<Option<JointExam>> {
        let status = match request.response.as_str() {
            "accept" => "accepted",
            "reject" => "rejected",
            _ => return Err(anyhow::anyhow!("Invalid response")),
        };
        let schema = Self::schema_name(tenant_id);

        let query = format!(
            r#"
            UPDATE {}.joint_exams 
            SET invitation_status = $1, responded_at = NOW()
            WHERE id = $2 AND participant_tenant_id = $3
            RETURNING *
            "#,
            schema
        );
        let joint_exam = sqlx::query_as::<_, JointExam>(&query)
            .bind(status)
            .bind(request.invitation_id)
            .bind(tenant_id)
            .fetch_optional(&self.pool)
            .await?;

        Ok(joint_exam)
    }

    pub async fn get_exam_statistics(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<ExamStatisticsResponse> {
        let _exam = self.get_exam(tenant_id, exam_id).await?
            .ok_or_else(|| anyhow::anyhow!("Exam not found"))?;
        let schema = Self::schema_name(tenant_id);

        let stats_query = format!(
            r#"
            SELECT 
                COUNT(DISTINCT es.student_id) as total_participants,
                COUNT(DISTINCT ps.student_id) as completed_participants,
                AVG(gr.final_score) as average_score,
                MAX(gr.final_score) as highest_score,
                MIN(gr.final_score) as lowest_score
            FROM {0}.exam_students es
            LEFT JOIN {0}.paper_scans ps ON es.student_id = ps.student_id AND ps.exam_id = $1
            LEFT JOIN {0}.grading_records gr ON es.student_id = gr.student_id AND gr.exam_id = $1
            WHERE es.exam_id = $1
            "#,
            schema
        );
        let stats = sqlx::query(&stats_query)
            .bind(exam_id)
            .fetch_one(&self.pool)
            .await?;

        let total_participants: i64 = stats.try_get("total_participants").unwrap_or(0);
        let completed_participants: i64 = stats.try_get("completed_participants").unwrap_or(0);
        let average_score: f64 = stats.try_get("average_score").unwrap_or(0.0);
        let highest_score: f64 = stats.try_get("highest_score").unwrap_or(0.0);
        let lowest_score: f64 = stats.try_get("lowest_score").unwrap_or(0.0);

        let pass_rate = if total_participants > 0 {
            let pass_count_query = format!(
                r#"
                SELECT COUNT(DISTINCT gr.student_id)::bigint
                FROM {0}.grading_records gr
                JOIN {0}.exam_subjects es ON gr.exam_id = es.exam_id
                WHERE gr.exam_id = $1 AND gr.final_score >= COALESCE(es.pass_score, 60.0)
                "#,
                schema
            );
            let pass_count = sqlx::query_scalar::<_, i64>(&pass_count_query)
                .bind(exam_id)
                .fetch_one(&self.pool)
                .await?;

            (pass_count as f32 / total_participants as f32) * 100.0
        } else {
            0.0
        };

        // Get score distribution
        let score_distribution = vec![
            ScoreDistribution {
                score_range: "90-100".to_string(),
                count: 0,
                percentage: 0.0,
            },
            ScoreDistribution {
                score_range: "80-89".to_string(),
                count: 0,
                percentage: 0.0,
            },
            ScoreDistribution {
                score_range: "70-79".to_string(),
                count: 0,
                percentage: 0.0,
            },
            ScoreDistribution {
                score_range: "60-69".to_string(),
                count: 0,
                percentage: 0.0,
            },
            ScoreDistribution {
                score_range: "0-59".to_string(),
                count: 0,
                percentage: 0.0,
            },
        ];

        Ok(ExamStatisticsResponse {
            exam_id,
            total_participants,
            completed_participants,
            average_score: average_score as f32,
            highest_score: highest_score as f32,
            lowest_score: lowest_score as f32,
            pass_rate,
            score_distribution,
        })
    }

    pub async fn get_exam_statistics_overview(&self, tenant_id: Uuid) -> Result<ExamStatistics> {
        let schema = Self::schema_name(tenant_id);
        let stats_query = format!(
            r#"
            SELECT 
                COUNT(*) as total_exams,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_exams,
                COUNT(*) FILTER (WHERE status = 'in_progress') as in_progress_exams,
                COUNT(*) FILTER (WHERE status = 'draft') as draft_exams
            FROM {}.exams
            "#,
            schema
        );
        let stats = sqlx::query(&stats_query)
            .fetch_one(&self.pool)
            .await?;

        let total_exams: i64 = stats.try_get("total_exams").unwrap_or(0);
        let completed_exams: i64 = stats.try_get("completed_exams").unwrap_or(0);
        let in_progress_exams: i64 = stats.try_get("in_progress_exams").unwrap_or(0);
        let draft_exams: i64 = stats.try_get("draft_exams").unwrap_or(0);

        let student_count_query = format!(
            "SELECT COUNT(DISTINCT student_id)::bigint FROM {}.exam_students",
            schema
        );
        let student_count = sqlx::query_scalar::<_, i64>(&student_count_query)
            .fetch_one(&self.pool)
            .await?;

        let paper_count_query = format!("SELECT COUNT(*)::bigint FROM {}.paper_scans", schema);
        let paper_count = sqlx::query_scalar::<_, i64>(&paper_count_query)
            .fetch_one(&self.pool)
            .await?;

        let grading_progress_query = format!(
            r#"
            SELECT CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE (COUNT(*) FILTER (WHERE completed_at IS NOT NULL))::float / COUNT(*)::float * 100.0
            END as progress
            FROM {}.grading_assignments
            "#,
            schema
        );
        let grading_progress: f64 = sqlx::query_scalar::<_, f64>(&grading_progress_query)
            .fetch_one(&self.pool)
            .await?;

        Ok(ExamStatistics {
            total_exams,
            completed_exams,
            in_progress_exams,
            draft_exams,
            total_students: student_count,
            total_papers: paper_count,
            grading_progress: grading_progress as f32,
        })
    }

    pub async fn publish_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
        let schema = Self::schema_name(tenant_id);
        let query = format!(
            r#"
            UPDATE {}.exams 
            SET status = 'published', updated_at = NOW()
            WHERE id = $1 AND status = 'draft'
            "#,
            schema
        );
        let result = sqlx::query(&query)
            .bind(exam_id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn start_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
        let schema = Self::schema_name(tenant_id);
        let query = format!(
            r#"
            UPDATE {}.exams 
            SET status = 'in_progress', updated_at = NOW()
            WHERE id = $1 AND status = 'published'
            "#,
            schema
        );
        let result = sqlx::query(&query)
            .bind(exam_id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn complete_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
        let schema = Self::schema_name(tenant_id);
        let query = format!(
            r#"
            UPDATE {}.exams 
            SET status = 'completed', updated_at = NOW()
            WHERE id = $1 AND status = 'in_progress'
            "#,
            schema
        );
        let result = sqlx::query(&query)
            .bind(exam_id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }
}
