use crate::model::user::auth::*;
use crate::service::user::parent_service::ParentService;
use crate::utils::jwt;
use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::Json,
    routing::{get, post, put},
    Router,
};
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;

pub fn create_router() -> Router<Arc<ParentService>> {
    Router::new()
        .route("/link-student", post(link_student))
        .route("/students", get(get_linked_students))
        .route("/relations/{relation_id}/verify", post(verify_relationship))
        .route("/relations/{relation_id}/permissions", put(update_permissions))
        .route("/relations/{relation_id}/deactivate", post(deactivate_relationship))
}

async fn link_student(
    State(parent_service): State<Arc<ParentService>>,
    headers: HeaderMap,
    Json(request): <PERSON><PERSON><LinkStudentRequest>,
) -> Result<Json<LinkStudentResponse>, (StatusCode, Json<ErrorResponse>)> {
    let parent_user_id = extract_user_id_from_headers(&headers)?;

    match parent_service.link_student(parent_user_id, request).await {
        Ok(response) => {
            info!("Student link request submitted by parent {}", parent_user_id);
            Ok(Json(response))
        }
        Err(e) => {
            error!("Failed to link student: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn get_linked_students(
    State(parent_service): State<Arc<ParentService>>,
    headers: HeaderMap,
) -> Result<Json<LinkedStudentsResponse>, (StatusCode, Json<ErrorResponse>)> {
    let parent_user_id = extract_user_id_from_headers(&headers)?;

    match parent_service.get_linked_students(parent_user_id).await {
        Ok(response) => {
            info!("Retrieved linked students for parent {}", parent_user_id);
            Ok(Json(response))
        }
        Err(e) => {
            error!("Failed to get linked students: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn verify_relationship(
    State(parent_service): State<Arc<ParentService>>,
    headers: HeaderMap,
    Path(relation_id): Path<Uuid>,
    Json(request): Json<VerifyRelationshipRequest>,
) -> Result<Json<VerifyRelationshipResponse>, (StatusCode, Json<ErrorResponse>)> {
    let verified_by = extract_user_id_from_headers(&headers)?;

    // TODO: Add authorization check to ensure user has permission to verify relationships
    // This should typically be restricted to administrators or the student themselves

    match parent_service.verify_parent_student_relationship(
        relation_id,
        verified_by,
        &request.verification_method,
    ).await {
        Ok(_) => {
            info!("Parent-student relationship {} verified by {}", relation_id, verified_by);
            Ok(Json(VerifyRelationshipResponse {
                success: true,
                message: "Relationship verified successfully".to_string(),
            }))
        }
        Err(e) => {
            error!("Failed to verify relationship: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn update_permissions(
    State(parent_service): State<Arc<ParentService>>,
    headers: HeaderMap,
    Path(relation_id): Path<Uuid>,
    Json(request): Json<UpdatePermissionsRequest>,
) -> Result<Json<UpdatePermissionsResponse>, (StatusCode, Json<ErrorResponse>)> {
    let _user_id = extract_user_id_from_headers(&headers)?;

    // TODO: Add authorization check to ensure user has permission to update permissions
    // This should typically be restricted to administrators

    match parent_service.update_access_permissions(relation_id, request.permissions).await {
        Ok(_) => {
            info!("Access permissions updated for relationship {}", relation_id);
            Ok(Json(UpdatePermissionsResponse {
                success: true,
                message: "Permissions updated successfully".to_string(),
            }))
        }
        Err(e) => {
            error!("Failed to update permissions: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn deactivate_relationship(
    State(parent_service): State<Arc<ParentService>>,
    headers: HeaderMap,
    Path(relation_id): Path<Uuid>,
) -> Result<Json<DeactivateRelationshipResponse>, (StatusCode, Json<ErrorResponse>)> {
    let _user_id = extract_user_id_from_headers(&headers)?;

    // TODO: Add authorization check to ensure user has permission to deactivate relationships
    // This should allow parents, students, or administrators

    match parent_service.deactivate_relationship(relation_id).await {
        Ok(_) => {
            info!("Parent-student relationship {} deactivated", relation_id);
            Ok(Json(DeactivateRelationshipResponse {
                success: true,
                message: "Relationship deactivated successfully".to_string(),
            }))
        }
        Err(e) => {
            error!("Failed to deactivate relationship: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

// Helper functions
fn extract_user_id_from_headers(
    headers: &HeaderMap,
) -> Result<Uuid, (StatusCode, Json<ErrorResponse>)> {
    let token = extract_bearer_token(headers)
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    success: false,
                    message: "Missing authorization token".to_string(),
                    error_code: "MISSING_TOKEN".to_string(),
                }),
            )
        })?;

    let claims = jwt::validate_token(&token).map_err(|e| {
        error!("Failed to validate token: {:?}", e);
        (
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                success: false,
                message: "Invalid or expired token".to_string(),
                error_code: "INVALID_TOKEN".to_string(),
            }),
        )
    })?;
    
    Uuid::parse_str(&claims.sub).map_err(|_| {
        (
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                success: false,
                message: "Invalid user ID in token".to_string(),
                error_code: "INVALID_TOKEN_SUB".to_string(),
            }),
        )
    })
}

fn extract_bearer_token(headers: &HeaderMap) -> Option<String> {
    headers
        .get("authorization")
        .and_then(|value| value.to_str().ok())
        .and_then(|auth_header| {
            if auth_header.starts_with("Bearer ") {
                Some(auth_header[7..].to_string())
            } else {
                None
            }
        })
}

fn map_auth_error(error: AuthError) -> (StatusCode, ErrorResponse) {
    let (status, message, code) = match error {
        AuthError::UserNotFound => (
            StatusCode::NOT_FOUND,
            "User not found".to_string(),
            "USER_NOT_FOUND".to_string(),
        ),
        AuthError::IdentityNotFound => (
            StatusCode::NOT_FOUND,
            "Student identity not found".to_string(),
            "IDENTITY_NOT_FOUND".to_string(),
        ),
        AuthError::IdentityAlreadyBound => (
            StatusCode::CONFLICT,
            "Relationship already exists".to_string(),
            "RELATIONSHIP_EXISTS".to_string(),
        ),
        AuthError::InsufficientPermissions => (
            StatusCode::FORBIDDEN,
            "Insufficient permissions".to_string(),
            "INSUFFICIENT_PERMISSIONS".to_string(),
        ),
        AuthError::InvalidCredentials => (
            StatusCode::UNAUTHORIZED,
            "Invalid credentials".to_string(),
            "INVALID_CREDENTIALS".to_string(),
        ),
        AuthError::DatabaseError(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Internal server error".to_string(),
            "DATABASE_ERROR".to_string(),
        ),
        AuthError::JwtError(_) => (
            StatusCode::UNAUTHORIZED,
            "Invalid or expired token".to_string(),
            "INVALID_TOKEN".to_string(),
        ),
        _ => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Internal server error".to_string(),
            "INTERNAL_ERROR".to_string(),
        ),
    };

    (
        status,
        ErrorResponse {
            success: false,
            message,
            error_code: code,
        },
    )
}

// Request/Response DTOs
#[derive(serde::Deserialize)]
struct VerifyRelationshipRequest {
    verification_method: String,
}

#[derive(serde::Serialize)]
struct VerifyRelationshipResponse {
    success: bool,
    message: String,
}

#[derive(serde::Deserialize)]
struct UpdatePermissionsRequest {
    permissions: serde_json::Value,
}

#[derive(serde::Serialize)]
struct UpdatePermissionsResponse {
    success: bool,
    message: String,
}

#[derive(serde::Serialize)]
struct DeactivateRelationshipResponse {
    success: bool,
    message: String,
}

#[derive(serde::Serialize)]
struct ErrorResponse {
    success: bool,
    message: String,
    error_code: String,
}