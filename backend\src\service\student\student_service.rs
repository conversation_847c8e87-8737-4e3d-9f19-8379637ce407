use std::collections::HashMap;

use crate::model::{
    CreateStudent, CreateStudentProfileLevel, CreateStudentProfileTag, CreateStudentTeachingClass,
    Student, StudentDetail, StudentProfileLevel, StudentProfileTag, StudentSearchParams,
    StudentTeachingClass, UpdateStudent,
};
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use sqlx::postgres::PgRow;
use sqlx::{PgPool, Row};
use uuid::Uuid;

#[derive(Clone)]
pub struct StudentService {
    db_pool: PgPool,
}

impl StudentService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// Get all students with optional search parameters
    pub async fn get_students(
        &self,
        schema_name: &str,
        params: StudentSearchParams,
    ) -> Result<Vec<Student>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let mut query = String::from("SELECT * FROM students WHERE 1=1");

        if let Some(name) = &params.name {
            query.push_str(" AND name ILIKE '%");
            query.push_str(name);
            query.push_str("%'");
        }

        if let Some(student_id) = &params.student_id {
            query.push_str(" AND student_id ILIKE '%");
            query.push_str(student_id);
            query.push_str("%'");
        }

        if let Some(class_id) = params.class_id {
            query.push_str(&format!(" AND administrative_class_id = {}", class_id));
        }

        if let Some(grade_level_id) = params.grade_level_id {
            query.push_str(&format!(" AND grade_level_id = {}", grade_level_id));
        }

        if let Some(status) = &params.status {
            query.push_str(" AND status = '");
            query.push_str(status);
            query.push_str("'");
        }

        if let Some(profile_level) = &params.profile_level {
            query.push_str(" AND profile_level = '");
            query.push_str(profile_level);
            query.push_str("'");
        }

        query.push_str(" ORDER BY created_at DESC");

        if let Some(limit) = params.limit {
            query.push_str(&format!(" LIMIT {}", limit));
        }

        if let Some(offset) = params.offset {
            query.push_str(&format!(" OFFSET {}", offset));
        }

        let students = sqlx::query_as::<_, Student>(&query)
            .fetch_all(&mut *conn)
            .await?;

        Ok(students)
    }

    /// Get student by ID
    pub async fn get_student_by_id(
        &self,
        schema_name: &str,
        id: Uuid,
    ) -> Result<Option<Student>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let student = sqlx::query_as::<_, Student>("SELECT * FROM students WHERE id = $1")
            .bind(id)
            .fetch_optional(&mut *conn)
            .await?;
        Ok(student)
    }

    /// Get student detail with related information
    pub async fn get_student_detail(
        &self,
        schema_name: &str,
        id: Uuid,
    ) -> Result<Option<StudentDetail>, AppError> {
        let student = self.get_student_by_id(schema_name, id).await?;

        if let Some(student) = student {
            let safe_schema = validate_schema_name(schema_name)?;
            let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

            // Get class name
            let class_name = if let Some(class_id) = student.administrative_class_id {
                sqlx::query_scalar::<_, String>("SELECT name FROM classes WHERE id = $1")
                    .bind(class_id)
                    .fetch_optional(&mut *conn)
                    .await?
            } else {
                None
            };

            // Get grade level name from public schema
            let grade_level_name = if let Some(grade_level_id) = student.grade_level_id {
                sqlx::query_scalar::<_, String>(
                    "SELECT name FROM public.grade_levels WHERE id = $1",
                )
                .bind(grade_level_id)
                .fetch_optional(&self.db_pool)
                .await?
            } else {
                None
            };

            // Get teaching classes
            let teaching_classes = sqlx::query_as::<_, StudentTeachingClass>(
                "SELECT * FROM student_teaching_classes WHERE student_id = $1",
            )
            .bind(id)
            .fetch_all(&mut *conn)
            .await?;

            // Get profile levels
            let profile_levels = sqlx::query_as::<_, StudentProfileLevel>(
                "SELECT * FROM student_profile_levels WHERE student_id = $1 ORDER BY assessment_date DESC"
            )
                .bind(id)
                .fetch_all(&mut *conn)
                .await?;

            // Get profile tags
            let profile_tags = sqlx::query_as::<_, StudentProfileTag>(
                "SELECT * FROM student_profile_tags WHERE student_id = $1 ORDER BY created_at DESC",
            )
            .bind(id)
            .fetch_all(&mut *conn)
            .await?;

            let student_detail = StudentDetail {
                student,
                class_name,
                grade_level_name,
                teaching_classes,
                profile_levels,
                profile_tags,
            };

            Ok(Some(student_detail))
        } else {
            Ok(None)
        }
    }

    /// Create a new student
    pub async fn create_student(
        &self,
        schema_name: &str,
        payload: CreateStudent,
    ) -> Result<Student, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let student = sqlx::query_as::<_, Student>(
            "INSERT INTO students (
                student_id, name, gender, birth_date, id_number, phone, email, address,
                guardian_name, guardian_phone, guardian_relation, administrative_class_id,
                grade_level_id, user_id, enrollment_date, status, profile_level, profile_tags, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19) 
            RETURNING *"
        )
        .bind(&payload.student_id)
        .bind(&payload.name)
        .bind(&payload.gender)
        .bind(&payload.birth_date)
        .bind(&payload.id_number)
        .bind(&payload.phone)
        .bind(&payload.email)
        .bind(&payload.address)
        .bind(&payload.guardian_name)
        .bind(&payload.guardian_phone)
        .bind(&payload.guardian_relation)
        .bind(&payload.administrative_class_id)
        .bind(&payload.grade_level_id)
        .bind(&payload.user_id)
        .bind(&payload.enrollment_date)
        .bind(&payload.status.as_deref().unwrap_or("active"))
        .bind(&payload.profile_level)
        .bind(&payload.profile_tags)
        .bind(&payload.notes)
        .fetch_one(&mut *conn)
        .await?;

        Ok(student)
    }

    /// Update an existing student
    pub async fn update_student(
        &self,
        schema_name: &str,
        id: Uuid,
        payload: UpdateStudent,
    ) -> Result<Student, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let student = sqlx::query_as::<_, Student>(
            "UPDATE students SET 
                student_id = COALESCE($1, student_id),
                name = COALESCE($2, name),
                gender = COALESCE($3, gender),
                birth_date = COALESCE($4, birth_date),
                id_number = COALESCE($5, id_number),
                phone = COALESCE($6, phone),
                email = COALESCE($7, email),
                address = COALESCE($8, address),
                guardian_name = COALESCE($9, guardian_name),
                guardian_phone = COALESCE($10, guardian_phone),
                guardian_relation = COALESCE($11, guardian_relation),
                administrative_class_id = COALESCE($12, administrative_class_id),
                grade_level_id = COALESCE($13, grade_level_id),
                user_id = COALESCE($14, user_id),
                enrollment_date = COALESCE($15, enrollment_date),
                status = COALESCE($16, status),
                profile_level = COALESCE($17, profile_level),
                profile_tags = COALESCE($18, profile_tags),
                notes = COALESCE($19, notes),
                updated_at = NOW()
            WHERE id = $20 
            RETURNING *",
        )
        .bind(&payload.student_id)
        .bind(&payload.name)
        .bind(&payload.gender)
        .bind(&payload.birth_date)
        .bind(&payload.id_number)
        .bind(&payload.phone)
        .bind(&payload.email)
        .bind(&payload.address)
        .bind(&payload.guardian_name)
        .bind(&payload.guardian_phone)
        .bind(&payload.guardian_relation)
        .bind(&payload.administrative_class_id)
        .bind(&payload.grade_level_id)
        .bind(&payload.user_id)
        .bind(&payload.enrollment_date)
        .bind(&payload.status)
        .bind(&payload.profile_level)
        .bind(&payload.profile_tags)
        .bind(&payload.notes)
        .bind(id)
        .fetch_one(&mut *conn)
        .await?;

        Ok(student)
    }

    /// Delete a student
    pub async fn delete_student(&self, schema_name: &str, id: Uuid) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        sqlx::query("DELETE FROM students WHERE id = $1")
            .bind(id)
            .execute(&mut *conn)
            .await?;

        Ok(())
    }

    /// Add student to teaching class
    pub async fn add_teaching_class(
        &self,
        schema_name: &str,
        payload: CreateStudentTeachingClass,
    ) -> Result<StudentTeachingClass, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let teaching_class = sqlx::query_as::<_, StudentTeachingClass>(
            "INSERT INTO student_teaching_classes (student_id, class_id, subject) VALUES ($1, $2, $3) RETURNING *"
        )
        .bind(payload.student_id)
        .bind(payload.class_id)
        .bind(payload.subject)
        .fetch_one(&mut *conn)
        .await?;

        Ok(teaching_class)
    }

    /// Remove student from teaching class
    pub async fn remove_teaching_class(
        &self,
        schema_name: &str,
        student_id: Uuid,
        class_id: Uuid,
        subject: &str,
    ) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        sqlx::query("DELETE FROM student_teaching_classes WHERE student_id = $1 AND class_id = $2 AND subject = $3")
            .bind(student_id)
            .bind(class_id)
            .bind(subject)
            .execute(&mut *conn)
            .await?;

        Ok(())
    }

    /// Add or update student profile level
    pub async fn update_profile_level(
        &self,
        schema_name: &str,
        payload: CreateStudentProfileLevel,
    ) -> Result<StudentProfileLevel, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let profile_level = sqlx::query_as::<_, StudentProfileLevel>(
            "INSERT INTO student_profile_levels (student_id, subject, level, level_description, assessment_date, assessed_by)
             VALUES ($1, $2, $3, $4, $5, $6)
             ON CONFLICT (student_id, subject, assessment_date)
             DO UPDATE SET level = EXCLUDED.level, level_description = EXCLUDED.level_description, assessed_by = EXCLUDED.assessed_by, updated_at = NOW()
             RETURNING *"
        )
        .bind(payload.student_id)
        .bind(&payload.subject)
        .bind(&payload.level)
        .bind(&payload.level_description)
        .bind(payload.assessment_date)
        .bind(payload.assessed_by)
        .fetch_one(&mut *conn)
        .await?;

        Ok(profile_level)
    }

    /// Add or update student profile tag
    pub async fn update_profile_tag(
        &self,
        schema_name: &str,
        payload: CreateStudentProfileTag,
    ) -> Result<StudentProfileTag, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let profile_tag = sqlx::query_as::<_, StudentProfileTag>(
            "INSERT INTO student_profile_tags (student_id, tag_name, tag_value, tag_category, created_by)
             VALUES ($1, $2, $3, $4, $5)
             ON CONFLICT (student_id, tag_name)
             DO UPDATE SET tag_value = EXCLUDED.tag_value, tag_category = EXCLUDED.tag_category, updated_at = NOW()
             RETURNING *"
        )
        .bind(payload.student_id)
        .bind(&payload.tag_name)
        .bind(&payload.tag_value)
        .bind(&payload.tag_category)
        .bind(payload.created_by)
        .fetch_one(&mut *conn)
        .await?;

        Ok(profile_tag)
    }

    /// Remove student profile tag
    pub async fn remove_profile_tag(
        &self,
        schema_name: &str,
        student_id: Uuid,
        tag_name: &str,
    ) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        sqlx::query("DELETE FROM student_profile_tags WHERE student_id = $1 AND tag_name = $2")
            .bind(student_id)
            .bind(tag_name)
            .execute(&mut *conn)
            .await?;

        Ok(())
    }

    /// Get students count by class
    pub async fn get_students_count_by_class(
        &self,
        schema_name: &str,
        class_id: Uuid,
    ) -> Result<i64, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM students WHERE administrative_class_id = $1 AND status = 'active'")
            .bind(class_id)
            .fetch_one(&mut *conn)
            .await?;

        Ok(count)
    }

    /// Get students count by grade level
    pub async fn get_students_count_by_grade(
        &self,
        schema_name: &str,
        grade_level_id: Uuid,
    ) -> Result<i64, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM students WHERE grade_level_id = $1 AND status = 'active'",
        )
        .bind(grade_level_id)
        .fetch_one(&mut *conn)
        .await?;

        Ok(count)
    }

    /**
     * 作者：张瀚
     * 说明：批量统计指定行政班级中的学生数量
     */
    pub async fn batch_count_by_class(
        &self,
        schema_name: &str,
        administrative_class_ids: &Vec<Uuid>,
    ) -> Result<HashMap<Uuid, i64>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.administrative_class_id,count(*) from {}.students s where s.administrative_class_id in (",
            schema_name
        ));
        for (index, id) in administrative_class_ids.iter().enumerate() {
            builder.push_bind(id);
            if index < administrative_class_ids.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) group by s.administrative_class_id");
            }
        }
        let list: Vec<PgRow> = builder
            .build()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        let mut map = HashMap::<Uuid, i64>::new();
        for ele in list.iter() {
            let id: Uuid = ele
                .try_get("administrative_class_id")
                .map_err(|e| e.to_string())?;
            let count: i64 = ele.try_get("count").map_err(|e| e.to_string())?;
            map.insert(id, count);
        }
        Ok(map)
    }
}
