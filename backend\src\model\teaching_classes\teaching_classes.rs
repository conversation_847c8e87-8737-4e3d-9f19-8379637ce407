use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::prelude::FromRow;
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TeachingClassesStatistics {
    pub total_classes: i32,
    pub total_teacher: i32,
    pub total_students: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct TeachingClasses {
    pub id: Uuid,
    pub class_name: String,
    pub class_code: String,
    pub academic_year: i32,
    pub grade_level_code: String,
    pub teacher_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct TeachingClassesDetail {
    pub id: Uuid,
    pub class_name: String,
    pub class_code: String,
    pub academic_year: i32,
    pub grade_level_code: String,
    pub teacher_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    //额外信息
    pub teacher_name: String,
    pub grade_level_name: String,
    pub total_student: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateTeachingClassesParams {
    pub class_name: String,
    pub class_code: String,
    pub academic_year: i32,
    pub grade_level_code: String,
    pub teacher_id: Uuid,
}
