import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getHomeworks, deleteHomework } from '../../services/homeworkService';
import { Homework } from '../../types/homework';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle } from 'lucide-react';

const HomeworkListPage: React.FC = () => {
  const [homeworks, setHomeworks] = useState<Homework[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHomeworks = async () => {
      try {
        const data = await getHomeworks();
        console.log('Fetched data:', data);
        setHomeworks(data);
      } catch (err) {
        setError('Failed to fetch homeworks');
      } finally {
        setLoading(false);
      }
    };

    fetchHomeworks();
  }, []);

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this homework?')) {
      try {
        await deleteHomework(id);
        setHomeworks(homeworks.filter(h => h.id !== id));
      } catch (err) {
        setError('Failed to delete homework');
      }
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>{error}</div>;

  return (
    <Tabs defaultValue="all">
      <div className="flex items-center justify-between mb-4">
        <TabsList>
          <TabsTrigger value="all">All Homework</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="published">Published</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>
        <Link to="/homeworks/new">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Homework
          </Button>
        </Link>
      </div>
      <TabsContent value="all">
        <Card>
          <CardHeader>
            <CardTitle>All Homework</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Grade Level</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Start Time</TableHead>
                  <TableHead>End Time</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {homeworks?.map(hw => (
                  <TableRow key={hw.id}>
                    <TableCell>{hw.name}</TableCell>
                    <TableCell>{hw.grade_level}</TableCell>
                    <TableCell>{hw.status}</TableCell>
                    <TableCell>{new Date(hw.start_time).toLocaleString()}</TableCell>
                    <TableCell>{new Date(hw.end_time).toLocaleString()}</TableCell>
                    <TableCell>
                      <Link to={`/homeworks/${hw.id}`} className="text-blue-500 hover:underline mr-2">
                        View
                      </Link>
                      <Link to={`/homeworks/edit/${hw.id}`} className="text-yellow-500 hover:underline mr-2">
                        Edit
                      </Link>
                      <button onClick={() => handleDelete(hw.id)} className="text-red-500 hover:underline">
                        Delete
                      </button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default HomeworkListPage;