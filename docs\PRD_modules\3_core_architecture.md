# 3. 核心架构需求

## 3.1 多租户架构
- **Schema分层设计**：采用混合Schema隔离策略
- **动态创建**：由系统管理员或运维人员添加学校机构时动态创建租户内的数据库表
- **Public Schema**：存储全局共享表（用户、角色、租户管理等）
- **租户Schema**：每个租户独立Schema，存储业务数据
- **跨租户协作**：支持多校联考、数据共享等场景
- **弹性扩展**：支持租户动态扩容和缩容

### 3.1.1 Schema分布策略
```sql
-- Public Schema（全局共享）
public.users                    -- 所有用户（通过手机号注册）
public.roles                    -- 角色定义
public.tenants                  -- 租户信息
public.user_tenant_links        -- 用户与租户的中央映射关系
public.parent_student_relations -- 家长-学生关联
public.user_identity_switches   -- 用户身份切换记录
public.question_bank            -- 公共题库（增强版：支持多答案/解析/难度/知识点）
public.question_answers         -- 题目多答案表
public.question_explanations    -- 题目多层次解析表
public.question_difficulties    -- 题目多维度难度表
public.question_knowledge_points -- 题目知识点关联表
public.exam_papers              -- 公共试卷模板
public.exam_paper_questions     -- 试卷-题目关联
public.textbooks                -- 公共教辅（支持混合内容结构）
public.textbook_tenant_access   -- 教辅租户授权
public.textbook_mixed_contents  -- 教辅混合内容（知识讲解/例题/试题混排，全局共享）
public.content_reading_orders   -- 教辅内容阅读顺序控制（全局共享）
public.subjects                 -- 公共字典：学科
public.grade_levels             -- 公共字典：年级
public.education_stages         -- 公共字典：学段 (小学/初中/高中等)
public.question_types           -- 公共字典：题型

-- 租户Schema（业务隔离 - tenant_001.<table>）
-- 组织与人员
subject_groups              -- 学科组
classes                     -- 班级 (行政班/教学班)
students                    -- 学生
user_identities             -- 用户身份 (核心权限关联)
-- 考试与阅卷
exams                       -- 考试
paper_scans                 -- 试卷扫描件
answer_card_blocks          -- 答题卡块定义
card_block_question_links   -- 答题卡块与题目关联
grading_assignments         -- 评阅任务分配
grading_records             -- 评阅记录 (人工)
ai_grading_records          -- AI评阅记录
-- 成绩与分析
question_scores             -- 单题得分记录
academic_statistics         -- 学情统计 (总分/排名)
question_analysis           -- 题目分析 (得分率/难度等)
learning_records            -- 学生个人学习记录
```

## 3.1.2 系统字典管理

### 3.1.2.1 学段管理 (Education Stages)

**功能概述**：
学段管理是平台的核心字典功能，为整个教育生态提供标准化的学段分类，确保跨租户的数据一致性和业务逻辑的统一性。

**业务价值**：
- **标准化管理**：建立统一的学段标准，支持小学、初中、高中等各级教育阶段
- **业务关联**：与年级、学科、教材等核心业务数据形成完整的教育数据体系
- **灵活扩展**：支持特殊教育阶段（如职业教育、国际课程）的自定义配置
- **数据聚合**：为跨学段的数据分析和统计提供基础维度

**核心功能**：

1. **学段定义与管理**：
   - **标准学段**：小学(PRIMARY)、初中(MIDDLE)、高中(HIGH)、职业教育(VOCATIONAL)
   - **国际课程**：IB、AP、A-Level等国际教育体系
   - **特殊教育**：特殊需求学生的定制化学段分类
   - **层级管理**：支持学段的排序和层级关系定义

2. **学段关联管理**：
   - **年级关联**：学段与具体年级的映射关系（如小学1-6年级）
   - **学科关联**：不同学段的学科设置和难度配置
   - **教材关联**：学段与对应教材版本的关联关系
   - **考试关联**：学段特定的考试类型和评估标准

3. **业务集成**：
   - **题库分类**：按学段组织题目资源和难度级别
   - **试卷模板**：学段特定的试卷格式和评分标准
   - **成绩分析**：学段维度的学情分析和对比
   - **教学管理**：学段特定的教学要求和课程标准

**数据结构设计**：

```sql
-- 学段字典表
CREATE TABLE public.education_stages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(30) UNIQUE NOT NULL,           -- 学段编码 (PRIMARY, MIDDLE, HIGH, etc.)
    name VARCHAR(100) NOT NULL,                 -- 学段名称 (小学, 初中, 高中, etc.)
    short_name VARCHAR(20),                     -- 简称 (小, 初, 高)
    description TEXT,                           -- 详细描述
    order_level INTEGER NOT NULL,              -- 排序级别 (1-小学, 2-初中, 3-高中)
    duration_years INTEGER DEFAULT 3,          -- 学段持续年数
    age_range VARCHAR(20),                      -- 适用年龄范围 (如 "6-12岁")
    is_standard BOOLEAN DEFAULT TRUE,           -- 是否为标准学段
    is_active BOOLEAN DEFAULT TRUE,             -- 是否启用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 年级表增加学段编码字段（通过code关联）
ALTER TABLE public.grade_levels 
ADD COLUMN education_stage_code VARCHAR(30) REFERENCES public.education_stages(code);
```

**关联设计说明**：
- 年级表通过 `education_stage_code` 字段直接关联学段的 `code` 字段
- 不使用独立的关联表，简化数据结构
- 通过学段编码可以快速查询该学段下的所有年级
- 年级可以直接获取所属学段信息

**预定义学段配置**：

| 学段编码 | 学段名称 | 简称 | 年限 | 年龄范围 | 排序 |
|---------|---------|------|------|---------|------|
| PRIMARY | 小学 | 小 | 6 | 6-12岁 | 1 |
| MIDDLE | 初中 | 初 | 3 | 12-15岁 | 2 |
| HIGH | 高中 | 高 | 3 | 15-18岁 | 3 |
| VOCATIONAL | 职业教育 | 职 | 2-4 | 15-22岁 | 4 |
| INTERNATIONAL_IB | IB课程 | IB | 2 | 16-19岁 | 5 |
| INTERNATIONAL_AP | AP课程 | AP | 1-2 | 16-18岁 | 6 |

**API接口规范**：

```yaml
# 学段管理接口
# =================================================
# 1. 获取学段列表 (分页)
# GET /api/v1/education-stages
#   Query Parameters:
#     - page: integer (default: 1)
#     - per_page: integer (default: 10)
#     - sort_by: string (e.g., "order_level")
#     - sort_order: string (e.g., "asc" or "desc")
#   Responses:
#     200:
#       description: "成功获取学段列表"
#       content:
#         application/json:
#           schema:
#             type: object
#             properties:
#               data:
#                 type: array
#                 items:
#                   $ref: "#/components/schemas/EducationStage"
#               meta:
#                 $ref: "#/components/schemas/Pagination"

# =================================================
# 2. 创建学段
# POST /api/v1/education-stages
#   Request Body:
#     application/json:
#       schema:
#         $ref: "#/components/schemas/CreateEducationStage"
#   Responses:
#     201:
#       description: "学段创建成功"
#       content:
#         application/json:
#           schema:
#             $ref: "#/components/schemas/EducationStage"
#     400:
#       description: "请求参数错误"
#     409:
#       description: "学段编码冲突"

# =================================================
# 3. 获取单个学段详情
# GET /api/v1/education-stages/{id}
#   Path Parameters:
#     - id: UUID (学段ID)
#   Responses:
#     200:
#       description: "成功获取学段详情"
#       content:
#         application/json:
#           schema:
#             $ref: "#/components/schemas/EducationStage"
#     404:
#       description: "学段未找到"

# =================================================
# 4. 更新学段信息
# PUT /api/v1/education-stages/{id}
#   Path Parameters:
#     - id: UUID (学段ID)
#   Request Body:
#     application/json:
#       schema:
#         $ref: "#/components/schemas/UpdateEducationStage"
#   Responses:
#     200:
#       description: "学段更新成功"
#       content:
#         application/json:
#           schema:
#             $ref: "#/components/schemas/EducationStage"
#     404:
#       description: "学段未找到"

# =================================================
# 5. 删除学段 (软删除)
# DELETE /api/v1/education-stages/{id}
#   Path Parameters:
#     - id: UUID (学段ID)
#   Responses:
#     204:
#       description: "学段删除成功"
#     404:
#       description: "学段未找到"
#     409:
#       description: "学段存在关联数据，无法删除"

# =================================================
# 6. 获取学段简要信息 (用于下拉选择)
# GET /api/v1/education-stages/summaries
#   Responses:
#     200:
#       description: "成功获取学段简要信息列表"
#       content:
#         application/json:
#           schema:
#             type: array
#             items:
#               type: object
#               properties:
#                 id: UUID
#                 name: string
#                 code: string

# =================================================
# 7. 批量更新学段排序
# PATCH /api/v1/education-stages/orders
#   Request Body:
#     application/json:
#       schema:
#         type: array
#         items:
#           type: object
#           properties:
#             id: UUID
#             order_level: integer
#   Responses:
#     200:
#       description: "排序更新成功"

# =================================================
# 8. 检查学段编码可用性
# GET /api/v1/education-stages/check-code
#   Query Parameters:
#     - code: string
#   Responses:
#     200:
#       description: "编码可用"
#       content:
#         application/json:
#           schema:
#             type: object
#             properties:
#               is_available: boolean
#     409:
#       description: "编码已被占用"

# =================================================
# 年级关联查询
# =================================================
# 9. 根据学段编码获取关联年级列表
# GET /api/v1/education-stages/{code}/grades
#   Path Parameters:
#     - code: string (学段编码，如 PRIMARY, MIDDLE, HIGH)
#   Responses:
#     200:
#       description: "成功获取关联年级列表"
#       content:
#         application/json:
#           schema:
#             type: array
#             items:
#               $ref: "#/components/schemas/GradeLevel"
#     404:
#       description: "学段未找到"
```

**权限控制**：
- **查看权限**：所有用户可查看启用的学段信息
- **管理权限**：仅系统管理员可创建、修改、删除学段
- **配置权限**：租户管理员可配置本租户使用的学段范围
- **审计记录**：所有学段变更操作需记录审计日志

**业务约束**：
- **唯一性约束**：学段编码全局唯一，不可重复
- **依赖检查**：删除学段前需检查是否被年级、题库、试卷等引用
- **排序约束**：排序级别必须为正整数，相同排序级别按创建时间排序
- **状态约束**：非标准学段可以禁用，标准学段不允许禁用

**使用场景**：

1. **题库组织**：按学段对题目进行分类和难度控制
   ```
   小学数学题库 → PRIMARY + MATH
   初中物理题库 → MIDDLE + PHYSICS  
   高中生物题库 → HIGH + BIOLOGY
   ```

2. **试卷模板**：学段特定的试卷格式和评分标准
   ```
   小学期末试卷 → 题型简单，重基础
   初中中考模拟 → 综合应用，重理解
   高中高考冲刺 → 深度分析，重能力
   ```

3. **成绩分析**：学段维度的教学质量评估
   ```
   小学段：重视基础知识掌握度
   初中段：关注知识运用能力
   高中段：突出思维分析能力
   ```

**系统集成点**：
- **年级系统**：学段与年级的映射关系管理
- **学科系统**：学段特定的学科设置和难度要求
- **题库系统**：学段维度的题目分类和筛选
- **考试系统**：学段特定的考试类型和评估标准
- **分析系统**：跨学段的数据对比和趋势分析

## 3.2 班级管理体系
- **行政班**：固定班级建制，用于日常管理和考试组织
- **教学班**：按学科能力分组，支持走班制教学和分层考试
- **班级关联**：学生可同时属于行政班和多个教学班
- **考试关联**：考试可按行政班或教学班组织，支持灵活的考试安排
- **班级升学复制**：支持一键复制班级用于升学场景，默认仅提升年级，保留原班级学生及任课老师。

### 3.2.1 学校组织架构
如果是集团校，可以将多个校区合并进入一个组织
年级和学科需要与组织关联，未关联的代表所有年级和学科。
资源与考试未来会属于组织的数据，顶层会继承获得相关权限。
- 考试列表中通过学校组织架构可以快速查找相关的考试
- 教辅或者题库默认分配到顶级组织，可以更改权限，限制语文学科查看语文数据资源。
```mermaid
mindmap
    Root(学校组织)
        行政组
            一年级
                行政一班
                行政二班
            二年级
            三年级
        学科组
            语文
                一年级语文
                    语文一班
            数学
            英语
```

## 3.3 用户认证与身份绑定体系

### 3.3.1 注册与登录流程
- **手机号注册**: 用户通过手机号和短信验证码进行初次注册。此时，用户在 `public.users` 表中创建，但处于 `unverified` 状态，无任何租户身份。
- **登录方式**:
  - 主要登录方式：手机号 + 密码/验证码。
  - 备用登录方式：用户名 + 密码。

### 3.3.2 身份绑定：用户自服务模式
为最大化便利性，系统采用用户自服务绑定模式。用户注册后，可自行根据已知的个人信息（如学号、教师编号）在目标学校（租户）内搜索并绑定自己的身份。

**绑定流程概述:**
```mermaid
graph TD
    A[用户使用手机号注册/登录] --> B[进入“绑定身份"页面]
    B --> C[选择并搜索学校/租户]
    C --> D[输入个人信息进行匹配<br>(如：姓名+教师编号/学号)]
    D --> E{系统在租户内找到唯一匹配项?}
    E -->|否| F[提示用户检查信息或联系管理员]
    E -->|是| G[显示脱敏后的匹配信息<br>"是否为 张*三 (编号: ****123)?"]
    G --> H{确认身份}
    H -->|是| I[绑定成功]
    H -->|否| D
    I --> J[用户获得该身份的权限]
```

**风险与纠错机制：**
- **主要风险**: 用户可能输入他人的信息（如果恰好知道）并成功绑定到错误的身份上。
- **纠错机制**:
  - **系统/运营方解绑**: 如果发生身份误绑，受影响的用户或学校管理员可以联系平台运营方。运营方（系统超级管理员）拥有后台权限，可以强制解除用户与身份之间的绑定关系。
  - **审计日志**: 所有的绑定操作都会被详细记录，便于运营方在收到申诉时进行追溯和核实。

### 3.3.3 多身份管理
- 用户成功绑定一个或多个身份后，可以在不同租户、不同角色身份之间进行切换，该机制保持不变。

## 3.4 跨租户家长账号管理
- 家长账号同样遵循手机号注册流程。
- 家长通过学��管理员提供的邀请码或学生提供的授权方式，将其账号与一个或多个租户下的学生进行关联。

### 3.4.1 多身份绑定与切换机制
- **身份绑定**：用户登录后可绑定租户内的多个身份角色
- **家长身份**：家长可选择绑定的学生身份，支持多个子女管理
- **教师身份**：教师可在多个身份间切换（如既是学科组长又是任课老师）
- **身份展示**：界面显示格式为"机构名/姓名（学号）/[身份角色]"
- **快速切换**：提供便捷的身份切换入口，无需重新登录
- **权限继承**：切换身份时自动继承对应身份的权限范围
- **会话管理**：不同身份的操作会话独立管理，避免权限混淆
- **审计追踪**：完整记录身份切换操作和对应的数据访问行为

## 3.5 学校内部权限管理
Deep-Mate平台基于"学校-学科组-年级-教学班"四级组织架构设计了完善的动态权限控制系统，支持精细化的数据访问控制和灵活的权限委托机制。

### 3.5.1 组织架构设计

**四级组织架构体系**：
```
学校 (School)
├── 学科组 (Subject Group)
│   ├── 数学学科组
│   ├── 语文学科组
│   ├── 英语学科组
│   └── 综合学科组
├── 年级 (Grade Level)
│   ├── 高一年级
│   ├── 高二年级
│   └── 高三年级
└── 教学班 (Teaching Classes)
    ├── 高一数学A班
    ├── 高一数学B班
    ├── 高一语文强化班
    └── 跨年级特色班级
```

**组织架构特点**：
- **横向管理**：学科组负责相同学科的跨年级教学管理
- **纵向管理**：年级负责同年级的跨学科管理协调
- **交叉管理**：教学班支持跨年级、跨学科的个性化教学组织
- **灵活组合**：支持行政班与教学班的双重班级管理体系

### 3.5.2 动态权限计算机制

**权限计算公式**：
```
用户数据访问权限 = 角色基础权限 ∪ 学科组权限 ∪ 年级权限 ∪ 教学班权限 ∪ 特殊授权权限
```

**权限计算维度**：
- **角色权限**：基于用户角色的基础权限（校长、教导主任等）
- **学科组权限**：基于学科组任教关系的权限（跨年级的学科数据访问）
- **年级权限**：基于年级管理关系的权限（跨学科的年级数据访问）
- **教学班权限**：基于教学班任教关系的权限（具体班级的学科数据访问）
- **特殊授权**：临时或特殊情况下的权限授权

#### 3.5.2.1 特殊授权权限详述

**特殊授权权限定义**：
特殊授权权限是指在正常角色权限和组织架构权限之外，基于特定业务需求或紧急情况而临时授予用户的额外权限。这类权限具有时效性、可追溯性和可撤销性的特点。

**使用场景**：

1. **代理职务场景**：
   - 年级长请假期间，临时授权其他教师代理年级长职责
   - 学科组长出差时，授权副组长临时管理学科组事务
   - 班主任生病期间，授权任课老师临���管理班级考试

2. **跨部门协作场景**：
   - 联考组织需要跨年级、跨学科的协调权限
   - 教学评估期间需要临时访问其他年级的教学数据
   - 课程改革试点需要跨学科组的数据分析权限

3. **应急处理场景**：
   - 系统故障期间的临时数据访问和修复权限
   - 考试异常情况下的紧急权限调配
   - 数据安全事件的应急响应权限

4. **数据迁移场景**：
   - 学生转班转年级的权限过渡期管理
   - 教师岗位调整的权限平滑过渡
   - 组织架构调整期间的临时权限安排

**权限类型分类**：

| 权限类型   | 权限范围         | 典型用途      | 最长期限 |
|--------|--------------|-----------|------|
| 临时管理权限 | 特定组织单元的完整管理权 | 代理职务、临时负责 | 30天  |
| 跨级查看权限 | 跨组织边界的数据查看权  | 联考协调、教学评估 | 14天  |
| 应急操作权限 | 紧急情况下的特殊操作权  | 系统故障、数据恢复 | 7天   |
| 数据恢复权限 | 历史数据的访问和修复权  | 数据迁移、错误修正 | 3天   |

**管理机制**：

**权限申请流程**：
```mermaid
graph TD
    A[用户发起特殊权限申请] --> B[填写申请表单]
    B --> C[系统自动风险评估]
    C --> D{风险等级}
    D -->|低风险| E[直属上级审批]
    D -->|中风险| F[跨级审批]
    D -->|高风险| G[多级联合审批]
    E --> H[权限授予]
    F --> H
    G --> H
    H --> I[权限生效通知]
    I --> J[权限使用监控]
```

**申请表单必填字段**：
- **申请理由**：详细说明需要特殊权限的业务背景
- **权限范围**：明确需要访问的数据范围和功能权限
- **使用期限**：权限的起始时间和到期时间
- **紧急程度**：一般/紧急/特急三个级别
- **业务影响**：说明不授权可能造成的业务影响

**审批机制**：
- **直属上级审批**：权限范围在直属上级管理范围内的申请
- **跨级审批**：需要跨越组织边界的权限申请
- **多级联合审批**：涉及敏感数据或高风险操作的权限申请
- **紧急授权**：特急情况下的24小时内快速审批通道

**权限生��与监控**：
- **即时生效**：审批通过后权限立即生效
- **使用监控**：所有特殊权限的使用行为实时记录
- **异常告警**：超出申请范围的权限使用自动告警
- **定期评估**：每48小时评估权限使用的合理性

**权限到期与回收**：
- **自动到期**：权限到达设定期限自动失效
- **主动回收**：申请人或审批人可随时主动回收权限
- **强制回收**：系统检测到滥用时强制回收权限
- **延期申请**：到期前可申请延期，需重新审批

**审计和合规**：
- **完整记录**：所有特殊权限的申请、审批、使用、回收全程记录
- **定期审计**：每月对特殊权限使用情况进行审计
- **合规检查**：确保特殊权限使用符合数据保护法规要求
- **责任追溯**：权限滥用时可快速定位责任人

**动态计算特性**：
- **实时计算**：每次数据访问时实时计算用户权限范围
- **范围聚合**：自动聚合用户在不同维度的数据访问权限
- **智能过滤**：基于权限范围自动过滤查询结果
- **缓存优化**：权限计算结果缓存30分钟���变更时立即失效

### 3.5.3 权限层级设计
```
学校级：校长（全校数据）
  ├── 教导主任（全校教学数据）
  ├── 学科组级：学科组长（跨年级学科数据）
  │   ├── 数学学科组长（全校数学相关数据）
  │   ├── 语文学科组长（全校语文相关数据）
  │   └── 其他学科组长（对应学科数据）
  ├── 年级级：年级长（跨学科年级数据）
  │   ├── 高一年级长（高一全部数据）
  │   ├── 高二年级长（高二全部数据）
  │   └── 高三年级长（高三全部数据）
  └── 教学班级：班主任、任课老师（具体班级学科数据）
      ├── 班主任（本班级全部学科数据）
      ├── 数学老师（任教班级数学数据）
      ├── 语文老师（任教班级语文数据）
      └── 学生（个人相关数据）
```

### 3.5.4 权限验证流程

**多维度权限验证**：
1. **角色验证**：验证用户基础角色权限
2. **组织架构验证**：验证用户在组织架构中的职责权限
3. **任教关系验��**：验证用户与具体班级、学科的任教关系
4. **特殊授权验证**：验证临时或特殊授权权限
5. **数据范围计算**：基于上述验证结果计算最终数据访问范围

**权限验证策略**：
- **就近原则**：优先使用最具体的权限（班级权限优于年级权限）
- **权限并集**：用户拥有所有适用权限的并集
- **安全优先**：存在权限冲突时，采用最严格的权限设置
- **审计记录**：所有权限验证过程完整记录审计日志

### 3.5.5 权限矩阵详述
| 角色   | 学生信息  | 班级管理   | 考试管理  | 成绩查看  | 成绩录入  | 统计分析  |
|------|-------|--------|-------|-------|-------|-------|
| 校长   | 全校    | 全校     | 全校    | 全校    | 否     | 全校    |
| 教导主任 | 全校    | 全校     | 全校    | 全校    | 审核    | 全校    |
| 学科组长 | 本学科相关 | 本学科教学班 | 本学科考试 | 本学科成绩 | 本学科录入 | 本学科分析 |
| 年级长  | 本年级   | 本年级    | 本年级   | 本年级   | 否     | 本年级   |
| 班主任  | 本班级   | 本班级    | 本���级   | 本班级   | 否     | 本班级   |
| 任课老师 | 任教学生  | 任教教学班  | 任教学科  | 任教学科  | 任教学科  | 任教学科  |

**角色权限详细说明**：

**学科组长权限**：
- 查看全校范围内本学科的所有相关数据（跨年级）
- 管理本学科的所有教学班级设置和调整
- 创建和管理本学科的考试安排
- 查看和分析本学科的所有成绩数据
- 录入和审核本学科的考试成绩
- 生成本学科的跨年级教学分析报告

**多重身份权限聚合**：
当用户同时担任多个角色时（如既是学科组长又是年级长），系统自动聚合所有角色权限：
- **数据访问范围** = 各角色数据范围的并集
- **功能操作权限** = 各角色功能权限的并集
- **管理对象范围** = 各角色管理对象的并集

## 3.6 系统设计原则
- **高可扩展性**：支持水平扩展和垂直扩展
- **灵活配置**：支持不同组织的个性化需求
- **数据安全**：多层次数据加密和访问控制
