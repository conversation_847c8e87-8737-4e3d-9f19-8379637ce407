import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, User, Phone, Mail, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import { 
  TeacherFormProps, 
  TeacherFormData, 
  TeacherFormErrors,
  EMPLOYMENT_STATUS_OPTIONS,
  GENDER_OPTIONS,
  EDUCATION_LEVELS,
  TEACHER_TITLES,
  TEACHING_SUBJECTS_OPTIONS
} from '@/types/teacher';

const TeacherForm: React.FC<TeacherFormProps> = ({
  teacher,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formErrors, setFormErrors] = useState<TeacherFormErrors>({});
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<TeacherFormData>({
    defaultValues: {
      user_id: '',
      employee_id: '',
      name: '',
      phone: '',
      email: '',
      gender: '未知',
      employment_status: '在职',
    },
  });

  // Reset form when dialog opens/closes or teacher changes
  useEffect(() => {
    if (open) {
      if (teacher) {
        reset({
          user_id: teacher.user_id,
          employee_id: teacher.employee_id,
          name: teacher.name,
          phone: teacher.phone || '',
          email: teacher.email || '',
          gender: teacher.gender || '未知',
          date_of_birth: teacher.date_of_birth ? teacher.date_of_birth.split('T')[0] : '',
          id_card_number: teacher.id_card_number || '',
          highest_education: teacher.highest_education || '',
          graduation_school: teacher.graduation_school || '',
          major: teacher.major || '',
          hire_date: teacher.hire_date ? teacher.hire_date.split('T')[0] : '',
          employment_status: teacher.employment_status || '在职',
          title: teacher.title || '',
          office_location: teacher.office_location || '',
          bio: teacher.bio || '',
        });
        setSelectedSubjects(teacher.teaching_subjects || []);
      } else {
        reset({
          user_id: '',
          employee_id: '',
          name: '',
          phone: '',
          email: '',
          gender: '未知',
          employment_status: '在职',
        });
        setSelectedSubjects([]);
      }
      setFormErrors({});
    }
  }, [open, teacher, reset]);

  const handleSubjectToggle = (subject: string) => {
    const newSubjects = selectedSubjects.includes(subject)
      ? selectedSubjects.filter(s => s !== subject)
      : [...selectedSubjects, subject];
    setSelectedSubjects(newSubjects);
  };

  const handleFormSubmit = async (data: TeacherFormData) => {
    try {
      // Add selected subjects to form data
      const formDataWithSubjects = {
        ...data,
        teaching_subjects: selectedSubjects.length > 0 ? selectedSubjects : undefined,
      };
      
      await onSubmit(formDataWithSubjects);
    } catch (error: any) {
      // Handle validation errors
      if (error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      }
    }
  };

  const isEditMode = !!teacher;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            {isEditMode ? '编辑教师' : '新增教师'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium border-b pb-2">基本信息</h3>
              
              {/* Employee ID */}
              <div className="space-y-2">
                <Label htmlFor="employee_id" className="text-sm font-medium">
                  工号 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="employee_id"
                  {...register('employee_id', {
                    required: '请输入工号',
                    minLength: { value: 2, message: '工号至少需要2个字符' },
                    maxLength: { value: 50, message: '工号不能超过50个字符' }
                  })}
                  placeholder="请输入工号"
                  disabled={loading}
                  className={errors.employee_id || formErrors.employee_id ? "border-red-500" : ""}
                />
                {errors.employee_id && (
                  <p className="text-sm text-red-500">{errors.employee_id.message}</p>
                )}
                {formErrors.employee_id && (
                  <p className="text-sm text-red-500">{formErrors.employee_id}</p>
                )}
              </div>

              {/* Teacher Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  姓名 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  {...register('name', {
                    required: '请输入教师姓名',
                    minLength: { value: 2, message: '姓名至少需要2个字符' },
                    maxLength: { value: 100, message: '姓名不能超过100个字符' }
                  })}
                  placeholder="请输入教师姓名"
                  disabled={loading}
                  className={errors.name || formErrors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
                {formErrors.name && (
                  <p className="text-sm text-red-500">{formErrors.name}</p>
                )}
              </div>

              {/* Phone */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-medium flex items-center">
                  <Phone className="w-4 h-4 mr-1" />
                  联系电话
                </Label>
                <Input
                  id="phone"
                  {...register('phone', {
                    pattern: {
                      value: /^1[3-9]\d{9}$/,
                      message: '请输入有效的手机号码'
                    }
                  })}
                  placeholder="请输入手机号码"
                  disabled={loading}
                  className={errors.phone || formErrors.phone ? "border-red-500" : ""}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone.message}</p>
                )}
                {formErrors.phone && (
                  <p className="text-sm text-red-500">{formErrors.phone}</p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium flex items-center">
                  <Mail className="w-4 h-4 mr-1" />
                  邮箱地址
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email', {
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: '请输入有效的邮箱地址'
                    }
                  })}
                  placeholder="请输入邮箱地址"
                  disabled={loading}
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">性别</Label>
                <Select 
                  value={watch('gender') || ''}
                  onValueChange={(value) => setValue('gender', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择性别" />
                  </SelectTrigger>
                  <SelectContent>
                    {GENDER_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Professional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium border-b pb-2">职业信息</h3>
              
              {/* Employment Status */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">在职状态</Label>
                <Select 
                  value={watch('employment_status') || 'active'}
                  onValueChange={(value) => setValue('employment_status', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {EMPLOYMENT_STATUS_OPTIONS.map(status => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Title */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">职称</Label>
                <Select 
                  value={watch('title') || ''}
                  onValueChange={(value) => setValue('title', value === '' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择职称" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">未设置</SelectItem>
                    {TEACHER_TITLES.map(title => (
                      <SelectItem key={title.value} value={title.value}>
                        {title.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Education */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">最高学历</Label>
                <Select 
                  value={watch('highest_education') || ''}
                  onValueChange={(value) => setValue('highest_education', value === '' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择学历" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">未设置</SelectItem>
                    {EDUCATION_LEVELS.map(level => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Hire Date */}
              <div className="space-y-2">
                <Label htmlFor="hire_date" className="text-sm font-medium flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  入职日期
                </Label>
                <Input
                  id="hire_date"
                  type="date"
                  {...register('hire_date')}
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          {/* Teaching Subjects */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">任教学科</Label>
            <div className="grid grid-cols-4 gap-2 p-3 border rounded">
              {TEACHING_SUBJECTS_OPTIONS.map((subject) => (
                <label
                  key={subject.value}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedSubjects.includes(subject.value)}
                    onChange={() => handleSubjectToggle(subject.value)}
                    className="rounded"
                  />
                  <span className="text-sm">{subject.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Bio */}
          <div className="space-y-2">
            <Label htmlFor="bio" className="text-sm font-medium">
              个人简介
            </Label>
            <Textarea
              id="bio"
              {...register('bio')}
              placeholder="可选，输入教师的个人简介..."
              disabled={loading}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditMode ? '更新' : '创建'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default TeacherForm;