import React, { useEffect, useState } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import {
  Dialog,
  DialogContent,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Mail, Calendar } from 'lucide-react';

import {
  TeacherFormProps,
  EMPLOYMENT_STATUS_OPTIONS,
  GENDER_OPTIONS,
  EDUCATION_LEVELS,
  TEACHER_TITLES,
  CreateTeacherParams
} from '@/types/teacher';

const TeacherForm: React.FC<TeacherFormProps> = ({
                                                   teacher,
                                                   open,
                                                   onClose,
                                                   onSubmit,
                                                   loading = false,
                                                 }) => {
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);

  // 定义表单默认值
  const defaultValues = {
    id: '',
    user_id: '',
    employee_id: '',
    name: '',
    phone: '',
    email: '',
    gender: '' as string | undefined,
    date_of_birth: undefined as string | undefined,
    id_card_number: '',
    employment_status: undefined as string | undefined,
    title: undefined as string | undefined,
    highest_education: undefined as string | undefined,
    hire_date: undefined as string | undefined,
    graduation_school: '',
    is_active: true,
    bio: '',
    teaching_subjects: [] as string[],
    tenant_id: '',
    major: '',
    homeroom_class_id: undefined as number | undefined,
    grade_level_id: undefined as number | undefined,
    subject_group_id: undefined as number | undefined,
    office_location: ''
  };
  
  // 定义表单数据类型
  type FormData = typeof defaultValues;

  const {
    control,
    handleSubmit,
    reset,
    // setValue
  } = useForm<typeof defaultValues>({
    defaultValues
  });

  // Reset form when dialog opens/closes or teacher changes
  useEffect(() => {
    if (open) {
      if (teacher) {
        const formData = {
          ...defaultValues,
          ...teacher,
          teaching_subjects: teacher.teaching_subjects || [],
        } as FormData;
        reset(formData);
        setSelectedSubjects(teacher.teaching_subjects || []);
      } else {
        reset({
          ...defaultValues,
          name: '',
          phone: '',
          email: '',
          employee_id: '',
          user_id: '',
        });
        setSelectedSubjects([]);
      }
      setFormErrors({});
    }
  }, [open, teacher, reset]);

  const handleFormSubmit = async (data: FormData) => {
    try {
      // 确保必填字段有值
      const formData: CreateTeacherParams = {
          id:data.id || '',
        employee_id: data.employee_id || '',
        name: data.name || '',
        phone: data.phone,
        email: data.email,
        gender: data.gender,
        date_of_birth: data.date_of_birth,
        id_card_number: data.id_card_number,
        highest_education: data.highest_education,
        graduation_school: data.graduation_school,
        major: data.major,
        hire_date: data.hire_date,
        employment_status: data.employment_status,
        title: data.title,
        teaching_subjects: selectedSubjects.length > 0 ? selectedSubjects : undefined,
        homeroom_class_id: data.homeroom_class_id,
        grade_level_id: data.grade_level_id,
        subject_group_id: data.subject_group_id,
        office_location: data.office_location,
        bio: data.bio,
        is_active: true
      };

      await onSubmit(formData);
    } catch (error: any) {
      if (error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      }
    }
  };

  const isEditMode = !!teacher;

  return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">

          <form onSubmit={handleSubmit(handleFormSubmit)}>
            {/* 使用网格布局实现左右分栏 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">基本信息</h3>

                <div className="space-y-2">
                  <Label htmlFor="employee_id" className="text-sm font-medium">
                    工号 <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                      name="employee_id"
                      control={control}
                      rules={{
                        required: '请输入工号',
                        minLength: { value: 2, message: '工号至少需要2个字符' },
                        maxLength: { value: 50, message: '工号不能超过50个字符' }
                      }}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="employee_id"
                                {...field}
                                placeholder="请输入工号"
                                disabled={loading}
                                className={fieldState.error || formErrors.employee_id ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.employee_id && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.employee_id}</p>
                            )}
                          </>
                      )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium">
                    姓名 <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                      name="name"
                      control={control}
                      rules={{
                        required: '请输入教师姓名',
                        minLength: { value: 2, message: '姓名至少需要2个字符' },
                        maxLength: { value: 100, message: '姓名不能超过100个字符' }
                      }}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="name"
                                {...field}
                                placeholder="请输入教师姓名"
                                disabled={loading}
                                className={fieldState.error || formErrors.name ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.name && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.name}</p>
                            )}
                          </>
                      )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-sm font-medium">
                    联系电话 <span className="text-red-500">*</span>
                  </Label>
                  <Controller
                      name="phone"
                      control={control}
                      rules={{
                        required: '请输入教师电话',
                        pattern: {
                          value: /^1[3-9]\d{9}$/,
                          message: '请输入有效的手机号码'
                        }
                      }}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="phone"
                                {...field}
                                placeholder="请输入手机号码"
                                disabled={loading}
                                className={fieldState.error || formErrors.phone ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.phone && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.phone}</p>
                            )}
                          </>
                      )}
                  />
                </div>

                {/* 邮箱地址 */}
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium flex items-center">
                    <Mail className="w-4 h-4 mr-1" />
                    邮箱地址
                  </Label>
                  <Controller
                      name="email"
                      control={control}
                      rules={{
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: '请输入有效的邮箱地址'
                        }
                      }}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="email"
                                type="email"
                                {...field}
                                placeholder="请输入邮箱地址"
                                disabled={loading}
                                className={fieldState.error || formErrors.email ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.email && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.email}</p>
                            )}
                          </>
                      )}
                  />
                </div>

                {/* 性别 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">性别</Label>
                  <Controller
                      name="gender"
                      control={control}
                      render={({ field }) => (
                          <Select
                              value={field.value}
                              onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="请选择性别" />
                            </SelectTrigger>
                            <SelectContent>
                              {GENDER_OPTIONS.map(option => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                      )}
                  />
                </div>

                {/* 工作单位 */}
                <div className="space-y-2">
                  <Label htmlFor="office_location" className="text-sm font-medium">
                    工作单位
                  </Label>
                  <Controller
                      name="office_location"
                      control={control}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="office_location"
                                {...field}
                                placeholder="请输入工作单位"
                                disabled={loading}
                                className={fieldState.error || formErrors.office_location ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.office_location && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.office_location}</p>
                            )}
                          </>
                      )}
                  />
                </div>

                {/* 出生日期 */}
                <div className="space-y-2">
                  <Label htmlFor="date_of_birth" className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    出生日期
                  </Label>
                  <Controller
                      name="date_of_birth"
                      control={control}
                      render={({ field }) => (
                          <Input
                              id="date_of_birth"
                              type="date"
                              value={field.value || ''}
                              onChange={(e) => field.onChange(e.target.value || undefined)}
                              disabled={loading}
                              max={new Date().toISOString().split('T')[0]}
                          />
                      )}
                  />
                </div>

                {/* 身份证号 */}
                <div className="space-y-2">
                  <Label htmlFor="id_card_number" className="text-sm font-medium">
                    身份证号
                  </Label>
                  <Controller
                      name="id_card_number"
                      control={control}
                      rules={{
                        pattern: {
                          value: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
                          message: '请输入有效的身份证号'
                        }
                      }}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="id_card_number"
                                {...field}
                                placeholder="请输入身份证号"
                                disabled={loading}
                                className={fieldState.error || formErrors.id_card_number ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.id_card_number && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.id_card_number}</p>
                            )}
                          </>
                      )}
                  />
                </div>
              </div>

              {/* 右侧：职业信息 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">职业信息</h3>

                {/* 在职状态 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">在职状态</Label>
                  <Controller
                      name="employment_status"
                      control={control}
                      defaultValue={'在职'}
                      render={({ field }) => (
                          <Select
                              value={field.value}
                              onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="请选择状态" />
                            </SelectTrigger>
                            <SelectContent>
                              {EMPLOYMENT_STATUS_OPTIONS.map(status => (
                                  <SelectItem key={status.value} value={status.value}>
                                    {status.label}
                                  </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                      )}
                  />
                </div>

                {/* 职称 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">职称</Label>
                  <Controller
                      name="title"
                      control={control}
                      defaultValue={undefined}
                      render={({ field }) => (
                          <Select
                              value={field.value || 'not_set'}
                              onValueChange={(value) => field.onChange(value === 'not_set' ? undefined : value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="请选择职称" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_set">未设置</SelectItem>
                              {TEACHER_TITLES.map((title: { value: string; label: string }) => (
                                  <SelectItem key={title.value} value={title.value}>
                                    {title.label}
                                  </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                      )}
                  />
                </div>

                {/* 最高学历 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">最高学历</Label>
                  <Controller
                      name="highest_education"
                      control={control}
                      defaultValue={undefined}
                      render={({ field }) => (
                          <Select
                              value={field.value || 'not_set'}
                              onValueChange={(value) => field.onChange(value === 'not_set' ? undefined : value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="请选择学历" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_set">未设置</SelectItem>
                              {EDUCATION_LEVELS.map(level => (
                                  <SelectItem key={level.value} value={level.value}>
                                    {level.label}
                                  </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                      )}
                  />
                </div>

                {/* 入职日期 */}
                <div className="space-y-2">
                  <Label htmlFor="hire_date" className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    入职日期
                  </Label>
                  <Controller
                      name="hire_date"
                      control={control}
                      defaultValue={undefined}
                      render={({ field }) => (
                          <Input
                              id="hire_date"
                              type="date"
                              value={field.value || ''}
                              onChange={(e) => field.onChange(e.target.value || undefined)}
                              disabled={loading}
                              max={new Date().toISOString().split('T')[0]}
                          />
                      )}
                  />
                </div>

                {/* 毕业学校 */}
                <div className="space-y-2">
                  <Label htmlFor="graduation_school" className="text-sm font-medium">
                    毕业学校
                  </Label>
                  <Controller
                      name="graduation_school"
                      control={control}
                      render={({ field, fieldState }) => (
                          <>
                            <Input
                                id="graduation_school"
                                {...field}
                                placeholder="请输入毕业学校"
                                disabled={loading}
                                className={fieldState.error || formErrors.graduation_school ? "border-red-500" : ""}
                            />
                            {fieldState.error && (
                                <p className="text-sm text-red-500">{fieldState.error.message}</p>
                            )}
                            {formErrors.graduation_school && !fieldState.error && (
                                <p className="text-sm text-red-500">{formErrors.graduation_school}</p>
                            )}
                          </>
                      )}
                  />
                </div>

                {/* 激活状态 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">激活状态</Label>
                  <Controller
                      name="is_active"
                      control={control}
                      render={({ field }) => (
                          <Select
                              value={field.value ? 'active' : 'inactive'}
                              onValueChange={(value) => field.onChange(value === 'active')}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="请选择激活状态" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="active">启用</SelectItem>
                              <SelectItem value="inactive">禁用</SelectItem>
                            </SelectContent>
                          </Select>
                      )}
                  />
                </div>
              </div>
            </div>

            {/* 底部居中按钮 */}
            <DialogFooter className="flex justify-center">
              <div className="flex space-x-4">
                <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    disabled={loading}
                >
                  取消
                </Button>
                <Button
                    type="submit"
                    disabled={loading}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEditMode ? '更新' : '创建'}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
  );
};

export default TeacherForm;