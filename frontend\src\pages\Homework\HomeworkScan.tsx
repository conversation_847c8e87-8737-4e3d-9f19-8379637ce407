import React, { useState, useEffect } from "react";
import {
  scan<PERSON><PERSON>,
  type Batch,
  type BatchImage,
  type ScanStats,
  type StudentSearchResult,
} from "@/services/scanApi";
import ImageOperationDialog from "./components/ImageOperationDialog";
import DeleteConfirmDialog from "./components/DeleteConfirmDialog";
import StudentImageDialog from "./components/StudentImageDialog";
import PaginationComponent from "@/components/Pagination";
import DraggableImage from "@/components/DraggableImage";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  RefreshCw,
  Image as ImageIcon,
  FileImage,
  CheckCircle2,
  AlertTriangle,
  Search,
  Upload,
  Edit,
  Trash2,
  Eye,
  Plus,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
} from "lucide-react";

// mock scan stats
const mockScanStats: ScanStats = {
  total_scanned: 10,
  pending_processing: 2,
  processing_complete: 8,
  exception_count: 1,
};

// mock paper scans
type PaperScan = {
  id: string;
  student_id: string;
  scan_time: string;
  paper_images: string[];
  scan_quality: string;
  scanner_device: string;
  scan_status: string;
};
const mockPaperScans: PaperScan[] = [
  {
    id: "scan-1",
    student_id: "stu-001",
    scan_time: "2024-07-21T10:00:00",
    paper_images: ["img1", "img2", "img3"],
    scan_quality: "优",
    scanner_device: "设备A",
    scan_status: "已完成",
  },
  {
    id: "scan-2",
    student_id: "stu-002",
    scan_time: "2024-07-21T10:05:00",
    paper_images: ["img1", "img2"],
    scan_quality: "良",
    scanner_device: "设备B",
    scan_status: "异常",
  },
];

// mock student scan data
const mockStudentScans = [
  {
    id: "stu-001",
    name: "张三",
    class: "初一1班",
    student_no: "2024001",
    school: "第一中学",
    review_status: "已阅",
    exception_status: "正常",
    objective_score: 45,
    subjective_score: 40,
    total_score: 85,
    images: ["img1", "img2"],
  },
  {
    id: "stu-002",
    name: "李四",
    class: "初一1班",
    student_no: "2024002",
    school: "第一中学",
    review_status: "未阅",
    exception_status: "异常",
    objective_score: 30,
    subjective_score: 20,
    total_score: 50,
    images: ["img3", "img4"],
  },
];

// mock batch data
const mockBatches: Batch[] = [
  {
    batch_no: "2024-07-21-01",
    page_count: 2,
    scan_time: "2024-07-21 10:00",
    scanner: "扫描员A",
    image_count: 40,
    status: "completed",
    created_at: "2024-07-21T10:00:00Z",
    updated_at: "2024-07-21T10:00:00Z",
    images: [
      {
        id: "img1",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047_origin/Doc1750906124_19.jpg",
        page_number: 1,
        student_id: "2024001",
        student_name: "张三",
        status: "normal",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
      {
        id: "img2",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047/Doc1750906124_-19.jpg",
        page_number: 2,
        student_id: "2024001",
        student_name: "张三",
        status: "normal",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
      {
        id: "img3",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047_origin/Doc1750906124_19.jpg",
        page_number: 1,
        student_id: "2024002",
        student_name: "李四",
        status: "ocr_error",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
      {
        id: "img4",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047/Doc1750906124_-19.jpg",
        page_number: 2,
        student_id: "2024002",
        student_name: "李四",
        status: "blank",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
      {
        id: "img5",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047_origin/Doc1750906124_19.jpg",
        page_number: 1,
        student_id: "2024003",
        student_name: "王五",
        status: "duplicate",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
      {
        id: "img6",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047/Doc1750906124_-19.jpg",
        page_number: 2,
        student_id: "2024003",
        student_name: "王五",
        status: "pending",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
    ],
  },
  {
    batch_no: "2024-07-21-02",
    page_count: 2,
    scan_time: "2024-07-21 11:00",
    scanner: "扫描员B",
    image_count: 30,
    status: "processing",
    created_at: "2024-07-21T11:00:00Z",
    updated_at: "2024-07-21T11:00:00Z",
    images: [
      {
        id: "img5",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047_origin/Doc1750906124_19.jpg",
        page_number: 1,
        student_id: "2024001",
        student_name: "王五",
        created_at: "2024-07-21T11:00:00Z",
        updated_at: "2024-07-21T11:00:00Z",
      },
      {
        id: "img6",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047/Doc1750906124_-19.jpg",
        page_number: 2,
        student_id: "2024003",
        student_name: "王五",
        created_at: "2024-07-21T11:00:00Z",
        updated_at: "2024-07-21T11:00:00Z",
      },
    ],
  },
];
const mockStudentList = [
  {
    id: "stu-001",
    name: "张三",
    class: "初一1班",
    student_no: "2024001",
    school: "第一中学",
    review_status: "已阅",
    exception_status: "正常",
    objective_score: 45,
    subjective_score: 40,
    total_score: 85,
  },
  {
    id: "stu-002",
    name: "李四",
    class: "初一1班",
    student_no: "2024002",
    school: "第一中学",
    review_status: "未阅",
    exception_status: "异常",
    objective_score: 30,
    subjective_score: 20,
    total_score: 50,
  },
];

const getQualityBadge = (quality: string) => quality;
const getStatusBadge = (status: string) => status;

const HomeworkScan: React.FC = () => {
  const [scanStats] = useState<ScanStats>(mockScanStats);
  const [paperScans, setPaperScans] = useState<PaperScan[]>(mockPaperScans);
  const loadPaperScans = () => setPaperScans(mockPaperScans);
  const [studentScans, setStudentScans] = useState(mockStudentList);
  const [batches, setBatches] = useState(mockBatches);
  const [filters, setFilters] = useState({
    student_no: "",
    name: "",
    class: "",
    review_status: "",
    exception_status: "",
    is_exception: "",
  });
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);

  // 新增状态
  const [batchSearchQuery, setBatchSearchQuery] = useState("");
  const [selectedBatch, setSelectedBatch] = useState<string | null>(null);
  const [cardsPerRow, setCardsPerRow] = useState(4);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);

  // 图片操作相关状态
  const [operationDialogOpen, setOperationDialogOpen] = useState(false);
  const [operationType, setOperationType] = useState<
    "replace" | "edit_page" | "edit_student" | "delete" | "add" | null
  >(null);
  const [currentOperationImage, setCurrentOperationImage] =
    useState<BatchImage | null>(null);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);

  // 表单状态
  const [editPageNumber, setEditPageNumber] = useState<number>(1);
  const [editStudentId, setEditStudentId] = useState<string>("");
  const [editStudentName, setEditStudentName] = useState<string>("");
  const [isMarkingError, setIsMarkingError] = useState<boolean>(false);
  const [studentSearchResults, setStudentSearchResults] = useState<
    StudentSearchResult[]
  >([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

  // 批次操作相关状态
  const [batchDeleteConfirmOpen, setBatchDeleteConfirmOpen] = useState(false);
  const [currentBatchToDelete, setCurrentBatchToDelete] = useState<
    string | null
  >(null);

  // 批次详情筛选和配置
  const [imageStatusFilter, setImageStatusFilter] = useState("all");
  const [imagesPerRow, setImagesPerRow] = useState(2);

  // 图片预览相关状态
  const [previewImageIndex, setPreviewImageIndex] = useState(0);
  const [previewImages, setPreviewImages] = useState<BatchImage[]>([]);
  const [zoomLevel, setZoomLevel] = useState(1);

  // 学生图片详情弹窗状态
  const [studentImageDialogOpen, setStudentImageDialogOpen] = useState(false);
  const [currentStudentImages, setCurrentStudentImages] = useState<
    BatchImage[]
  >([]);
  const [currentStudentInfo, setCurrentStudentInfo] = useState<{
    name: string;
    id: string;
    class: string;
  } | null>(null);

  // 分页状态
  const [batchPage, setBatchPage] = useState(1);
  const [batchPageSize, setBatchPageSize] = useState(5);
  const [studentPage, setStudentPage] = useState(1);
  const [studentPageSize, setStudentPageSize] = useState(20);

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ ...filters, [key]: value });
  };

  const filteredScans = studentScans.filter((scan) => {
    return (
      (filters.student_no === "" ||
        scan.student_no.includes(filters.student_no)) &&
      (filters.name === "" || scan.name.includes(filters.name)) &&
      (filters.class === "" || scan.class === filters.class) &&
      (filters.review_status === "all" ||
        scan.review_status === filters.review_status) &&
      (filters.exception_status === "all" ||
        scan.exception_status === filters.exception_status) &&
      (filters.is_exception === "all" ||
        (filters.is_exception === "是"
          ? scan.exception_status === "异常"
          : scan.exception_status !== "异常"))
    );
  });

  // 学生分页数据
  const studentTotal = filteredScans.length;
  const studentStartIndex = (studentPage - 1) * studentPageSize;
  const studentEndIndex = studentStartIndex + studentPageSize;

  const handleShowImages = (images: string[]) => {
    setCurrentImages(images);
    setImageDialogOpen(true);
  };

  const handleRefreshBatches = () => {
    setBatches([...mockBatches]);
  };

  // 批次搜索过滤
  const filteredBatches = batches.filter((batch) => {
    return (
      batchSearchQuery === "" ||
      batch.batch_no.toLowerCase().includes(batchSearchQuery.toLowerCase()) ||
      batch.scanner.toLowerCase().includes(batchSearchQuery.toLowerCase())
    );
  });

  // 批次分页数据
  const batchTotal = filteredBatches.length;
  const batchStartIndex = (batchPage - 1) * batchPageSize;
  const batchEndIndex = batchStartIndex + batchPageSize;
  const paginatedBatches = filteredBatches.slice(
    batchStartIndex,
    batchEndIndex
  );

  // 处理批次选中
  const handleBatchSelect = (batchNo: string) => {
    setSelectedBatch(selectedBatch === batchNo ? null : batchNo);
  };

  // 图片操作处理函数
  const handleImageOperation = (
    type: "replace" | "edit_page" | "edit_student" | "delete" | "add",
    image?: BatchImage
  ) => {
    if (type === "delete") {
      setCurrentOperationImage(image || null);
      setDeleteConfirmOpen(true);
    } else {
      setOperationType(type);
      setCurrentOperationImage(image || null);
      if (type === "edit_page" && image) {
        setEditPageNumber(image.page_number);
      }
      if (type === "edit_student" && image) {
        setEditStudentId(image.student_id || "");
        setEditStudentName(image.student_name || "");
      }
      setOperationDialogOpen(true);
    }
  };

  // 文件上传处理
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 保存操作
  const handleSaveOperation = () => {
    // 这里应该调用API保存操作
    console.log("保存操作:", operationType, currentOperationImage, uploadFile);
    setOperationDialogOpen(false);
    setOperationType(null);
    setCurrentOperationImage(null);
    setUploadFile(null);
    setUploadPreview(null);
  };

  // 批次重置OCR
  const handleBatchResetOCR = (batchNo: string) => {
    // 这里应该调用API重置OCR
    console.log("重置OCR:", batchNo);
    // 显示成功提示
    alert("OCR重置成功");
  };

  // 批次删除确认
  const handleBatchDeleteConfirm = (batchNo: string) => {
    setCurrentBatchToDelete(batchNo);
    setBatchDeleteConfirmOpen(true);
  };

  // 执行批次删除
  const handleBatchDelete = () => {
    if (currentBatchToDelete) {
      // 这里应该调用API删除批次
      console.log("删除批次:", currentBatchToDelete);
      setBatches(batches.filter((b) => b.batch_no !== currentBatchToDelete));
      setBatchDeleteConfirmOpen(false);
      setCurrentBatchToDelete(null);
    }
  };

  // 打开图片预览
  const handleImagePreview = (image: BatchImage, allImages: BatchImage[]) => {
    const index = allImages.findIndex((img) => img.id === image.id);
    setPreviewImages(allImages);
    setPreviewImageIndex(index);
    setPreviewImageUrl(image.url);
    setZoomLevel(1);
  };

  // 切换到上一张图片
  const handlePrevImage = () => {
    if (previewImageIndex > 0) {
      const newIndex = previewImageIndex - 1;
      setPreviewImageIndex(newIndex);
      setPreviewImageUrl(previewImages[newIndex].url);
      setZoomLevel(1);
    }
  };

  // 切换到下一张图片
  const handleNextImage = () => {
    if (previewImageIndex < previewImages.length - 1) {
      const newIndex = previewImageIndex + 1;
      setPreviewImageIndex(newIndex);
      setPreviewImageUrl(previewImages[newIndex].url);
      setZoomLevel(1);
    }
  };

  // 处理滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    setZoomLevel((prev) => Math.max(0.5, Math.min(3, prev + delta)));
  };

  // 打开学生图片详情
  const handleShowStudentImages = (student: any) => {
    // Mock学生图片数据
    const mockStudentImages: BatchImage[] = [
      {
        id: "student_img1",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047_origin/Doc1750906124_19.jpg",
        page_number: 1,
        student_id: student.student_no,
        student_name: student.name,
        status: "normal",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
      {
        id: "student_img2",
        url: "http://202.116.234.4:8173/static/image/1750905904857/685cb526b8e82f7b93053047/Doc1750906124_-19.jpg",
        page_number: 2,
        student_id: student.student_no,
        student_name: student.name,
        status: "ocr_error",
        created_at: "2024-07-21T10:00:00Z",
        updated_at: "2024-07-21T10:00:00Z",
      },
    ];

    setCurrentStudentImages(mockStudentImages);
    setCurrentStudentInfo({
      name: student.name,
      id: student.student_no,
      class: student.class,
    });
    setStudentImageDialogOpen(true);
  };

  // 处理学生查询
  const handleSearchStudents = () => {
    // 这里应该调用API进行学生查询
    console.log("查询学生:", filters);
    // 重置分页到第一页
    setStudentPage(1);
  };

  // 处理生成任务
  const handleGenerateTask = () => {
    // 这里应该调用API生成任务
    console.log("生成任务");
    alert("任务生成成功");
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已扫描</CardTitle>
            <FileImage className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scanStats?.total_scanned || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理中</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scanStats?.pending_processing || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">处理完成</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scanStats?.processing_complete || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">扫描异常</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {scanStats?.exception_count || 0}
            </div>
          </CardContent>
        </Card>
      </div>
      <Tabs defaultValue="preview" className="w-full">
        <TabsList className="grid w-96 grid-cols-2 mb-4">
          <TabsTrigger value="preview">扫描预览</TabsTrigger>
          <TabsTrigger value="student">学生列表</TabsTrigger>
        </TabsList>
        {/* 批次扫描预览 */}
        <TabsContent
          value="preview"
          className="space-y-4"
          style={{ marginTop: "10px" }}
        >
          {/* 搜索和刷新控制栏 */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索批次号"
                value={batchSearchQuery}
                onChange={(e) => setBatchSearchQuery(e.target.value)}
                className="w-64"
              />
            </div>
            <Button variant="outline" onClick={handleRefreshBatches}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            {/* <div className="flex items-center gap-2">
              <Settings className="h-4 w-4 text-muted-foreground" />
              <Select value={cardsPerRow.toString()} onValueChange={(v) => setCardsPerRow(Number(v))}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2">2列</SelectItem>
                  <SelectItem value="3">3列</SelectItem>
                  <SelectItem value="4">4列</SelectItem>
                  <SelectItem value="6">6列</SelectItem>
                </SelectContent>
              </Select>
            </div> */}
          </div>

          {/* 批次卡片网格 */}
          <div
            className="grid gap-4"
            style={{
              gridTemplateColumns: `repeat(${cardsPerRow}, 1fr)`,
            }}
          >
            {paginatedBatches.map((batch) => (
              <Card
                key={batch.batch_no}
                className={`p-4 cursor-pointer transition-all duration-200 hover:bg-muted/50 hover:shadow-md relative group ${
                  selectedBatch === batch.batch_no
                    ? "border-blue-500 border-2"
                    : ""
                }`}
                onClick={() => handleBatchSelect(batch.batch_no)}
              >
                {/* 操作按钮 */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBatchResetOCR(batch.batch_no);
                        }}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>重置OCR</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleBatchDeleteConfirm(batch.batch_no);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>批量删除</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                <CardHeader className="p-0 pb-1">
                  <CardTitle className="text-base font-semibold">
                    批次号：{batch.batch_no}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0 space-y-1">
                  <div className="text-sm text-muted-foreground">
                    总张数：{batch.image_count} 张
                  </div>
                  <div className="text-sm text-muted-foreground">
                    扫描时间：{batch.scan_time}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    状态：{batch.scanner}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 批次详情展开区域 */}
          {selectedBatch && (
            <div className="mt-6 p-4 border rounded-lg bg-muted/20">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">
                    批次详情：{selectedBatch}
                  </h3>
                  <Button variant="outline" className="ml-3">
                    重新同步
                  </Button>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedBatch(null)}
                >
                  收起
                </Button>
              </div>

              {(() => {
                const batch = batches.find((b) => b.batch_no === selectedBatch);
                if (!batch) return null;

                // 筛选图片
                const filteredImages = batch.images.filter((image) => {
                  if (imageStatusFilter === "all") return true;
                  return image.status === imageStatusFilter;
                });

                // 将图片两张一组
                const imageGroups = [];
                for (let i = 0; i < filteredImages.length; i += 2) {
                  imageGroups.push(filteredImages.slice(i, i + 2));
                }

                return (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
                      <div>
                        <span className="font-medium">总张数：</span>
                        <span>{batch.image_count} 张</span>
                      </div>
                      <div>
                        <span className="font-medium">扫描时间：</span>
                        <span>{batch.scan_time}</span>
                      </div>
                      <div>
                        <span className="font-medium">扫描员：</span>
                        <span>{batch.scanner}</span>
                      </div>
                    </div>

                    {/* 图片预览区域 */}
                    <div className="border-t pt-4">
                      <div className="flex items-center justify-center mb-4">
                        <div className="flex items-center gap-4">
                          {/* 状态筛选 */}
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                              状态:
                            </span>
                            <Select
                              value={imageStatusFilter}
                              onValueChange={setImageStatusFilter}
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">全部</SelectItem>
                                <SelectItem value="normal">正常</SelectItem>
                                <SelectItem value="ocr_error">
                                  OCR识别异常
                                </SelectItem>
                                <SelectItem value="blank">空白页</SelectItem>
                                <SelectItem value="duplicate">
                                  重复页
                                </SelectItem>
                                <SelectItem value="pending">待分发</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* 每行显示组数配置 */}
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                              每行:
                            </span>
                            <Select
                              value={imagesPerRow.toString()}
                              onValueChange={(v) => setImagesPerRow(Number(v))}
                            >
                              <SelectTrigger className="w-20">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">1组</SelectItem>
                                <SelectItem value="2">2组</SelectItem>
                                <SelectItem value="3">3组</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* 批次分页 */}
                          <div>
                            <PaginationComponent
                              total={batchTotal}
                              current={batchPage}
                              pageSize={batchPageSize}
                              onChange={(page, size) => {
                                setBatchPage(page);
                                setBatchPageSize(size);
                              }}
                              showSizeChanger={true}
                              showTotal={true}
                            />
                          </div>
                        </div>
                      </div>

                      <div
                        className="grid gap-4"
                        style={{
                          gridTemplateColumns: `repeat(${imagesPerRow}, 1fr)`,
                        }}
                      >
                        {imageGroups.map((group, groupIndex) => (
                          <Card key={groupIndex} className="p-4 shadow-md">
                            <div className="flex gap-2">
                              {group.map((image, imageIndex) => (
                                <div key={image.id} className="flex-1">
                                  <div
                                    className="relative h-full border rounded-lg overflow-hidden group cursor-pointer"
                                    onClick={() =>
                                      handleImagePreview(image, filteredImages)
                                    }
                                  >
                                    <img
                                      src={image.url}
                                      alt={`页面 ${image.page_number}`}
                                      className="w-full h-full object-cover"
                                    />

                                    {/* 图片信息覆盖层 */}
                                    <div
                                      className={`absolute bottom-0 left-0 right-0 text-white p-2 text-xs ${
                                        image.status === "normal"
                                          ? "bg-green-600/80"
                                          : image.status === "ocr_error"
                                          ? "bg-red-600/80"
                                          : image.status === "blank"
                                          ? "bg-gray-600/80"
                                          : image.status === "duplicate"
                                          ? "bg-yellow-600/80"
                                          : image.status === "pending"
                                          ? "bg-blue-600/80"
                                          : "bg-black/70"
                                      }`}
                                    >
                                      <div>页码：{image.page_number}</div>
                                      {image.student_id && (
                                        <div>学号：{image.student_id}</div>
                                      )}
                                      <div>
                                        状态：
                                        {image.status === "normal"
                                          ? "正常"
                                          : image.status === "ocr_error"
                                          ? "OCR异常"
                                          : image.status === "blank"
                                          ? "空白页"
                                          : image.status === "duplicate"
                                          ? "重复页"
                                          : image.status === "pending"
                                          ? "待分发"
                                          : "未知"}
                                      </div>
                                    </div>

                                    {/* hover操作按钮 */}
                                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1 flex-wrap p-2">
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation(
                                            "replace",
                                            image
                                          );
                                        }}
                                      >
                                        <Upload className="h-3 w-3 mr-1" />
                                        替换
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation(
                                            "edit_page",
                                            image
                                          );
                                        }}
                                      >
                                        <Edit className="h-3 w-3 mr-1" />
                                        页码
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation(
                                            "edit_student",
                                            image
                                          );
                                        }}
                                      >
                                        <Edit className="h-3 w-3 mr-1" />
                                        学号
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        title="重置OCR"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          // 重置OCR功能
                                          console.log("重置OCR:", image.id);
                                          alert("OCR重置成功");
                                        }}
                                      >
                                        <RefreshCw className="h-3 w-3 mr-1" />
                                        重置OCR
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="destructive"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageOperation("delete", image);
                                        }}
                                      >
                                        <Trash2 className="h-3 w-3 mr-1" />
                                        删除
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        className="text-xs"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImagePreview(
                                            image,
                                            filteredImages
                                          );
                                        }}
                                      >
                                        <Eye className="h-3 w-3 mr-1" />
                                        预览
                                      </Button>
                                    </div>
                                  </div>

                                  {/* 垂直分割线 */}
                                  {imageIndex === 0 && group.length > 1 && (
                                    <div className="absolute top-0 bottom-0 right-0 w-px bg-border" />
                                  )}
                                </div>
                              ))}
                            </div>
                          </Card>
                        ))}

                        {/* 添加新图片按钮 */}
                        <div
                          className="flex-shrink-0 w-48 h-64 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                          onClick={() => handleImageOperation("add")}
                        >
                          <div className="text-center text-muted-foreground">
                            <Plus className="h-8 w-8 mx-auto mb-2" />
                            <div className="text-sm">添加图片</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}
        </TabsContent>
        {/* 学生详情tab */}
        <TabsContent value="student" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>学生扫描详情</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-4">
                {/* 第一行筛选项 */}
                <div className="flex gap-4">
                  <Input
                    placeholder="学号"
                    value={filters.student_no}
                    onChange={(e) =>
                      handleFilterChange("student_no", e.target.value)
                    }
                    className="w-32"
                  />
                  <Input
                    placeholder="姓名"
                    value={filters.name}
                    onChange={(e) => handleFilterChange("name", e.target.value)}
                    className="w-32"
                  />
                  <Input
                    placeholder="班级"
                    value={filters.class}
                    onChange={(e) =>
                      handleFilterChange("class", e.target.value)
                    }
                    className="w-32"
                  />
                  <Select
                    value={filters.exception_status}
                    onValueChange={(v) =>
                      handleFilterChange("exception_status", v)
                    }
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="异常状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="页码异常">页码异常</SelectItem>
                      <SelectItem value="少页">少页</SelectItem>
                      <SelectItem value="多页">多页</SelectItem>
                      <SelectItem value="存在相同页码">存在相同页码</SelectItem>
                      <SelectItem value="未上传答题卡">未上传答题卡</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={filters.is_exception}
                    onValueChange={(v) => handleFilterChange("is_exception", v)}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="是否异常" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="否">否</SelectItem>
                      <SelectItem value="是">是</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" onClick={handleSearchStudents}>
                    <Search className="h-4 w-4 mr-2" />
                    查询
                  </Button>
                  <Button onClick={handleGenerateTask}>生成任务</Button>
                </div>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>姓名</TableHead>
                    <TableHead>班级</TableHead>
                    <TableHead>学号</TableHead>
                    <TableHead>学校</TableHead>
                    <TableHead>异常状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentScans.map((stu) => (
                    <TableRow key={stu.id}>
                      <TableCell>{stu.name}</TableCell>
                      <TableCell>{stu.class}</TableCell>
                      <TableCell>{stu.student_no}</TableCell>
                      <TableCell>{stu.school}</TableCell>
                      <TableCell>{stu.exception_status}</TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleShowStudentImages(stu)}
                        >
                          <ImageIcon className="w-4 h-4 mr-1" />
                          图片详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 学生分页 */}
              <div className="mt-4 flex justify-end">
                <PaginationComponent
                  total={studentTotal}
                  current={studentPage}
                  pageSize={studentPageSize}
                  onChange={(page, size) => {
                    setStudentPage(page);
                    setStudentPageSize(size);
                  }}
                  showSizeChanger={true}
                  showTotal={true}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 大图预览Dialog */}
      <Dialog
        open={!!previewImageUrl}
        onOpenChange={() => {
          setPreviewImageUrl(null);
          setPreviewImages([]);
          setPreviewImageIndex(0);
          setZoomLevel(1);
        }}
      >
        <DialogContent className="max-w-[70vw] max-h-[90vh] p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle>
              图片预览 ({previewImageIndex + 1}/{previewImages.length})
            </DialogTitle>
          </DialogHeader>

          <div className="flex h-[80vh]">
            {/* 左侧切换按钮 */}
            <div className="flex items-center p-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevImage}
                disabled={previewImageIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>

            {/* 中间图片区域 */}
            <div className="flex-1 flex flex-col">
              <div className="flex-1 flex items-center justify-center overflow-hidden bg-muted/20">
                {previewImageUrl && (
                  <DraggableImage
                    src={previewImageUrl}
                    alt="预览图片"
                    className="w-full h-full"
                    zoomLevel={zoomLevel}
                    onZoomChange={setZoomLevel}
                  />
                )}
              </div>

              {/* 缩放控制和信息 */}
              <div className="p-4 border-t bg-background">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setZoomLevel((prev) => Math.max(0.5, prev - 0.1))
                      }
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                    <span className="text-sm font-medium">
                      {Math.round(zoomLevel * 100)}%
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setZoomLevel((prev) => Math.min(3, prev + 0.1))
                      }
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setZoomLevel(1)}
                    >
                      重置
                    </Button>
                  </div>

                  {/* 当前图片信息 */}
                  {previewImages[previewImageIndex] && (
                    <div className="text-sm text-muted-foreground">
                      页码：{previewImages[previewImageIndex].page_number} |
                      状态：
                      {previewImages[previewImageIndex].status === "normal"
                        ? "正常"
                        : previewImages[previewImageIndex].status ===
                          "ocr_error"
                        ? "OCR异常"
                        : previewImages[previewImageIndex].status === "blank"
                        ? "空白页"
                        : previewImages[previewImageIndex].status ===
                          "duplicate"
                        ? "重复页"
                        : previewImages[previewImageIndex].status === "pending"
                        ? "待分发"
                        : "未知"}
                      {previewImages[previewImageIndex].student_id && (
                        <>
                          {" "}
                          | 学号：{previewImages[previewImageIndex].student_id}
                        </>
                      )}
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                {previewImages[previewImageIndex] && (
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        handleImageOperation(
                          "replace",
                          previewImages[previewImageIndex]
                        )
                      }
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      替换图片
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        handleImageOperation(
                          "edit_page",
                          previewImages[previewImageIndex]
                        )
                      }
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      修改页码
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        handleImageOperation(
                          "edit_student",
                          previewImages[previewImageIndex]
                        )
                      }
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      修改学号
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        console.log(
                          "重置OCR:",
                          previewImages[previewImageIndex].id
                        );
                        alert("OCR重置成功");
                      }}
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      重置OCR
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() =>
                        handleImageOperation(
                          "delete",
                          previewImages[previewImageIndex]
                        )
                      }
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      删除图片
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧切换按钮 */}
            <div className="flex items-center p-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextImage}
                disabled={previewImageIndex === previewImages.length - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 图片操作Dialog */}
      <ImageOperationDialog
        open={operationDialogOpen}
        onOpenChange={setOperationDialogOpen}
        operationType={operationType}
        currentImage={currentOperationImage}
        uploadFile={uploadFile}
        uploadPreview={uploadPreview}
        editPageNumber={editPageNumber}
        editStudentId={editStudentId}
        editStudentName={editStudentName}
        isMarkingError={isMarkingError}
        studentSearchResults={studentSearchResults}
        onFileUpload={handleFileUpload}
        onPageNumberChange={setEditPageNumber}
        onStudentIdChange={(value) => {
          setEditStudentId(value);
          // 这里应该调用API进行学生搜索
          if (value) {
            setStudentSearchResults([
              {
                id: "1",
                student_id: "2024001",
                name: "张三",
                class: "初一1班",
                grade: "初一",
              },
              {
                id: "2",
                student_id: "2024002",
                name: "李四",
                class: "初一1班",
                grade: "初一",
              },
            ]);
          } else {
            setStudentSearchResults([]);
          }
        }}
        onStudentNameChange={setEditStudentName}
        onMarkingErrorChange={setIsMarkingError}
        onStudentSelect={(student) => {
          setEditStudentId(student.student_id);
          setEditStudentName(student.name);
          setStudentSearchResults([]);
        }}
        onSave={handleSaveOperation}
        onCancel={() => setOperationDialogOpen(false)}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="确认删除"
        description="您确定要删除这张图片吗？此操作无法撤销。"
        onConfirm={() => {
          // 这里应该调用API删除图片
          console.log("删除图片:", currentOperationImage);
          setDeleteConfirmOpen(false);
          setCurrentOperationImage(null);
        }}
      />

      {/* 批次删除确认对话框 */}
      <DeleteConfirmDialog
        open={batchDeleteConfirmOpen}
        onOpenChange={setBatchDeleteConfirmOpen}
        title="确认删除批次"
        description={`您确定要删除批次 ${currentBatchToDelete} 的所有图片吗？此操作无法撤销。`}
        onConfirm={handleBatchDelete}
      />

      {/* 学生图片详情Dialog */}
      <Dialog open={imageDialogOpen} onOpenChange={setImageDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>图片详情</DialogTitle>
          </DialogHeader>
          <div className="flex flex-wrap gap-2">
            {currentImages.map((img, idx) => (
              <div
                key={idx}
                className="w-24 h-32 bg-gray-200 flex items-center justify-center text-xs"
              >
                {img}
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* 学生图片详情弹窗 */}
      {currentStudentInfo && (
        <StudentImageDialog
          open={studentImageDialogOpen}
          onOpenChange={setStudentImageDialogOpen}
          studentName={currentStudentInfo.name}
          studentId={currentStudentInfo.id}
          studentClass={currentStudentInfo.class}
          images={currentStudentImages}
          onImageOperation={(type, image) => {
            // 复用现有的图片操作功能
            handleImageOperation(type, image);
          }}
        />
      )}
    </>
  );
};

export default HomeworkScan;
