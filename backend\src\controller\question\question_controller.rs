use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Json},
};
use uuid::Uuid;
use crate::{
    model::question::{
        BulkImportRequest, BulkImportResponse, CreateQuestionRequest, QuestionListResponse,
        QuestionQueryParams, QuestionResponse, QuestionSearchResponse, QuestionStatisticsResponse,
        UpdateQuestionRequest
    },
    service::question::QuestionService,
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};
pub async fn create_question_handler(
    State(state): State<AppState>,
    Json(request): J<PERSON><CreateQuestionRequest>,
) -> impl IntoResponse {
    let service = QuestionService::new(state.db.clone());
    let creator_id = Uuid::new_v4(); // TODO: Extract from auth context
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.create_question(creator_id, tenant_id, request).await {
        Ok(question) => responses::success(question, Some("Question created successfully")),
        Err(e) => {
            eprintln!("Error creating question: {}", e);
            responses::error("Failed to create question", Some("INTERNAL_ERROR"))
        }
    }
}

pub async fn get_question_handler(
    State(state): State<AppState>,
    Path(question_id): Path<Uuid>,
) -> impl IntoResponse {
    let service = QuestionService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.get_question(question_id, tenant_id).await {
        Ok(Some(question)) => responses::success(question, Some("Question retrieved successfully")),
        Ok(None) => responses::error("Question not found", Some("NOT_FOUND")),
        Err(e) => {
            eprintln!("Error retrieving question: {}", e);
            responses::error("Failed to retrieve question", Some("INTERNAL_ERROR"))
        }
    }
}

pub async fn list_questions_handler(
    State(state): State<AppState>,
    Query(params): Query<QuestionQueryParams>,
) -> Result<Json<ApiResponse<QuestionListResponse>>, StatusCode> {
    let service = QuestionService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.list_questions(params, tenant_id).await {
        Ok(questions) => Ok(Json(ApiResponse::success(questions, None))),
        Err(e) => {
            eprintln!("Error listing questions: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn update_question_handler(
    State(state): State<AppState>,
    Path(question_id): Path<Uuid>,
    Json(request): Json<UpdateQuestionRequest>,
) -> Result<Json<ApiResponse<QuestionResponse>>, StatusCode> {
    let service = QuestionService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.update_question(question_id, tenant_id, request).await {
        Ok(Some(question)) => Ok(Json(ApiResponse::success(question, None))),
        Ok(None) => Ok(Json(ApiResponse::error("Question not found".to_string(), Some("NOT_FOUND".to_string())))),
        Err(e) => {
            eprintln!("Error updating question: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn delete_question_handler(
    State(state): State<AppState>,
    Path(question_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    let service = QuestionService::new(state.db.clone());

    match service.delete_question(question_id).await {
        Ok(_) => Ok(Json(ApiResponse::success((), None))),
        Err(e) => {
            eprintln!("Error deleting question: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn bulk_import_questions_handler(
    State(state): State<AppState>,
    Json(request): Json<BulkImportRequest>,
) -> Result<Json<ApiResponse<BulkImportResponse>>, StatusCode> {
    let service = QuestionService::new(state.db.clone());
    let creator_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.bulk_import_questions(creator_id, request).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error bulk importing questions: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn get_question_statistics_handler(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<QuestionStatisticsResponse>>, StatusCode> {
    let service = QuestionService::new(state.db.clone());

    match service.get_question_statistics().await {
        Ok(stats) => Ok(Json(ApiResponse::success(stats, None))),
        Err(e) => {
            eprintln!("Error getting question statistics: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn search_questions_handler(
    State(state): State<AppState>,
    Query(params): Query<QuestionQueryParams>,
) -> Result<Json<ApiResponse<QuestionSearchResponse>>, StatusCode> {
    let service = QuestionService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.search_questions(params, tenant_id).await {
        Ok(results) => Ok(Json(ApiResponse::success(results, None))),
        Err(e) => {
            eprintln!("Error searching questions: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}