use std::collections::HashMap;

use axum::{
    extract::{Path, State},
    routing::post,
    Json, Router,
};
use uuid::Uuid;

use crate::{
    model::homework_papers::homework_papers::{BindPapersToHomeworkParams, HomeworkPapers},
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/bindPapersToHomework", post(bind_papers_to_homework))
        
}

pub async fn bind_papers_to_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): <PERSON><PERSON><BindPapersToHomeworkParams>,
)->Result<ApiResponse<Vec<HomeworkPapers>>,ApiResponse<()>>{
    state
        .homework_papers_service
        .bind_papers_to_homework(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))

}
