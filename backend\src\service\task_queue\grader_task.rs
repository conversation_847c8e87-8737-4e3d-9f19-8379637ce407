use std::sync::Arc;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use tracing::log;
use redis::{Client as RedisClient, AsyncCommands};
use crate::utils::redis_client::RedisPool;
use super::ocr_task::OcrTask;
use super::Task;

#[derive(Clone, Deserialize, Serialize)]
pub struct GraderTask {
    score_id: String,
    tenant: String,
    grade_workflow: String,
    answer: String,
    question_content: Option<String>,
    standard_answer: String,
    score: f64,
}

impl GraderTask {
    pub fn from_ocr_task_and_answer(ocr_task: OcrTask, answer: String) -> Self {
        Self {
            score_id: ocr_task.score_id,
            tenant: ocr_task.tenant,
            grade_workflow: ocr_task.grade_workflow,
            answer,
            question_content: ocr_task.question_content,
            standard_answer: ocr_task.standard_answer,
            score: ocr_task.score,
        }
    }
}

#[derive(Serialize)]
struct Payload {
    #[serde(rename = "type")]
    name: String,
    texts: Vec<String>,
    params: Params,
}
#[derive(Serialize)]
struct Params {
    score: f64,
    #[serde(rename = "correctAnswer")]
    standard_answer: String,
    #[serde(rename = "originalQuestion")]
    question_content: Option<String>,
}

impl Task for GraderTask {
    fn get_id(&self) -> (String, String) {
        (self.score_id.clone(), self.tenant.clone())
    }

    // 获取OCR结果，修改内容，添加到score任务队列
    async fn execute(&mut self, client: Client, db: PgPool, redis_pool: RedisPool) -> Result<(), String> {
        let url = format!("http://192.168.4.9:3000/api/run_workflow_json/{}", self.grade_workflow);
        let builder = client.post(url);
        let params = Params {
            score: self.score,
            standard_answer: self.standard_answer.clone(),
            question_content: self.question_content.clone(),
        };
        let payload = Payload {
            name: "text".to_string(),
            texts: vec![self.answer.clone()],
            params,
        };
        let response = builder.json(&payload).send().await.map_err(|e| e.to_string())?;
        if !response.status().is_success() {
            return Err(response.text().await.map_err(|e| e.to_string())?);
        }
        let data = response.json::<serde_json::Value>().await.map_err(|e| e.to_string())?;
        let data = data.as_object().and_then(|d| d.get("results")).and_then(|v| v.as_array()).ok_or("results error")?;
        let contents: Vec<_> = data.into_iter().filter_map(|choice| {
            let reason = choice.get("reason").and_then(|v| v.as_str()).and_then(|content| Some(content.to_string()));
            let score = choice.get("score").and_then(|v| v.as_f64());
            let timeout = choice.get("timeout_feedback").and_then(|v| v.as_bool()).unwrap_or(false);
            if timeout {
                None
            } else {
                reason.zip(score)
            }
        }).collect();
        // TODO 更新数据库db
        if contents.len() != 1 {
            // Failed
        } else {
            log::info!("{:?}", contents[0]);
        }
        Ok(())
    }
}