use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct Classes {
    pub id: i64,
    pub name: String,
    #[serde(rename = "type")]
    pub class_type: String,
    pub grade_level_id: i32,
    pub subject_group_id: Option<i64>,
    pub capacity: i32,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct CreateClass {
    pub name: String,
    #[serde(rename = "type")]
    pub class_type: String,
    pub grade_level_id: i32,
    pub subject_group_id: Option<i64>,
    pub capacity: i32,
}

#[derive(Debug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub struct UpdateClass {
    pub name: Option<String>,
    #[serde(rename = "type")]
    pub class_type: Option<String>,
    pub grade_level_id: Option<i32>,
    pub subject_group_id: Option<i64>,
    pub capacity: Option<i32>,
}