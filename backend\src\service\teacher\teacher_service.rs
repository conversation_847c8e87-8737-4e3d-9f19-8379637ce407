use crate::model::base::PageResult;
use crate::model::teacher::{
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Teacher, Teacher<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ist<PERSON>, TeacherQueryParams, UpdateTeacher,
};
use crate::utils::error::AppError;
use crate::utils::schema::{connect_with_schema, validate_schema_name};
use sqlx::PgPool;
use uuid::Uuid;

#[derive(Clone)]
pub struct TeacherService {
    db_pool: PgPool,
}

impl TeacherService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 获取教师列表（分页）
    pub async fn get_teachers(
        &self,
        schema_name: &str,
        params: TeacherQueryParams,
    ) -> Result<PageResult<TeacherListVO>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        // 构建基础查询
        let mut query = String::from(
            "SELECT 
                t.id,
                t.employee_id,
                t.name,
                t.phone,
                t.employment_status,
                t.title,
                t.teaching_subjects,
                t.is_active,
                c.name as homeroom_class_name,
                g.name as grade_level_name
            FROM teachers t
            LEFT JOIN classes c ON t.homeroom_class_id = c.id
            LEFT JOIN public.grade_levels g ON t.grade_level_id = g.id
            WHERE 1=1",
        );

        // 添加过滤条件
        if let Some(name) = &params.name {
            query.push_str(" AND t.name ILIKE '%");
            query.push_str(name);
            query.push_str("%'");
        }

        if let Some(employee_id) = &params.employee_id {
            query.push_str(" AND t.employee_id = '");
            query.push_str(employee_id);
            query.push_str("'");
        }

        if let Some(employment_status) = &params.employment_status {
            query.push_str(" AND t.employment_status = '");
            query.push_str(employment_status);
            query.push_str("'");
        }

        if let Some(is_active) = params.is_active {
            query.push_str(&format!(" AND t.is_active = {}", is_active));
        }

        if let Some(grade_level_id) = params.grade_level_id {
            query.push_str(&format!(" AND t.grade_level_id = {}", grade_level_id));
        }

        if let Some(teaching_subject) = &params.teaching_subject {
            query.push_str(" AND '");
            query.push_str(teaching_subject);
            query.push_str("' = ANY(t.teaching_subjects)");
        }

        query.push_str(" ORDER BY t.created_at DESC");
        query.push_str(&format!(" LIMIT {} OFFSET {}", page_size, offset));

        // 执行查询
        let teachers = sqlx::query_as::<_, TeacherListVO>(&query)
            .fetch_all(&mut *conn)
            .await?;

        // 获取总数
        let count_query = "SELECT COUNT(*) FROM teachers WHERE 1=1";
        let total = sqlx::query_scalar::<_, i64>(count_query)
            .fetch_one(&mut *conn)
            .await?;

        Ok(PageResult {
            page,
            page_size,
            total,
            total_pages: (total + page_size - 1) / page_size,
            data: teachers,
        })
    }

    /// 根据ID获取教师详细信息
    pub async fn get_teacher_by_id(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
    ) -> Result<Option<TeacherDetailVO>, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let teacher = sqlx::query_as::<_, TeacherDetailVO>(
            "SELECT 
                t.id,
                t.tenant_id,
                t.user_id,
                t.employee_id,
                t.name,
                t.phone,
                t.email,
                t.gender,
                t.date_of_birth,
                t.id_card_number,
                t.highest_education,
                t.graduation_school,
                t.major,
                t.hire_date,
                t.employment_status,
                t.title,
                t.teaching_subjects,
                t.homeroom_class_id,
                c.name as homeroom_class_name,
                t.grade_level_id,
                g.name as grade_level_name,
                t.subject_group_id,
                sg.name as subject_group_name,
                t.office_location,
                t.bio,
                t.is_active,
                t.created_at,
                t.updated_at
            FROM teachers t
            LEFT JOIN classes c ON t.homeroom_class_id = c.id
            LEFT JOIN public.grade_levels g ON t.grade_level_id = g.id
            LEFT JOIN public.subject_groups sg ON t.subject_group_id = sg.id
            WHERE t.id = $1",
        )
        .bind(teacher_id)
        .fetch_optional(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 创建教师
    pub async fn create_teacher(
        &self,
        schema_name: &str,
        tenant_id: Uuid,
        data: CreateTeacher,
    ) -> Result<Teacher, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        // 检查工号是否已存在
        if self
            .employee_id_exists(schema_name, &data.employee_id)
            .await?
        {
            return Err(AppError::BadRequest("工号已存在".to_string()));
        }

        // 检查用户是否已绑定为其他教师
        if self.user_already_teacher(schema_name, data.user_id).await? {
            return Err(AppError::BadRequest("该用户已经是教师".to_string()));
        }

        let teacher = sqlx::query_as::<_, Teacher>(
            r#"
            INSERT INTO teachers (
                tenant_id, user_id, employee_id, name, phone, email, gender,
                date_of_birth, id_card_number, highest_education, graduation_school,
                major, hire_date, employment_status, title, teaching_subjects,
                homeroom_class_id, grade_level_id, subject_group_id, office_location, bio
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21)
            RETURNING *
            "#
        )
        .bind(tenant_id)
        .bind(data.user_id)
        .bind(&data.employee_id)
        .bind(&data.name)
        .bind(data.phone)
        .bind(data.email)
        .bind(data.gender)
        .bind(data.date_of_birth)
        .bind(data.id_card_number)
        .bind(data.highest_education)
        .bind(data.graduation_school)
        .bind(data.major)
        .bind(data.hire_date)
        .bind(&data.employment_status)
        .bind(data.title)
        .bind(&data.teaching_subjects.unwrap_or_default())
        .bind(data.homeroom_class_id)
        .bind(data.grade_level_id)
        .bind(data.subject_group_id)
        .bind(data.office_location)
        .bind(data.bio)
        .fetch_one(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 更新教师信息
    pub async fn update_teacher(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
        data: UpdateTeacher,
    ) -> Result<Teacher, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        // 检查工号是否与其他教师冲突
        if let Some(employee_id) = &data.employee_id {
            if self
                .employee_id_conflicts(schema_name, teacher_id, employee_id)
                .await?
            {
                return Err(AppError::BadRequest("工号已被其他教师使用".to_string()));
            }
        }

        let teacher = sqlx::query_as::<_, Teacher>(
            r#"
            UPDATE teachers SET
                employee_id = COALESCE($2, employee_id),
                name = COALESCE($3, name),
                phone = COALESCE($4, phone),
                email = COALESCE($5, email),
                gender = COALESCE($6, gender),
                date_of_birth = COALESCE($7, date_of_birth),
                id_card_number = COALESCE($8, id_card_number),
                highest_education = COALESCE($9, highest_education),
                graduation_school = COALESCE($10, graduation_school),
                major = COALESCE($11, major),
                hire_date = COALESCE($12, hire_date),
                employment_status = COALESCE($13, employment_status),
                title = COALESCE($14, title),
                teaching_subjects = COALESCE($15, teaching_subjects),
                homeroom_class_id = COALESCE($16, homeroom_class_id),
                grade_level_id = COALESCE($17, grade_level_id),
                subject_group_id = COALESCE($18, subject_group_id),
                office_location = COALESCE($19, office_location),
                bio = COALESCE($20, bio),
                is_active = COALESCE($21, is_active),
                updated_at = NOW()
            WHERE id = $1
            RETURNING *
            "#,
        )
        .bind(teacher_id)
        .bind(data.employee_id)
        .bind(data.name)
        .bind(data.phone)
        .bind(data.email)
        .bind(data.gender)
        .bind(data.date_of_birth)
        .bind(data.id_card_number)
        .bind(data.highest_education)
        .bind(data.graduation_school)
        .bind(data.major)
        .bind(data.hire_date)
        .bind(data.employment_status)
        .bind(data.title)
        .bind(data.teaching_subjects.as_deref())
        .bind(data.homeroom_class_id)
        .bind(data.grade_level_id)
        .bind(data.subject_group_id)
        .bind(data.office_location)
        .bind(data.bio)
        .bind(data.is_active)
        .fetch_one(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 删除教师
    pub async fn delete_teacher(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
    ) -> Result<(), AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let result = sqlx::query("DELETE FROM teachers WHERE id = $1")
            .bind(teacher_id)
            .execute(&mut *conn)
            .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("教师不存在".to_string()));
        }

        Ok(())
    }

    /// 激活/停用教师
    pub async fn toggle_teacher_status(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
        is_active: bool,
    ) -> Result<Teacher, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let teacher = sqlx::query_as::<_, Teacher>(
            r#"
            UPDATE teachers SET
                is_active = $2,
                updated_at = NOW()
            WHERE id = $1
            RETURNING *
            "#,
        )
        .bind(teacher_id)
        .bind(is_active)
        .fetch_one(&mut *conn)
        .await?;

        Ok(teacher)
    }

    /// 检查工号是否已存在
    async fn employee_id_exists(
        &self,
        schema_name: &str,
        employee_id: &str,
    ) -> Result<bool, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM teachers WHERE employee_id = $1")
                .bind(employee_id)
                .fetch_one(&mut *conn)
                .await?;

        Ok(count > 0)
    }

    /// 检查用户是否已经是教师
    async fn user_already_teacher(
        &self,
        schema_name: &str,
        user_id: Uuid,
    ) -> Result<bool, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count =
            sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM teachers WHERE user_id = $1")
                .bind(user_id)
                .fetch_one(&mut *conn)
                .await?;

        Ok(count > 0)
    }

    /// 检查工号是否与其他教师冲突
    async fn employee_id_conflicts(
        &self,
        schema_name: &str,
        teacher_id: Uuid,
        employee_id: &str,
    ) -> Result<bool, AppError> {
        let safe_schema = validate_schema_name(schema_name)?;
        let mut conn = connect_with_schema(&self.db_pool, &safe_schema).await?;

        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM teachers WHERE employee_id = $1 AND id != $2",
        )
        .bind(employee_id)
        .bind(teacher_id)
        .fetch_one(&mut *conn)
        .await?;

        Ok(count > 0)
    }

    /**
     * 作者：张瀚
     * 说明：通过教师ID批量查询
     */
    pub async fn find_all_by_id_in(
        &self,
        schema_name: &String,
        teacher_id_list: Vec<Uuid>,
    ) -> Result<Vec<Teacher>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select t.* from {}.teachers t where t.id in( ",
            schema_name
        ));
        for (index, id) in teacher_id_list.iter().enumerate() {
            builder.push_bind(id);
            if index < teacher_id_list.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) order by t.created_at desc");
            }
        }
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }
}
