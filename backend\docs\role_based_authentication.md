# 基于角色的认证系统

## 概述

本项目已将原来专门的 `admin_auth_middleware` 重构为通用的基于角色的认证系统。新系统通过用户角色来判定权限，而不是单独的管理员认证逻辑。

## 主要变更

### 1. 新增通用认证上下文

```rust
/// 用户角色信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRole {
    pub role_id: Uuid,
    pub identity_type: String,
    pub tenant_id: Option<Uuid>,
    pub is_verified: bool,
}

/// 通用认证上下文
#[derive(Debug, Clone)]
pub struct AuthContext {
    pub user_id: Uuid,
    pub username: String,
    pub roles: Vec<UserRole>,
}
```

### 2. 角色检查方法

`AuthContext` 提供了便捷的角色检查方法：

```rust
impl AuthContext {
    /// 检查用户是否具有指定角色
    pub fn has_role(&self, role_type: &str) -> bool;
    
    /// 检查用户是否为系统管理员
    pub fn is_admin(&self) -> bool;
    
    /// 检查用户是否为教师
    pub fn is_teacher(&self) -> bool;
    
    /// 检查用户是否为学生
    pub fn is_student(&self) -> bool;
    
    /// 获取用户在指定租户中的角色
    pub fn get_roles_for_tenant(&self, tenant_id: Uuid) -> Vec<&UserRole>;
}
```

## 中间件使用

### 1. 通用认证中间件

```rust
use crate::middleware::auth_middleware::auth_middleware;

// 验证用户身份并加载角色信息，不限制特定角色
Router::new()
    .route("/profile", get(get_profile))
    .route_layer(middleware::from_fn_with_state(pool, auth_middleware));
```

### 2. 角色检查中间件

```rust
use crate::middleware::auth_middleware::{require_admin_middleware, require_teacher_middleware, require_student_middleware};

// 管理员专用路由
Router::new()
    .route("/admin/dashboard", get(admin_dashboard))
    .route_layer(middleware::from_fn(require_admin_middleware))
    .route_layer(middleware::from_fn_with_state(pool, auth_middleware));

// 教师专用路由
Router::new()
    .route("/teacher/classes", get(teacher_classes))
    .route_layer(middleware::from_fn(require_teacher_middleware))
    .route_layer(middleware::from_fn_with_state(pool, auth_middleware));
```

### 3. 多角色检查

```rust
use crate::middleware::auth_middleware::require_any_role_middleware;

// 教师或管理员都可以访问
Router::new()
    .route("/manage/students", get(manage_students))
    .route_layer(middleware::from_fn(require_any_role(&["teacher", "admin"])))
    .route_layer(middleware::from_fn_with_state(pool, auth_middleware));
```

## 控制器中的使用

### 1. 使用新的认证上下文

```rust
use crate::middleware::auth_middleware::AuthExtractor;

pub async fn get_user_info(
    AuthExtractor(auth_context): AuthExtractor,
) -> Result<Json<UserInfo>, StatusCode> {
    let user_info = UserInfo {
        user_id: auth_context.user_id,
        username: auth_context.username,
        is_admin: auth_context.is_admin(),
        is_teacher: auth_context.is_teacher(),
        roles: auth_context.roles.iter()
            .filter(|role| role.is_verified)
            .map(|role| role.identity_type.clone())
            .collect(),
    };
    
    Ok(Json(user_info))
}
```

### 2. 向后兼容的管理员认证

```rust
use crate::middleware::auth_middleware::AuthExtractor;

// 仍然可以使用传统的 AuthExtractor
pub async fn admin_only_function(
    AuthExtractor(admin_context): AuthExtractor,
) -> Result<Json<String>, StatusCode> {
    // 只有管理员可以访问
    Ok(Json("Admin only content".to_string()))
}
```

## 路由配置示例

### 1. 完整的路由配置

```rust
pub fn build_app(state: AppState) -> Router {
    // 公开路由（无需认证）
    let public_routes = Router::new()
        .route("/login", post(login))
        .route("/register", post(register));

    // 需要认证但不限制角色的路由
    let authenticated_routes = Router::new()
        .route("/profile", get(get_profile))
        .route("/logout", post(logout))
        .route_layer(middleware::from_fn_with_state(state.db.clone(), auth_middleware));

    // 管理员专用路由
    let admin_routes = Router::new()
        .route("/users", get(list_users))
        .route("/users/:id/roles", post(assign_role))
        .route_layer(middleware::from_fn(require_admin_middleware))
        .route_layer(middleware::from_fn_with_state(state.db.clone(), auth_middleware));

    // 教师专用路由
    let teacher_routes = Router::new()
        .route("/classes", get(get_classes))
        .route("/grades", post(submit_grades))
        .route_layer(middleware::from_fn(require_teacher_middleware))
        .route_layer(middleware::from_fn_with_state(state.db.clone(), auth_middleware));

    Router::new()
        .nest("/api/public", public_routes)
        .nest("/api", authenticated_routes)
        .nest("/api/admin", admin_routes)
        .nest("/api/teacher", teacher_routes)
        .with_state(state)
}
```

## 向后兼容性

为了确保现有代码的正常运行，新系统保持了向后兼容性：

1. **AdminContext 和 AuthExtractor** 仍然可用
2. **admin_auth_middleware** 仍然可用，但内部使用新的角色系统
3. **现有的管理员路由** 无需修改即可继续工作

## 数据库查询

新系统基于 `public.user_identities` 表来获取用户角色：

```sql
-- 获取用户所有角色
SELECT ui.id as role_id, ui.identity_type, ui.tenant_id, ui.is_verified
FROM public.user_identities ui
WHERE ui.user_id = $1
ORDER BY ui.created_at DESC;

-- 检查用户是否为管理员
SELECT EXISTS (
    SELECT 1 FROM public.user_identities ui
    WHERE ui.user_id = $1 AND ui.identity_type = 'admin' AND ui.is_verified = true
) as is_admin;
```

## 最佳实践

1. **优先使用新的认证系统** - 对于新功能，建议使用 `AuthExtractor` 和通用认证中间件
2. **渐进式迁移** - 现有功能可以逐步迁移到新系统
3. **角色检查** - 在业务逻辑中使用 `auth_context.has_role()` 等方法进行角色检查
4. **租户隔离** - 使用 `get_roles_for_tenant()` 方法进行租户级别的权限检查

## API 端点示例

新系统提供了以下 API 端点：

- `GET /api/v2/profile` - 获取用户信息（需要认证）
- `GET /api/v2/teacher/dashboard` - 教师仪表板（需要教师角色）
- `GET /api/v2/student/dashboard` - 学生仪表板（需要学生角色）
- `GET /api/admin/users/me` - 获取当前用户详细信息（使用新认证系统）
- `GET /api/admin/users/all` - 获取所有用户（需要管理员权限）
- `POST /api/admin/users/assign-role` - 分配角色（需要管理员权限）
