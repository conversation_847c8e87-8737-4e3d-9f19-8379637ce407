use dotenvy::dotenv;
use sqlx::{Connection, PgConnection, PgPool, migrate::MigrateDatabase};
use std::fs;
use std::process::Command;

pub async fn setup_test_db() -> PgPool {
    dotenv().ok();
    let database_url = std::env::var("TEST_DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://test:test@localhost/test_db".to_string());

    // Create the test database
    if !sqlx::Postgres::database_exists(&database_url).await.unwrap_or(false) {
        Command::new("sqlx")
            .args(&["database", "create", "--database-url", &database_url])
            .output()
            .expect("Failed to create test database");
    }

    // Run migrations
    Command::new("sqlx")
        .args(&["migrate", "run", "--source", "migrations", "--database-url", &database_url])
        .output()
        .expect("Failed to run migrations");

    let pool = PgPool::connect(&database_url).await.expect("Failed to connect to test database");

    // Create a tenant for testing
    let mut conn = PgConnection::connect(&database_url).await.expect("Failed to connect to test database");
    sqlx::query("CREATE SCHEMA IF NOT EXISTS test_tenant")
        .execute(&mut conn)
        .await
        .expect("Failed to create tenant schema");

    let template = fs::read_to_string("tenants/template/init_tenant_schema.sql").expect("Failed to read tenant schema template");
    let tenant_schema_sql = template.replace("{schema}", "test_tenant");
    sqlx::query(&tenant_schema_sql)
        .execute(&mut conn)
        .await
        .expect("Failed to run tenant schema migration");

    pool
}

