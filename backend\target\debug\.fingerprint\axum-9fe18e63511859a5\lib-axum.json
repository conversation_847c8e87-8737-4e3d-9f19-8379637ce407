{"rustc": 1842507548689473721, "features": "[\"default\", \"form\", \"http1\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 13877304307972683652, "path": 7484472065759414222, "deps": [[40386456601120721, "percent_encoding", false, 5790721750244072186], [778154619793643451, "hyper_util", false, 17139366269164843306], [784494742817713399, "tower_service", false, 14074221061578414422], [1906322745568073236, "pin_project_lite", false, 16220677842723031536], [2517136641825875337, "sync_wrapper", false, 14794565523353513454], [5111622162004141796, "tokio_tungstenite", false, 5384300426569797805], [5695049318159433696, "tower", false, 3975352489635114627], [7695812897323945497, "itoa", false, 1429031462616551717], [7712452662827335977, "tower_layer", false, 8146032542958474769], [7858942147296547339, "rustversion", false, 14602530518800037077], [8606274917505247608, "tracing", false, 14064863493795061905], [8913795983780778928, "matchit", false, 1403097361733254745], [9010263965687315507, "http", false, 3133077987240373595], [9538054652646069845, "tokio", false, 6155195535907693298], [9689903380558560274, "serde", false, 12799672664599880943], [10229185211513642314, "mime", false, 17562962022958888321], [10629569228670356391, "futures_util", false, 2747797314946402595], [10724389056617919257, "sha1", false, 2614719430604899817], [11957360342995674422, "hyper", false, 7482627464293487771], [12757619235593077227, "multer", false, 13110028207347059558], [13077212702700853852, "base64", false, 11698229905019321626], [13645307863515715290, "serde_path_to_error", false, 17405738321958365505], [14084095096285906100, "http_body", false, 4683579932501336993], [14299496324343720937, "form_urlencoded", false, 13449675964369115100], [15176407853393882315, "axum_core", false, 11971615471536465098], [15367738274754116744, "serde_json", false, 10257594307601572992], [15932120279885307830, "memchr", false, 7712901758548144593], [16066129441945555748, "bytes", false, 92163385925654338], [16276906931490180112, "axum_macros", false, 5591777059978657864], [16542808166767769916, "serde_urlencoded", false, 15908812855208047916], [16900715236047033623, "http_body_util", false, 15728502437839345970]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-9fe18e63511859a5\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}