import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Pagination } from '@/components/ui/pagination';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  User,
  Phone,
  Calendar
} from 'lucide-react';
import { Student, StudentTableProps } from '@/types/student';
import { cn } from '@/lib/utils';

const StudentTable: React.FC<StudentTableProps> = ({
  students,
  loading,
  onEdit,
  onDelete,
  onViewDetail,
  pagination,
}) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'graduated':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'transferred':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    const statusMap = {
      'active': '在校',
      'inactive': '休学',
      'graduated': '毕业',
      'transferred': '转学',
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getProfileLevelColor = (level?: string) => {
    if (!level) return 'bg-gray-100 text-gray-600';
    
    const levelColors = {
      'A+': 'bg-red-100 text-red-800',
      'A': 'bg-red-100 text-red-700',
      'B+': 'bg-orange-100 text-orange-800',
      'B': 'bg-orange-100 text-orange-700',
      'C+': 'bg-yellow-100 text-yellow-800',
      'C': 'bg-yellow-100 text-yellow-700',
      'D+': 'bg-green-100 text-green-800',
      'D': 'bg-green-100 text-green-700',
    };
    return levelColors[level as keyof typeof levelColors] || 'bg-gray-100 text-gray-600';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>学号</TableHead>
                <TableHead>姓名</TableHead>
                <TableHead>性别</TableHead>
                <TableHead>联系方式</TableHead>
                <TableHead>班级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>能力等级</TableHead>
                <TableHead>入学时间</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 9 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (students.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 text-lg mb-2">暂无学生数据</div>
        <div className="text-gray-400 text-sm">点击"新增学生"创建第一个学生档案</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[120px]">学号</TableHead>
              <TableHead className="w-[100px]">姓名</TableHead>
              <TableHead className="w-[60px] text-center">性别</TableHead>
              <TableHead className="w-[120px]">联系方式</TableHead>
              <TableHead className="w-[100px]">班级</TableHead>
              <TableHead className="w-[80px] text-center">状态</TableHead>
              <TableHead className="w-[80px] text-center">能力等级</TableHead>
              <TableHead className="w-[100px]">入学时间</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id} className="hover:bg-gray-50">
                <TableCell className="font-mono">
                  <Badge variant="outline" className="text-xs">
                    {student.student_id}
                  </Badge>
                </TableCell>
                
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-2 text-gray-400" />
                    {student.name}
                  </div>
                </TableCell>
                
                <TableCell className="text-center">
                  <Badge variant="secondary" className="text-xs">
                    {student.gender === 'male' ? '男' : student.gender === 'female' ? '女' : '-'}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    {student.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="w-3 h-3 mr-1" />
                        {student.phone}
                      </div>
                    )}
                    {student.email && (
                      <div className="text-xs text-gray-500 truncate max-w-[100px]">
                        {student.email}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm">
                    {student.administrative_class_id ? (
                      <Badge variant="outline" className="text-xs">
                        待关联班级名称
                      </Badge>
                    ) : (
                      <span className="text-gray-400">未分配</span>
                    )}
                  </div>
                </TableCell>
                
                <TableCell className="text-center">
                  <Badge 
                    variant="secondary"
                    className={cn("text-xs", getStatusColor(student.status))}
                  >
                    {getStatusLabel(student.status)}
                  </Badge>
                </TableCell>
                
                <TableCell className="text-center">
                  {student.profile_level ? (
                    <Badge 
                      variant="secondary"
                      className={cn("text-xs", getProfileLevelColor(student.profile_level))}
                    >
                      {student.profile_level}
                    </Badge>
                  ) : (
                    <span className="text-gray-400 text-xs">未评级</span>
                  )}
                </TableCell>
                
                <TableCell className="text-sm text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {formatDate(student.enrollment_date)}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center justify-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem 
                          onClick={() => onViewDetail(student)}
                          className="cursor-pointer"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => onEdit(student)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem 
                          onClick={() => onDelete(student.id)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.total > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            显示第 {((pagination.current - 1) * pagination.pageSize) + 1} - {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条记录
          </div>
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={pagination.onChange}
            showSizeChanger={true}
            showQuickJumper={true}
            size="sm"
          />
        </div>
      )}
    </div>
  );
};

export default StudentTable;