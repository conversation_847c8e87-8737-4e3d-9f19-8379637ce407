import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import { BindPapersToHomeworkParams, HomeworkPapers } from '@/types/homeworkPapers';
import apiClient from './apiClient';

export const homeworkPapersApi = {

/**
 * 作者：朱若彪
 * 说明：绑定作业和试卷的关联
 */
  bindPapersToHomework: async (tenant_id: string, tenant_name: string, params: BindPapersToHomeworkParams): Promise<ApiResponse<HomeworkPapers[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkPapers/bindPapersToHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
};
