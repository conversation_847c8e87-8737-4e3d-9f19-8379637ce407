use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc, Timelike};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionTemplate {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub risk_level: i32,
    pub requires_approval: bool,
    pub approval_levels: i32,
    pub max_duration_hours: Option<i32>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionCategory {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub parent_category_id: Option<Uuid>,
    pub risk_multiplier: f64,
    pub requires_special_approval: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct ApprovalWorkflow {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub permission_category_id: Option<Uuid>,
    pub risk_threshold: i32,
    pub approval_levels: serde_json::Value,
    pub auto_approval_criteria: Option<serde_json::Value>,
    pub escalation_rules: Option<serde_json::Value>,
    pub timeout_hours: i32,
    pub created_at: DateTime<Utc>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SpecialPermissionRequest {
    pub id: Uuid,
    pub requester_id: Uuid,
    pub tenant_id: Uuid,
    pub permission_template_id: Uuid,
    pub target_resource: Option<String>,
    pub justification: String,
    pub risk_score: i32,
    pub risk_factors: Option<serde_json::Value>,
    pub requested_duration_hours: Option<i32>,
    pub status: String,
    pub workflow_id: Option<Uuid>,
    pub current_approval_level: i32,
    pub approval_history: serde_json::Value,
    pub auto_approved: bool,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub approved_at: Option<DateTime<Utc>>,
    pub approved_by: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionApproval {
    pub id: Uuid,
    pub request_id: Uuid,
    pub approver_id: Uuid,
    pub approval_level: i32,
    pub decision: String,
    pub comments: Option<String>,
    pub delegated_to: Option<Uuid>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionAuditLog {
    pub id: Uuid,
    pub user_id: Option<Uuid>,
    pub tenant_id: Option<Uuid>,
    pub action: String,
    pub resource: Option<String>,
    pub permission: Option<String>,
    pub result: String,
    pub risk_score: Option<i32>,
    pub context_data: Option<serde_json::Value>,
    pub ip_address: Option<std::net::IpAddr>,
    pub user_agent: Option<String>,
    pub session_id: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionConflict {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub conflict_type: String,
    pub conflicting_permissions: serde_json::Value,
    pub resolution_strategy: Option<String>,
    pub resolved_by: Option<Uuid>,
    pub resolution_notes: Option<String>,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCalculationRequest {
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub resource: String,
    pub context: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCalculationResult {
    pub granted: bool,
    pub permission_level: String,
    pub sources: Vec<PermissionSource>,
    pub conditions: Vec<PermissionCondition>,
    pub risk_score: i32,
    pub cache_ttl: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionSource {
    pub source_type: String, // role, special, organizational
    pub source_id: Uuid,
    pub source_name: String,
    pub priority: i32,
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCondition {
    pub condition_type: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    pub factor: String,
    pub weight: f64,
    pub description: String,
    pub value: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionRequestInput {
    pub permission_template_id: Uuid,
    pub target_resource: Option<String>,
    pub justification: String,
    pub requested_duration_hours: Option<i32>,
    pub context: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApprovalDecision {
    pub decision: String, // approved, rejected, delegated
    pub comments: Option<String>,
    pub conditions: Option<Vec<PermissionCondition>>,
    pub delegated_to: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionMonitoringDashboard {
    pub summary: PermissionSummary,
    pub risk_distribution: HashMap<String, i32>,
    pub top_anomalies: Vec<PermissionAnomaly>,
    pub approval_metrics: ApprovalMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionSummary {
    pub total_requests: i32,
    pub high_risk_requests: i32,
    pub pending_approvals: i32,
    pub anomalies_detected: i32,
    pub cache_hit_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionAnomaly {
    pub anomaly_type: String,
    pub user_id: Option<Uuid>,
    pub description: String,
    pub risk_score: f64,
    pub detected_at: DateTime<Utc>,
    pub context: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApprovalMetrics {
    pub average_approval_time: String,
    pub auto_approval_rate: f64,
    pub escalation_rate: f64,
    pub timeout_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheInvalidationRequest {
    pub invalidation_type: String, // user, tenant, resource, global
    pub target_id: Option<Uuid>,
    pub resource_pattern: Option<String>,
    pub reason: String,
    pub cascade: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheInvalidationResult {
    pub invalidation_id: Uuid,
    pub affected_entries: i32,
    pub processing_time_ms: u64,
    pub cascade_invalidations: i32,
    pub estimated_rebuild_time: String,
}

// Permission calculation context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionContext {
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub session_id: Option<String>,
    pub ip_address: Option<std::net::IpAddr>,
    pub user_agent: Option<String>,
    pub current_time: DateTime<Utc>,
    pub additional_context: HashMap<String, serde_json::Value>,
}

// Risk assessment result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessmentResult {
    pub risk_score: i32,
    pub risk_factors: Vec<RiskFactor>,
    pub requires_approval: bool,
    pub recommended_workflow: Option<Uuid>,
    pub auto_approval_eligible: bool,
}

impl PermissionTemplate {
    pub fn calculate_effective_risk_level(&self, context: &PermissionContext) -> i32 {
        let mut effective_risk = self.risk_level;
        
        // Apply time-based risk adjustments
        let hour = context.current_time.hour();
        if hour < 6 || hour > 22 {
            effective_risk += 1; // Higher risk for off-hours access
        }
        
        // Apply context-based adjustments
        if let Some(emergency) = context.additional_context.get("emergency_level") {
            if emergency.as_str() == Some("high") {
                effective_risk += 2;
            }
        }
        
        effective_risk.min(5) // Cap at maximum risk level
    }
}

impl SpecialPermissionRequest {
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            expires_at < Utc::now()
        } else {
            false
        }
    }
    
    pub fn can_auto_approve(&self, workflow: &ApprovalWorkflow) -> bool {
        if self.auto_approved {
            return false;
        }
        
        if let Some(criteria) = &workflow.auto_approval_criteria {
            if let Some(max_risk) = criteria.get("max_risk_score") {
                if let Some(max_risk_val) = max_risk.as_i64() {
                    return self.risk_score <= max_risk_val as i32;
                }
            }
        }
        
        false
    }
}

impl PermissionAuditLog {
    pub fn new(
        user_id: Option<Uuid>,
        tenant_id: Option<Uuid>,
        action: String,
        resource: Option<String>,
        permission: Option<String>,
        result: String,
        context: &PermissionContext,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            user_id,
            tenant_id,
            action,
            resource,
            permission,
            result,
            risk_score: None,
            context_data: Some(serde_json::to_value(&context.additional_context).unwrap_or_default()),
            ip_address: context.ip_address,
            user_agent: context.user_agent.clone(),
            session_id: context.session_id.clone(),
            created_at: Utc::now(),
        }
    }
}