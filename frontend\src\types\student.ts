// Student management types for the Deep-Mate frontend application

export interface Student {
  id: string;
  student_id: string;
  name: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_relation?: string;
  administrative_class_id?: string;
  grade_level_id?: string;
  user_id?: string;
  enrollment_date?: string;
  status: string;
  profile_level?: string;
  profile_tags?: any;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentRequest {
  student_id: string;
  name: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_relation?: string;
  administrative_class_id?: string;
  grade_level_id?: string;
  user_id?: string;
  enrollment_date?: string;
  status?: string;
  profile_level?: string;
  profile_tags?: any;
  notes?: string;
}

export interface UpdateStudentRequest {
  student_id?: string;
  name?: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  guardian_name?: string;
  guardian_phone?: string;
  guardian_relation?: string;
  administrative_class_id?: string;
  grade_level_id?: string;
  user_id?: string;
  enrollment_date?: string;
  status?: string;
  profile_level?: string;
  profile_tags?: any;
  notes?: string;
}

export interface StudentSearchParams {
  name?: string;
  student_id?: string;
  class_id?: string;
  grade_level_id?: string;
  status?: string;
  profile_level?: string;
  limit?: number;
  offset?: number;
}

export interface StudentTeachingClass {
  id: string;
  student_id: string;
  class_id: string;
  subject: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentTeachingClassRequest {
  class_id: string;
  subject: string;
}

export interface StudentProfileLevel {
  id: string;
  student_id: string;
  subject: string;
  level: string;
  level_description?: string;
  assessment_date: string;
  assessed_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentProfileLevelRequest {
  subject: string;
  level: string;
  level_description?: string;
  assessment_date: string;
  assessed_by?: string;
}

export interface StudentProfileTag {
  id: string;
  student_id: string;
  tag_name: string;
  tag_value?: string;
  tag_category: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentProfileTagRequest {
  tag_name: string;
  tag_value?: string;
  tag_category: string;
  created_by?: string;
}

export interface StudentDetail {
  student: Student;
  class_name?: string;
  grade_level_name?: string;
  teaching_classes: StudentTeachingClass[];
  profile_levels: StudentProfileLevel[];
  profile_tags: StudentProfileTag[];
}

// Form validation schemas
export interface StudentFormData extends CreateStudentRequest {
  id?: string;
}

export interface StudentFormErrors {
  student_id?: string;
  name?: string;
  gender?: string;
  birth_date?: string;
  id_number?: string;
  phone?: string;
  email?: string;
  guardian_phone?: string;
  enrollment_date?: string;
}

// Component prop types
export interface StudentTableProps {
  students: Student[];
  loading?: boolean;
  onEdit: (student: Student) => void;
  onDelete: (id: string) => void;
  onViewDetail: (student: Student) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

export interface StudentFormProps {
  student?: Student;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: StudentFormData) => Promise<void>;
  loading?: boolean;
}

export interface StudentDetailProps {
  student?: StudentDetail;
  open: boolean;
  onClose: () => void;
  loading?: boolean;
}

export interface StudentSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  classId?: string;
  gradeId?: string;
  className?: string;
}

// Filter and sort options
export const STUDENT_STATUS_OPTIONS = [
  { value: 'active', label: '在校' },
  { value: 'inactive', label: '休学' },
  { value: 'graduated', label: '毕业' },
  { value: 'transferred', label: '转学' },
] as const;

export const STUDENT_PROFILE_LEVELS = [
  { value: 'A+', label: 'A+' },
  { value: 'A', label: 'A' },
  { value: 'B+', label: 'B+' },
  { value: 'B', label: 'B' },
  { value: 'C+', label: 'C+' },
  { value: 'C', label: 'C' },
  { value: 'D+', label: 'D+' },
  { value: 'D', label: 'D' },
] as const;

export const STUDENT_TAG_CATEGORIES = [
  { value: 'academic', label: '学业' },
  { value: 'behavior', label: '行为' },
  { value: 'interest', label: '兴趣' },
  { value: 'ability', label: '能力' },
  { value: 'other', label: '其他' },
] as const;

export const GUARDIAN_RELATIONS = [
  { value: 'father', label: '父亲' },
  { value: 'mother', label: '母亲' },
  { value: 'grandfather', label: '祖父' },
  { value: 'grandmother', label: '祖母' },
  { value: 'uncle', label: '叔叔' },
  { value: 'aunt', label: '阿姨' },
  { value: 'other', label: '其他' },
] as const;

export const GENDER_OPTIONS = [
  { value: 'male', label: '男' },
  { value: 'female', label: '女' },
] as const;

// Default values
export const DEFAULT_STUDENT_SEARCH: Required<StudentSearchParams> = {
  name: '',
  student_id: '',
  class_id: '',
  grade_level_id: '',
  status: '',
  profile_level: '',
  limit: 20,
  offset: 0,
};