import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Upload,
  Edit,
  Trash2,
  Plus,
  ZoomIn,
  ZoomOut,
} from "lucide-react";
import { BatchImage } from "@/services/scanApi";
import DraggableImage from "@/components/DraggableImage";

interface StudentImageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentName: string;
  studentId: string;
  studentClass: string;
  images: BatchImage[];
  onImageOperation: (type: 'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add', image?: BatchImage) => void;
}

const StudentImageDialog: React.FC<StudentImageDialogProps> = ({
  open,
  onOpenChange,
  studentName,
  studentId,
  studentClass,
  images,
  onImageOperation,
}) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(1);

  // 移除handleWheel函数，因为DraggableImage组件会处理缩放

  const selectedImage = images[selectedImageIndex];

  // 处理Dialog关闭时的重置
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      // 关闭时重置状态
      setSelectedImageIndex(0);
      setZoomLevel(1);
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-[80vw] max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle>学生图片详情 - {studentName}</DialogTitle>
        </DialogHeader>
        
        <div className="flex h-[80vh]">
          {/* 左侧图片列表 */}
          <div className="w-64 border-r bg-muted/20 p-4">
            <div className="space-y-2 mb-4">
              <div className="text-sm"><strong>姓名：</strong>{studentName}</div>
              <div className="text-sm"><strong>学号：</strong>{studentId}</div>
              <div className="text-sm"><strong>班级：</strong>{studentClass}</div>
            </div>
            
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {images.map((image, index) => (
                <div
                  key={image.id}
                  className={`p-2 border rounded cursor-pointer transition-colors ${
                    selectedImageIndex === index ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                  }`}
                  onClick={() => {
                    setSelectedImageIndex(index);
                    setZoomLevel(1);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <img 
                      src={image.url} 
                      alt={`页面 ${image.page_number}`}
                      className="w-12 h-16 object-cover rounded"
                    />
                    <div className="flex-1 text-xs">
                      <div>页码：{image.page_number}</div>
                      <div>状态：{
                        image.status === 'normal' ? '正常' :
                        image.status === 'ocr_error' ? 'OCR异常' :
                        image.status === 'blank' ? '空白页' :
                        image.status === 'duplicate' ? '重复页' :
                        image.status === 'pending' ? '待分发' : '未知'
                      }</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <Button
              className="w-full mt-4"
              variant="outline"
              onClick={() => onImageOperation('add')}
            >
              <Plus className="h-4 w-4 mr-2" />
              绑定图片
            </Button>
          </div>
          
          {/* 右侧大图预览 */}
          <div className="flex-1 flex flex-col">
            {selectedImage && (
              <>
                <div className="flex-1 flex items-center justify-center overflow-hidden bg-muted/10">
                  <DraggableImage
                    src={selectedImage.url}
                    alt={`页面 ${selectedImage.page_number}`}
                    className="w-full h-full"
                    zoomLevel={zoomLevel}
                    onZoomChange={setZoomLevel}
                  />
                </div>
                
                {/* 底部控制栏 */}
                <div className="p-4 border-t bg-background">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}
                      >
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                      <span className="text-sm font-medium">{Math.round(zoomLevel * 100)}%</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setZoomLevel(prev => Math.min(3, prev + 0.1))}
                      >
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setZoomLevel(1)}
                      >
                        重置
                      </Button>
                    </div>
                    
                    <div className="text-sm text-muted-foreground">
                      页码：{selectedImage.page_number} | 
                      状态：{
                        selectedImage.status === 'normal' ? '正常' :
                        selectedImage.status === 'ocr_error' ? 'OCR异常' :
                        selectedImage.status === 'blank' ? '空白页' :
                        selectedImage.status === 'duplicate' ? '重复页' :
                        selectedImage.status === 'pending' ? '待分发' : '未知'
                      }
                    </div>
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onImageOperation('edit_student', selectedImage)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      修改学号
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onImageOperation('edit_page', selectedImage)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      修改页码
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onImageOperation('replace', selectedImage)}
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      替换图片
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => onImageOperation('delete', selectedImage)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      删除图片
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StudentImageDialog;
