use std::env;
use bb8::Pool;
use bb8_redis::RedisConnectionManager;
use redis::Client;
use tracing::log;

pub type RedisPool = Pool<RedisConnectionManager>;

pub async fn get_redis_client() -> anyhow::Result<RedisPool> {
    let redis_url = env::var("REDIS_URI").unwrap_or("redis://127.0.0.1/".to_string());
    log::info!("Using redis url: {}", redis_url);
    let manager = RedisConnectionManager::new(redis_url)?;
    // 创建连接池
    let pool = Pool::builder().build(manager).await?;
    Ok(pool)
    // let client = Client::open(redis_url).expect("Couldn't create Redis client");
    // client
}
#[cfg(test)]
mod tests {
    use std::num::NonZeroUsize;
    use dotenvy::dotenv;
    use redis::{AsyncCommands, AsyncIter};
    use crate::utils::redis_client::get_redis_client;

    #[tokio::test]
    pub async fn test_redis() -> anyhow::Result<()> {
        dotenv().ok();
        let pool = get_redis_client().await?;
        let mut conn = pool.get().await?;
        for i in 0..10 {
            conn.rpush::<_, _, ()>("list_a", i.to_string()).await?;
        }
        let a: Vec<String> = conn.lpop("list_a", NonZeroUsize::new(15)).await?;
        let mut iter = conn.scan_match::<_, String>("list_*").await?;
        while let Some(key) = iter.next_item().await {
            println!("Found key: {}", key);
        }
        log::info!("a: {:?}", a);
        Ok(())
    }
}