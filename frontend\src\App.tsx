import {RouterProvider} from 'react-router-dom';
import {AuthProvider} from './contexts/AuthContext';
import {ThemeProvider} from './contexts/ThemeContext';
import {router} from './router';
import {Toaster} from "sonner";


function App() {
    return (
        <ThemeProvider>
            <AuthProvider>
                <RouterProvider router={router}/>
                <Toaster position="top-center"/>
            </AuthProvider>
        </ThemeProvider>
    );
}

export default App;
