/**
 * 学生管理 API 服务
 * 提供学生相关的 API 调用接口
 */

import apiClient from './apiClient';
import {
  Student,
  CreateStudentRequest,
  UpdateStudentRequest,
  StudentSearchParams,
  StudentDetail,
  StudentTeachingClass,
  CreateStudentTeachingClassRequest,
  StudentProfileLevel,
  CreateStudentProfileLevelRequest,
  StudentProfileTag,
  CreateStudentProfileTagRequest,
} from '@/types/student';

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  code: number;
  message?: string;
  data: T;
  meta?: any;
}

// Student API service
export const studentsApi = {
  // Get all students with optional search parameters
  async getStudents(params?: StudentSearchParams): Promise<ApiResponse<Student[]>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/v1/students?${queryString}` : '/api/v1/students';
    
    return apiClient.get<ApiResponse<Student[]>>(url);
  },

  // Get student by ID
  async getStudentById(id: string): Promise<ApiResponse<Student>> {
    return apiClient.get<ApiResponse<Student>>(`/api/v1/students/${id}`);
  },

  // Get student detail with related information
  async getStudentDetail(id: string): Promise<ApiResponse<StudentDetail>> {
    return apiClient.get<ApiResponse<StudentDetail>>(`/api/v1/students/${id}/detail`);
  },

  // Create a new student
  async createStudent(student: CreateStudentRequest): Promise<ApiResponse<Student>> {
    return apiClient.post<ApiResponse<Student>>('/api/v1/students', student);
  },

  // Update an existing student
  async updateStudent(id: string, student: UpdateStudentRequest): Promise<ApiResponse<Student>> {
    return apiClient.put<ApiResponse<Student>>(`/api/v1/students/${id}`, student);
  },

  // Delete a student
  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(`/api/v1/students/${id}`);
  },

  // Add student to teaching class
  async addTeachingClass(studentId: string, teachingClass: CreateStudentTeachingClassRequest): Promise<ApiResponse<StudentTeachingClass>> {
    return apiClient.post<ApiResponse<StudentTeachingClass>>(
      `/api/v1/students/${studentId}/teaching-classes`,
      teachingClass
    );
  },

  // Remove student from teaching class
  async removeTeachingClass(studentId: string, classId: string, subject: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(
      `/api/v1/students/${studentId}/teaching-classes/${classId}/${encodeURIComponent(subject)}`
    );
  },

  // Update student profile level
  async updateProfileLevel(studentId: string, profileLevel: CreateStudentProfileLevelRequest): Promise<ApiResponse<StudentProfileLevel>> {
    return apiClient.post<ApiResponse<StudentProfileLevel>>(
      `/api/v1/students/${studentId}/profile-levels`,
      profileLevel
    );
  },

  // Update student profile tag
  async updateProfileTag(studentId: string, profileTag: CreateStudentProfileTagRequest): Promise<ApiResponse<StudentProfileTag>> {
    return apiClient.post<ApiResponse<StudentProfileTag>>(
      `/api/v1/students/${studentId}/profile-tags`,
      profileTag
    );
  },

  // Remove student profile tag
  async removeProfileTag(studentId: string, tagName: string): Promise<ApiResponse<void>> {
    return apiClient.delete<ApiResponse<void>>(
      `/api/v1/students/${studentId}/profile-tags/${encodeURIComponent(tagName)}`
    );
  },

  // Get students count by class
  async getStudentsCountByClass(classId: string): Promise<ApiResponse<number>> {
    return apiClient.get<ApiResponse<number>>(`/api/v1/students/count/by-class/${classId}`);
  },

  // Get students count by grade
  async getStudentsCountByGrade(gradeId: string): Promise<ApiResponse<number>> {
    return apiClient.get<ApiResponse<number>>(`/api/v1/students/count/by-grade/${gradeId}`);
  },
};

// 导出默认学生API
export default studentsApi;