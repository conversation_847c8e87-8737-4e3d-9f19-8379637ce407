use crate::{
    model::homework::homework::{CreateHomeworkPayload, Homework, HomeworkStatistics},
    utils::api_response::ApiResponse,
};
use sqlx::{PgPool, Pool, Postgres, Result};
use uuid::Uuid;
#[derive(Clone)]
pub struct HomeworkService {
    db_pool: PgPool,
}

impl HomeworkService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

impl HomeworkService {
    /**
     * 作者：张瀚
     * 说明：获取作业管理统计数据
     */
    pub async fn get_statistics(
        &self,
        tenant_id: String,
    ) -> Result<ApiResponse<HomeworkStatistics>, ApiResponse<()>> {
        let data = HomeworkStatistics {
            total_homeworks: 100,
            completed_homeworks: 40,
            in_progress_homeworks: 60,
            total_students: 666,
        };
        Ok(ApiResponse::success(data, None))
    }

    pub async fn create_homework(&self, payload: &CreateHomeworkPayload) -> Result<Homework> {
        let new_homework = sqlx::query_as::<_, Homework>(
            "INSERT INTO {schema}.Homework (name, type, grade_level, description, start_time, end_time, status, created_by) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *"
        )
        .bind(&payload.name)
        .bind(&payload.r#type)
        .bind(&payload.grade_level)
        .bind(&payload.description)
        .bind(payload.start_time)
        .bind(payload.end_time)
        .bind(&payload.status)
        .bind(payload.created_by)
        .fetch_one(&self.db_pool)
        .await?;
        Ok(new_homework)
    }

    pub async fn get_homework(&self, homework_id: Uuid) -> Result<Option<Homework>> {
        let homework =
            sqlx::query_as::<_, Homework>("SELECT * FROM {schema}.Homework WHERE id = $1")
                .bind(homework_id)
                .fetch_optional(&self.db_pool)
                .await?;
        Ok(homework)
    }
}
