import { paperApi } from "@/services/paperApi";
import { useEffect, useRef, useState } from "react";
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserPlus, ChevronLeft, ChevronRight, LogIn } from 'lucide-react';
import { TeachingAid } from "@/services/teachingAidsApi";
import { textbookApi } from "@/services/questionApi";
import { Paper, textbookPaperApi } from "@/services/textbookPaperApi";
import { PageParams } from "@/types";
import { log } from "console";

interface BindpapersToHomeworkProps {
    open: boolean,
    onOpenChange: (open: boolean) => void,
    textbookList: TeachingAid[],
    onBind: (paperIds: string[]) => Promise<void>,
    title?: string,
    description?: string,
}

const AddpapersByToHomework: React.FC<BindpapersToHomeworkProps> = ({
    open,
    onOpenChange,
    textbookList,
    onBind,
    title = '绑定试卷到作业',
    description = '请选择教辅并勾选要绑定的试卷',
}) => {

    const [selectedTextbookId, setSelectedTextbookId] = useState<string>('');
    const [selectedPapers, setSelectedPapers] = useState<string[]>([]);
    const [papers, setPapers] = useState<Paper[]>([]);
    const [isBinding, setIsBinding] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // 分页状态
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(10); // 每页显示数量
    const [totalCount, setTotalCount] = useState(0);
    const [totalPages, setTotalPages] = useState(0);

    // 加载试卷数据
    const loadPapers = async (textbookId: string, page: number = 1) => {
        if (!textbookId) {
            setPapers([]);
            setSelectedPapers([]);
            setTotalCount(0);
            setTotalPages(0);
            return;
        }
        setIsLoading(true);
        const params: PageParams = {
            page: page,
            page_size: pageSize,
        };
        try {
            textbookPaperApi.getPapersByTextbookId(textbookId, params).then(res => {
                const { success, data, pagination } = res;
                if (!success) {
                    setPapers([]);
                    setSelectedPapers([]);
                    setTotalCount(0);
                    setTotalPages(0);
                    return;
                }
                const paperList = Array.isArray(data) ? data : [];
                setPapers(paperList);
                setSelectedPapers([]);
                setTotalCount(pagination.total);
                setTotalPages(pagination.total_pages);
            });
        } catch (error) {
            console.error('加载试卷失败:', error);
            setPapers([]);
            setSelectedPapers([]);
            setTotalCount(0);
            setTotalPages(0);
        } finally {
            setIsLoading(false);
        }
    };

    //选择教辅后拉取试卷
    useEffect(() => {
        setCurrentPage(1); // 重置到第一页
        loadPapers(selectedTextbookId, 1);

    }, [selectedTextbookId]);

    // 分页切换
    const handlePageChange = (page: number) => {
        if (page < 1 || page > totalPages || isLoading) return;
        setCurrentPage(page);
        loadPapers(selectedTextbookId, page);
    };

    // 全选/取消全选
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedPapers(papers.map((p) => p.id));
        } else {
            setSelectedPapers([]);
        }
    };

    // 单个勾选
    const handlePaperToggle = (paperId: string) => {
        setSelectedPapers((prev) =>
            prev.includes(paperId)
                ? prev.filter((id) => id !== paperId)
                : [...prev, paperId]
        );
    };

    // 绑定试卷
    const handleBind = async () => {
        if (selectedPapers.length === 0) return;
        setIsBinding(true);
        try {
            await onBind(selectedPapers);
            setSelectedPapers([]);
            setPapers([]);
            setSelectedTextbookId('');
            setCurrentPage(1);
            setTotalCount(0);
            setTotalPages(0);
            onOpenChange(false);
        } catch (error) {
            console.error('绑定试卷失败:', error);
        } finally {
            setIsBinding(false);
        }
    };

    // 取消
    const handleCancel = () => {
        setSelectedPapers([]);
        setPapers([]);
        setSelectedTextbookId('');
        setCurrentPage(1);
        setTotalCount(0);
        setTotalPages(0);
        onOpenChange(false);
    };

    // Note: Radix UI Checkbox doesn't support indeterminate state like native checkbox
    // The partial selection state is visually handled by the checked prop logic

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[700px]">
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                    <DialogDescription>{description}</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    {/* 教辅选择 */}
                    <div>
                        <Label htmlFor="textbook_select">选择教辅</Label>
                        <Select
                            value={selectedTextbookId}
                            onValueChange={(value) => setSelectedTextbookId(value)}
                            disabled={isLoading || isBinding}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="请选择教辅" />
                            </SelectTrigger>
                            <SelectContent>
                                {textbookList.map((textbook) => (
                                    <SelectItem key={textbook.id} value={textbook.id}>
                                        {textbook.title}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* 试卷列表 */}
                    {selectedTextbookId && (
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <Label>
                                    试卷列表 {isLoading ? '(加载中...)' : `(第${currentPage}页，共${papers.length}份)`}
                                </Label>
                                {/* 分页控件 */}
                                {totalPages > 1 && (
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage <= 1 || isLoading}
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                        </Button>
                                        <span className="text-sm text-muted-foreground">
                                            {currentPage} / {totalPages}
                                        </span>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage >= totalPages || isLoading}
                                        >
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                            <ScrollArea className="h-[300px]">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-12">
                                                <Checkbox
                                                    checked={papers.length > 0 && selectedPapers.length === papers.length}
                                                    onCheckedChange={(checked) => handleSelectAll(!!checked)}
                                                    disabled={papers.length === 0}
                                                />
                                            </TableHead>
                                            <TableHead>试卷名称</TableHead>
                                            <TableHead>序号</TableHead>
                                            <TableHead>创建时间</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {papers.map((paper) => (
                                            <TableRow key={paper.id}>
                                                <TableCell>
                                                    <Checkbox
                                                        checked={selectedPapers.includes(paper.id)}
                                                        onCheckedChange={() => handlePaperToggle(paper.id)}
                                                    />
                                                </TableCell>
                                                <TableCell className="font-medium">{paper.paper_name}</TableCell>
                                                <TableCell>{'-'}</TableCell>
                                                <TableCell>{paper.created_at ? new Date(paper.created_at).toLocaleDateString() : '-'}</TableCell>
                                            </TableRow>
                                        ))}
                                        {papers.length === 0 && !isLoading && (
                                            <TableRow>
                                                <TableCell colSpan={4} className="text-center text-muted-foreground">
                                                    暂无试卷数据
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </ScrollArea>
                        </div>
                    )}

                    {/* 选中试卷数量提示 */}
                    {selectedPapers.length > 0 && (
                        <div className="text-sm text-muted-foreground">已选择 {selectedPapers.length} 份试卷</div>
                    )}
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={handleCancel} disabled={isBinding}>
                        取消
                    </Button>
                    <Button
                        onClick={handleBind}
                        disabled={selectedPapers.length === 0 || isBinding}
                    >
                        <UserPlus className="h-4 w-4 mr-2" />
                        {isBinding ? '绑定中...' : `绑定 (${selectedPapers.length})`}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default AddpapersByToHomework;