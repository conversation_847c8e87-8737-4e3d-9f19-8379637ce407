import {createContext, useState, useContext, ReactNode} from 'react';
import { LoginForm } from "@/pages/LoginPage.tsx";
import apiClient from "@/services/apiClient.ts";
import {isAxiosError} from "axios";

// 更新 AuthContextType 接口以包含 request 方法
interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  login: (data: LoginForm) => Promise<void>;
  logout: () => void;
}


const AuthContext = createContext<AuthContextType | undefined>(undefined);
export { AuthContext };

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [token, setToken] = useState<string | null>(() => localStorage.getItem('token'));
  const [isAuthenticated, setIsAuthenticated] = useState(!!token);

  const login = async (data: LoginForm) => {
    try {
      const res = await apiClient.post('/api/v1/auth/login', data);
      const { access_token, refresh_token } = res.data;

      // 保存 token 到 state 和 localStorage
      setToken(access_token);
      localStorage.setItem('token', access_token);
      if (refresh_token) {
        localStorage.setItem('refresh_token', refresh_token);
      }
      setIsAuthenticated(true);
    } catch (err: unknown) {
      if (isAxiosError(err)) {
        const msg = err.response?.data?.message || '请求失败';
        throw new Error(msg);
      } else if (err instanceof Error) {
        throw new Error(err.message);
      } else {
        throw new Error('未知错误');
      }
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setIsAuthenticated(false);
  };


  const contextValue: AuthContextType  = {
    isAuthenticated,
    token,
    login,
    logout,
  };

  return (
      <AuthContext.Provider value={contextValue}>
        {children}
      </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
