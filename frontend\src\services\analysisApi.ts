// API service for academic analysis
import apiClient from '../services/examApi';

// Academic Statistics Types
export interface StudentStatistics {
  id: string;
  student_id: string;
  exam_id: string;
  total_score: number;
  total_possible: number;
  percentage: number;
  class_rank: number;
  grade_rank: number;
  school_rank: number;
  absent_subjects: string[];
  absent_reason?: string;
  subject_scores: SubjectScore[];
  trend_analysis: TrendAnalysis;
  ability_level: string;
  created_at: string;
  updated_at: string;
}

export interface SubjectScore {
  subject_id: string;
  subject_name: string;
  score: number;
  total_possible: number;
  percentage: number;
  rank: number;
  question_scores: QuestionScore[];
}

export interface QuestionScore {
  question_id: string;
  question_number: string;
  score: number;
  total_possible: number;
  correct: boolean;
  knowledge_points: string[];
}

export interface TrendAnalysis {
  previous_exams: PreviousExamData[];
  trend_direction: string;
  improvement_rate: number;
  consistency_score: number;
}

export interface PreviousExamData {
  exam_id: string;
  exam_date: string;
  score: number;
  percentage: number;
  rank: number;
}

// Question Analysis Types
export interface QuestionAnalysis {
  id: string;
  question_id: string;
  exam_id: string;
  total_attempts: number;
  correct_count: number;
  incorrect_count: number;
  average_score: number;
  score_distribution: any;
  difficulty_coefficient: number;
  discrimination_index: number;
  knowledge_point_mastery: KnowledgePointMastery[];
  option_analysis?: OptionAnalysis[];
  created_at: string;
}

export interface KnowledgePointMastery {
  knowledge_point: string;
  mastery_rate: number;
  student_count: number;
  avg_score: number;
}

export interface OptionAnalysis {
  option: string;
  selection_count: number;
  selection_rate: number;
  correct_option: boolean;
}

// Comprehensive Analysis Types
export interface ExamAnalysis {
  id: string;
  exam_id: string;
  total_students: number;
  participated_students: number;
  absent_students: number;
  average_score: number;
  pass_rate: number;
  excellence_rate: number;
  score_distribution: any;
  subject_analysis: SubjectAnalysis[];
  class_comparison: ClassComparison[];
  grade_comparison: GradeComparison[];
  weak_knowledge_points: WeakKnowledgePoint[];
  improvement_suggestions: string[];
  created_at: string;
}

export interface SubjectAnalysis {
  subject_id: string;
  subject_name: string;
  average_score: number;
  pass_rate: number;
  excellence_rate: number;
  difficulty_rating: string;
  performance_comparison: string;
}

export interface ClassComparison {
  class_id: string;
  class_name: string;
  average_score: number;
  pass_rate: number;
  excellence_rate: number;
  rank: number;
  student_count: number;
}

export interface GradeComparison {
  grade_level: string;
  average_score: number;
  pass_rate: number;
  excellence_rate: number;
  student_count: number;
}

export interface WeakKnowledgePoint {
  knowledge_point: string;
  mastery_rate: number;
  affected_students: number;
  suggested_actions: string[];
}

// Student Grade Labels Types
export interface StudentGradeLabel {
  id: string;
  student_id: string;
  subject_id: string;
  ability_level: string;
  academic_tags: string[];
  behavior_tags: string[];
  interest_tags: string[];
  capability_tags: string[];
  last_updated: string;
  updated_by: string;
  change_reason: string;
  created_at: string;
}

export interface GradeLabelHistory {
  id: string;
  student_id: string;
  subject_id: string;
  previous_level: string;
  new_level: string;
  change_reason: string;
  updated_by: string;
  updated_at: string;
}

// Learning Record Types
export interface LearningRecord {
  id: string;
  student_id: string;
  question_id: string;
  grading_record_id: string;
  original_score: number;
  standardized_score: number;
  score_percentage: number;
  rank_info: RankInfo;
  ability_analysis: AbilityAnalysis;
  learning_suggestions: LearningSuggestion[];
  historical_comparison: HistoricalComparison;
  knowledge_point_mastery: KnowledgePointMastery[];
  version: number;
  created_at: string;
  updated_at: string;
}

export interface RankInfo {
  class_rank: number;
  grade_rank: number;
  school_rank: number;
  percentile: number;
}

export interface AbilityAnalysis {
  mastered_points: string[];
  weak_points: string[];
  ability_level: string;
  improvement_areas: string[];
}

export interface LearningSuggestion {
  type: string;
  description: string;
  recommended_exercises: string[];
  improvement_direction: string;
  priority: number;
}

export interface HistoricalComparison {
  previous_scores: number[];
  trend_analysis: string;
  improvement_rate: number;
  consistency_score: number;
  progress_indicators: string[];
}

// Parent View Types
export interface ParentChildInfo {
  student_id: string;
  student_name: string;
  school_id: string;
  school_name: string;
  class_name: string;
  relationship: string;
  verified: boolean;
  verification_date?: string;
}

export interface ParentDashboard {
  children: ParentChildInfo[];
  recent_exams: RecentExamInfo[];
  performance_summary: PerformanceSummary[];
  notifications: ParentNotification[];
}

export interface RecentExamInfo {
  exam_id: string;
  exam_name: string;
  exam_date: string;
  student_id: string;
  score: number;
  rank: number;
  subject: string;
}

export interface PerformanceSummary {
  student_id: string;
  average_score: number;
  trend: string;
  strong_subjects: string[];
  weak_subjects: string[];
  recent_improvement: boolean;
}

export interface ParentNotification {
  id: string;
  type: string;
  message: string;
  student_id: string;
  read: boolean;
  created_at: string;
}

// Request/Response Types
export interface AnalysisRequest {
  exam_id: string;
  analysis_type: string;
  filters?: any;
}

export interface GradeLabelUpdateRequest {
  student_id: string;
  subject_id: string;
  ability_level: string;
  tags: {
    academic_tags: string[];
    behavior_tags: string[];
    interest_tags: string[];
    capability_tags: string[];
  };
  change_reason: string;
}

export interface LearningRecordQuery {
  student_id?: string;
  exam_id?: string;
  subject_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  page_size?: number;
}

// Student Statistics API
export const studentStatisticsApi = {
  // Get student statistics
  getStudentStatistics: async (examId: string, studentId: string): Promise<StudentStatistics> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/exams/${examId}/statistics`);
  },

  // Get multiple students statistics
  getStudentsStatistics: async (examId: string, params?: any): Promise<StudentStatistics[]> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/students/statistics`, { params });
  },

  // Get student trend analysis
  getStudentTrend: async (studentId: string, subjectId?: string): Promise<TrendAnalysis> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/trends`, {
      params: { subject_id: subjectId }
    });
  },

  // Get student subject performance
  getStudentSubjectPerformance: async (studentId: string, subjectId: string): Promise<SubjectScore[]> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/subjects/${subjectId}/performance`);
  },

  // Get student ranking
  getStudentRanking: async (examId: string, studentId: string): Promise<any> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/students/${studentId}/ranking`);
  },
};

// Question Analysis API
export const questionAnalysisApi = {
  // Get question analysis
  getQuestionAnalysis: async (examId: string, questionId: string): Promise<QuestionAnalysis> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/questions/${questionId}/analysis`);
  },

  // Get all questions analysis for exam
  getExamQuestionsAnalysis: async (examId: string): Promise<QuestionAnalysis[]> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/questions/analysis`);
  },

  // Get knowledge point mastery
  getKnowledgePointMastery: async (examId: string, knowledgePoint: string): Promise<KnowledgePointMastery> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/knowledge-points/${knowledgePoint}/mastery`);
  },

  // Get question difficulty analysis
  getQuestionDifficulty: async (examId: string): Promise<any> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/questions/difficulty`);
  },

  // Get question discrimination analysis
  getQuestionDiscrimination: async (examId: string): Promise<any> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/questions/discrimination`);
  },
};

// Comprehensive Analysis API
export const comprehensiveAnalysisApi = {
  // Get exam analysis
  getExamAnalysis: async (examId: string): Promise<ExamAnalysis> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/comprehensive`);
  },

  // Get subject comparison
  getSubjectComparison: async (examId: string): Promise<SubjectAnalysis[]> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/subjects/comparison`);
  },

  // Get class comparison
  getClassComparison: async (examId: string): Promise<ClassComparison[]> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/classes/comparison`);
  },

  // Get grade comparison
  getGradeComparison: async (examId: string): Promise<GradeComparison[]> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/grades/comparison`);
  },

  // Get weak knowledge points
  getWeakKnowledgePoints: async (examId: string): Promise<WeakKnowledgePoint[]> => {
    return apiClient.get(`/api/v1/analysis/exams/${examId}/weak-knowledge-points`);
  },

  // Generate analysis report
  generateAnalysisReport: async (examId: string, reportType: string): Promise<any> => {
    return apiClient.post(`/api/v1/analysis/exams/${examId}/reports/generate`, { report_type: reportType });
  },

  // Get historical comparison
  getHistoricalComparison: async (examIds: string[]): Promise<any> => {
    return apiClient.post('/api/v1/analysis/exams/historical-comparison', { exam_ids: examIds });
  },
};

// Grade Label API
export const gradeLabelApi = {
  // Get student grade labels
  getStudentGradeLabels: async (studentId: string): Promise<StudentGradeLabel[]> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/grade-labels`);
  },

  // Update student grade label
  updateStudentGradeLabel: async (labelData: GradeLabelUpdateRequest): Promise<StudentGradeLabel> => {
    return apiClient.put('/api/v1/analysis/students/grade-labels', labelData);
  },

  // Get grade label history
  getGradeLabelHistory: async (studentId: string, subjectId?: string): Promise<GradeLabelHistory[]> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/grade-labels/history`, {
      params: { subject_id: subjectId }
    });
  },

  // Get class grade labels
  getClassGradeLabels: async (classId: string): Promise<StudentGradeLabel[]> => {
    return apiClient.get(`/api/v1/analysis/classes/${classId}/grade-labels`);
  },

  // Batch update grade labels
  batchUpdateGradeLabels: async (updates: GradeLabelUpdateRequest[]): Promise<boolean> => {
    return apiClient.post('/api/v1/analysis/students/grade-labels/batch', { updates });
  },
};

// Learning Record API
export const learningRecordApi = {
  // Get learning records
  getLearningRecords: async (params: LearningRecordQuery): Promise<LearningRecord[]> => {
    return apiClient.get('/api/v1/analysis/learning-records', { params });
  },

  // Get learning record by ID
  getLearningRecord: async (recordId: string): Promise<LearningRecord> => {
    return apiClient.get(`/api/v1/analysis/learning-records/${recordId}`);
  },

  // Generate learning records for exam
  generateLearningRecords: async (examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/analysis/exams/${examId}/learning-records/generate`);
  },

  // Get student learning trajectory
  getStudentLearningTrajectory: async (studentId: string, subjectId?: string): Promise<any> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/learning-trajectory`, {
      params: { subject_id: subjectId }
    });
  },

  // Export learning records
  exportLearningRecords: async (params: LearningRecordQuery, format: string): Promise<any> => {
    return apiClient.get('/api/v1/analysis/learning-records/export', {
      params: { ...params, format }
    });
  },

  // Get learning record statistics
  getLearningRecordStats: async (studentId: string): Promise<any> => {
    return apiClient.get(`/api/v1/analysis/students/${studentId}/learning-records/stats`);
  },
};

// Parent View API
export const parentViewApi = {
  // Get parent dashboard
  getParentDashboard: async (parentId: string): Promise<ParentDashboard> => {
    return apiClient.get(`/api/v1/analysis/parents/${parentId}/dashboard`);
  },

  // Get parent children info
  getParentChildren: async (parentId: string): Promise<ParentChildInfo[]> => {
    return apiClient.get(`/api/v1/analysis/parents/${parentId}/children`);
  },

  // Add child to parent account
  addChildToParent: async (parentId: string, studentId: string, relationship: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/analysis/parents/${parentId}/children`, {
      student_id: studentId,
      relationship: relationship
    });
  },

  // Verify parent-child relationship
  verifyParentChildRelationship: async (parentId: string, studentId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/analysis/parents/${parentId}/children/${studentId}/verify`);
  },

  // Get child performance for parent
  getChildPerformance: async (parentId: string, studentId: string): Promise<PerformanceSummary> => {
    return apiClient.get(`/api/v1/analysis/parents/${parentId}/children/${studentId}/performance`);
  },

  // Get child learning records for parent
  getChildLearningRecords: async (parentId: string, studentId: string, params?: any): Promise<LearningRecord[]> => {
    return apiClient.get(`/api/v1/analysis/parents/${parentId}/children/${studentId}/learning-records`, { params });
  },

  // Get parent notifications
  getParentNotifications: async (parentId: string): Promise<ParentNotification[]> => {
    return apiClient.get(`/api/v1/analysis/parents/${parentId}/notifications`);
  },

  // Mark notification as read
  markNotificationAsRead: async (notificationId: string): Promise<boolean> => {
    return apiClient.put(`/api/v1/analysis/notifications/${notificationId}/read`);
  },

  // Switch school context for parent
  switchSchoolContext: async (parentId: string, schoolId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/analysis/parents/${parentId}/switch-school`, { school_id: schoolId });
  },
};