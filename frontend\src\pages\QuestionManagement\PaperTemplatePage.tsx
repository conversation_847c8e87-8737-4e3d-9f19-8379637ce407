import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  BookOpen,
  FileText,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  Eye,
  Upload,
  Download,
  Layout,
  Settings,
  Copy,
  Share2
} from 'lucide-react';

import { 
  paperTemplateApi, 
  PaperResponse, 
  CreatePaperRequest, 
  PaperQueryParams,
  questionBankApi,
  QuestionResponse
} from '@/services/questionApi';

const PaperTemplatePage: React.FC = () => {
  // State management
  const [papers, setPapers] = useState<PaperResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [subjectFilter, setSubjectFilter] = useState('');
  const [gradeFilter, setGradeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isQuestionSelectDialogOpen, setIsQuestionSelectDialogOpen] = useState(false);
  const [selectedPaper, setSelectedPaper] = useState<PaperResponse | null>(null);
  
  // Form state
  const [paperForm, setPaperForm] = useState<CreatePaperRequest>({
    title: '',
    subject: '',
    grade_level: 1,
    total_score: 100,
    description: '',
    structure: {},
    questions: []
  });

  // Question selection
  const [availableQuestions, setAvailableQuestions] = useState<QuestionResponse[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<QuestionResponse[]>([]);
  const [questionSearchTerm, setQuestionSearchTerm] = useState('');

  // Load initial data
  useEffect(() => {
    loadPapers();
  }, []);

  // Load papers when filters change
  useEffect(() => {
    loadPapers();
  }, [currentPage, searchTerm, subjectFilter, gradeFilter, statusFilter]);

  const loadPapers = async () => {
    try {
      setLoading(true);
      const params: PaperQueryParams = {
        page: currentPage,
        page_size: 10,
        keyword: searchTerm || undefined,
        subject: subjectFilter || undefined,
        grade_level: gradeFilter ? parseInt(gradeFilter) : undefined,
        status: statusFilter || undefined
      };
      
      const data = await paperTemplateApi.getPapers(params);
      setPapers(data.papers);
      setTotalPages(Math.ceil(data.total / 10));
      setError(null);
    } catch (err) {
      setError('Failed to load papers');
      console.error('Error loading papers:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableQuestions = async () => {
    try {
      const data = await questionBankApi.getQuestions({
        page: 1,
        page_size: 100,
        keyword: questionSearchTerm || undefined,
        subject: paperForm.subject || undefined,
        grade_level: paperForm.grade_level || undefined,
        status: 'published'
      });
      setAvailableQuestions(data.questions);
    } catch (err) {
      console.error('Error loading questions:', err);
    }
  };

  const handleCreatePaper = async () => {
    try {
      if (!paperForm.title || !paperForm.subject || paperForm.questions.length === 0) {
        setError('Please fill in all required fields and add at least one question');
        return;
      }

      await paperTemplateApi.createPaper(paperForm);
      setIsCreateDialogOpen(false);
      resetForm();
      loadPapers();
    } catch (err) {
      setError('Failed to create paper');
      console.error('Error creating paper:', err);
    }
  };

  const handleUpdatePaper = async () => {
    if (!selectedPaper) return;
    
    try {
      await paperTemplateApi.updatePaper(selectedPaper.id, {
        title: paperForm.title,
        description: paperForm.description,
        total_score: paperForm.total_score,
        structure: paperForm.structure
      });
      setIsEditDialogOpen(false);
      setSelectedPaper(null);
      resetForm();
      loadPapers();
    } catch (err) {
      setError('Failed to update paper');
      console.error('Error updating paper:', err);
    }
  };

  const handleDeletePaper = async () => {
    if (!selectedPaper) return;
    
    try {
      await paperTemplateApi.deletePaper(selectedPaper.id);
      setIsDeleteDialogOpen(false);
      setSelectedPaper(null);
      loadPapers();
    } catch (err) {
      setError('Failed to delete paper');
      console.error('Error deleting paper:', err);
    }
  };

  const handlePublishPaper = async (paperId: string) => {
    try {
      await paperTemplateApi.publishPaper(paperId);
      loadPapers();
    } catch (err) {
      setError('Failed to publish paper');
      console.error('Error publishing paper:', err);
    }
  };

  const resetForm = () => {
    setPaperForm({
      title: '',
      subject: '',
      grade_level: 1,
      total_score: 100,
      description: '',
      structure: {},
      questions: []
    });
    setSelectedQuestions([]);
  };

  const openCreateDialog = () => {
    resetForm();
    setIsCreateDialogOpen(true);
  };

  const openEditDialog = (paper: PaperResponse) => {
    setSelectedPaper(paper);
    setPaperForm({
      title: paper.title,
      subject: paper.subject,
      grade_level: paper.grade_level,
      total_score: paper.total_score,
      description: paper.description || '',
      structure: paper.structure,
      questions: paper.questions.map(q => ({
        question_id: q.question_id,
        question_number: q.question_number,
        score: q.score,
        section_name: q.section_name || '',
        display_order: q.display_order
      }))
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (paper: PaperResponse) => {
    setSelectedPaper(paper);
    setIsDeleteDialogOpen(true);
  };

  const openQuestionSelectDialog = () => {
    loadAvailableQuestions();
    setIsQuestionSelectDialogOpen(true);
  };

  const handleQuestionSelect = (question: QuestionResponse) => {
    if (selectedQuestions.find(q => q.id === question.id)) {
      setSelectedQuestions(selectedQuestions.filter(q => q.id !== question.id));
    } else {
      setSelectedQuestions([...selectedQuestions, question]);
    }
  };

  const handleConfirmQuestionSelection = () => {
    const questions = selectedQuestions.map((question, index) => ({
      question_id: question.id,
      question_number: (index + 1).toString(),
      score: 10, // Default score
      section_name: '第一部分',
      display_order: index + 1
    }));
    
    setPaperForm({
      ...paperForm,
      questions: questions,
      total_score: questions.reduce((sum, q) => sum + q.score, 0)
    });
    setIsQuestionSelectDialogOpen(false);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: '草稿', icon: FileText },
      published: { variant: 'default' as const, label: '已发布', icon: CheckCircle2 },
      archived: { variant: 'outline' as const, label: '已归档', icon: BookOpen }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">试卷模板</h1>
          <p className="text-muted-foreground">创建和管理试卷模板</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadPapers}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                新建试卷
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建试卷模板</DialogTitle>
                <DialogDescription>
                  请填写试卷的基本信息并选择题目
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">试卷标题 *</Label>
                    <Input
                      id="title"
                      value={paperForm.title}
                      onChange={(e) => setPaperForm({...paperForm, title: e.target.value})}
                      placeholder="请输入试卷标题"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subject">学科 *</Label>
                    <Select value={paperForm.subject} onValueChange={(value) => setPaperForm({...paperForm, subject: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择学科" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="math">数学</SelectItem>
                        <SelectItem value="chinese">语文</SelectItem>
                        <SelectItem value="english">英语</SelectItem>
                        <SelectItem value="physics">物理</SelectItem>
                        <SelectItem value="chemistry">化学</SelectItem>
                        <SelectItem value="biology">生物</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="grade_level">年级 *</Label>
                    <Select value={paperForm.grade_level.toString()} onValueChange={(value) => setPaperForm({...paperForm, grade_level: parseInt(value)})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">一年级</SelectItem>
                        <SelectItem value="2">二年级</SelectItem>
                        <SelectItem value="3">三年级</SelectItem>
                        <SelectItem value="4">四年级</SelectItem>
                        <SelectItem value="5">五年级</SelectItem>
                        <SelectItem value="6">六年级</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="total_score">总分</Label>
                    <Input
                      id="total_score"
                      type="number"
                      value={paperForm.total_score}
                      onChange={(e) => setPaperForm({...paperForm, total_score: parseInt(e.target.value)})}
                      placeholder="100"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">试卷说明</Label>
                  <Textarea
                    id="description"
                    value={paperForm.description}
                    onChange={(e) => setPaperForm({...paperForm, description: e.target.value})}
                    placeholder="请输入试卷说明"
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>试卷题目</Label>
                    <Button variant="outline" size="sm" onClick={openQuestionSelectDialog}>
                      <Plus className="h-4 w-4 mr-2" />
                      选择题目
                    </Button>
                  </div>
                  {selectedQuestions.length > 0 ? (
                    <div className="space-y-2">
                      {selectedQuestions.map((question, index) => (
                        <div key={question.id} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex-1">
                            <div className="font-medium truncate">{question.question_content}</div>
                            <div className="text-sm text-muted-foreground">
                              {question.subject} · {question.grade_level}年级 · {question.question_type}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              value={paperForm.questions[index]?.score || 10}
                              onChange={(e) => {
                                const newQuestions = [...paperForm.questions];
                                newQuestions[index] = {
                                  ...newQuestions[index],
                                  score: parseInt(e.target.value)
                                };
                                setPaperForm({...paperForm, questions: newQuestions});
                              }}
                              className="w-20"
                              placeholder="分值"
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedQuestions(selectedQuestions.filter(q => q.id !== question.id));
                                setPaperForm({
                                  ...paperForm,
                                  questions: paperForm.questions.filter(q => q.question_id !== question.id)
                                });
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      暂未选择题目，请点击"选择题目"添加
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreatePaper}>
                  创建试卷
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center flex-wrap">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索试卷..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={subjectFilter} onValueChange={setSubjectFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="学科" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部学科</SelectItem>
            <SelectItem value="math">数学</SelectItem>
            <SelectItem value="chinese">语文</SelectItem>
            <SelectItem value="english">英语</SelectItem>
            <SelectItem value="physics">物理</SelectItem>
            <SelectItem value="chemistry">化学</SelectItem>
            <SelectItem value="biology">生物</SelectItem>
          </SelectContent>
        </Select>
        <Select value={gradeFilter} onValueChange={setGradeFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="年级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部年级</SelectItem>
            <SelectItem value="1">一年级</SelectItem>
            <SelectItem value="2">二年级</SelectItem>
            <SelectItem value="3">三年级</SelectItem>
            <SelectItem value="4">四年级</SelectItem>
            <SelectItem value="5">五年级</SelectItem>
            <SelectItem value="6">六年级</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部状态</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="published">已发布</SelectItem>
            <SelectItem value="archived">已归档</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Paper List */}
      <Card>
        <CardHeader>
          <CardTitle>试卷模板列表</CardTitle>
          <CardDescription>
            当前共有 {papers.length} 个试卷模板
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>试卷标题</TableHead>
                <TableHead>学科</TableHead>
                <TableHead>年级</TableHead>
                <TableHead>总分</TableHead>
                <TableHead>题目数量</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>使用次数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {papers.map((paper) => (
                <TableRow key={paper.id}>
                  <TableCell className="font-medium">{paper.title}</TableCell>
                  <TableCell>{paper.subject}</TableCell>
                  <TableCell>{paper.grade_level}年级</TableCell>
                  <TableCell>{paper.total_score}分</TableCell>
                  <TableCell>{paper.questions.length}题</TableCell>
                  <TableCell>{getStatusBadge(paper.status)}</TableCell>
                  <TableCell>{paper.usage_count}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" onClick={() => openEditDialog(paper)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                      {paper.status === 'draft' && (
                        <Button variant="ghost" size="sm" onClick={() => handlePublishPaper(paper.id)}>
                          <Upload className="h-4 w-4" />
                        </Button>
                      )}
                      {paper.status === 'draft' && (
                        <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(paper)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Question Selection Dialog */}
      <Dialog open={isQuestionSelectDialogOpen} onOpenChange={setIsQuestionSelectDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>选择题目</DialogTitle>
            <DialogDescription>
              从题库中选择试卷题目
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索题目..."
                value={questionSearchTerm}
                onChange={(e) => setQuestionSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="max-h-[400px] overflow-y-auto space-y-2">
              {availableQuestions.map((question) => (
                <div
                  key={question.id}
                  className={`p-3 border rounded cursor-pointer transition-colors ${
                    selectedQuestions.find(q => q.id === question.id)
                      ? 'bg-primary/10 border-primary'
                      : 'hover:bg-muted'
                  }`}
                  onClick={() => handleQuestionSelect(question)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium">{question.question_content}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {question.subject} · {question.grade_level}年级 · {question.question_type}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        难度: {question.difficulty_level}
                      </Badge>
                      {selectedQuestions.find(q => q.id === question.id) && (
                        <CheckCircle2 className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsQuestionSelectDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleConfirmQuestionSelection}>
              确定选择 ({selectedQuestions.length})
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑试卷模板</DialogTitle>
            <DialogDescription>
              修改试卷信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_title">试卷标题</Label>
              <Input
                id="edit_title"
                value={paperForm.title}
                onChange={(e) => setPaperForm({...paperForm, title: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_description">试卷说明</Label>
              <Textarea
                id="edit_description"
                value={paperForm.description}
                onChange={(e) => setPaperForm({...paperForm, description: e.target.value})}
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_total_score">总分</Label>
              <Input
                id="edit_total_score"
                type="number"
                value={paperForm.total_score}
                onChange={(e) => setPaperForm({...paperForm, total_score: parseInt(e.target.value)})}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleUpdatePaper}>
              保存
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              你确定要删除试卷模板 "{selectedPaper?.title}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeletePaper}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PaperTemplatePage;