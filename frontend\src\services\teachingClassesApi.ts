import { ApiResponse } from '@/types';
import { ClassesStatistics } from '@/types/classess';
import { CreateTeachingClassesParams, TeachingClasses, TeachingClassesDetail } from '@/types/teachingClasses';
import apiClient from './examApi';

/**
 * 作者：张瀚
 * 说明：行政班级管理API
 */
export const TeachingClassesApi = {
  /**
   * 作者：张瀚
   * 说明：统计班级管理信息
   */
  getStatistics: async (tenantName: string): Promise<ApiResponse<ClassesStatistics>> => {
    return apiClient.get(`/api/v1/tenants/${tenantName}/teachingClasses/getStatistics`);
  },
  /**
   * 作者：张瀚
   * 说明：获取有权限的行政班列表
   */
  getUserClassList: async (tenantName: string): Promise<ApiResponse<TeachingClassesDetail[]>> => {
    return apiClient.get(`/api/v1/tenants/${tenantName}/teachingClasses/getUserClassList`);
  },
  /**
   * 作者：张瀚
   * 说明：创建班级
   */
  createClasses: async (tenantName: string, params: CreateTeachingClassesParams): Promise<ApiResponse<TeachingClasses>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/teachingClasses/createClasses`, params);
  },
};
