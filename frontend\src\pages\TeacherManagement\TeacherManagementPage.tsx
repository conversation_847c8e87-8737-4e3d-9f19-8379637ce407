import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Download, Users, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';

import TeacherTable from './components/TeacherTable';
import TeacherForm from './components/TeacherForm';
import TeacherDetail from './components/TeacherDetail';
import { teachersApi } from '@/services/teacherApi';
import { 
  Teacher, 
  TeacherSearchParams, 
  TeacherFormData,
  TeacherDetailVO,
  TeacherListVO,
  DEFAULT_TEACHER_SEARCH,
  EMPLOYMENT_STATUS_OPTIONS
} from '@/types/teacher';

const TeacherManagementPage: React.FC = () => {
  // State management
  const [teachers, setTeachers] = useState<TeacherListVO[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<TeacherSearchParams>(DEFAULT_TEACHER_SEARCH);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  });
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // Detail state
  const [detailOpen, setDetailOpen] = useState(false);
  const [teacherDetail, setTeacherDetail] = useState<TeacherDetailVO | undefined>();
  const [detailLoading, setDetailLoading] = useState(false);

  // Statistics state
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    byStatus: {} as Record<string, number>,
  });

  // Load teachers data
  const loadTeachers = async (params?: Partial<TeacherSearchParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...searchParams, ...params };
      const response = await teachersApi.getTeachers(finalParams);
      
      if (response.success && response.data) {
        setTeachers(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.page_size,
          total: response.data.total,
          totalPages: response.data.total_pages,
        });
        setSearchParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load teachers:', error);
      toast.error('加载教师列表失败');
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const statusResponse = await teachersApi.getTeachersCountByStatus();
      if (statusResponse.success) {
        const statusCounts = statusResponse.data;
        const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
        const active = statusCounts['在职'] || 0;
        const inactive = total - active;
        
        setStats({
          total,
          active,
          inactive,
          byStatus: statusCounts,
        });
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
    }
  };

  // Initialize data
  useEffect(() => {
    loadTeachers();
    loadStats();
  }, []);

  // Handle search
  const handleSearch = (search: string) => {
    loadTeachers({ ...searchParams, name: search, page: 1 });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof TeacherSearchParams, value: any) => {
    const newParams = { ...searchParams, [key]: value, page: 1 };
    loadTeachers(newParams);
  };

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    loadTeachers({ ...searchParams, page, page_size: pageSize });
  };

  // Handle create teacher
  const handleCreateTeacher = () => {
    setEditingTeacher(undefined);
    setFormOpen(true);
  };

  // Handle edit teacher
  const handleEditTeacher = (teacher: TeacherListVO) => {
    // Convert TeacherListVO to Teacher for editing
    const teacherForEdit: Teacher = {
      id: teacher.id,
      tenant_id: '', // Will be filled by backend
      user_id: '', // Will be filled by backend
      employee_id: teacher.employee_id,
      name: teacher.name,
      phone: teacher.phone,
      email: '',
      gender: undefined,
      date_of_birth: undefined,
      id_card_number: undefined,
      highest_education: undefined,
      graduation_school: undefined,
      major: undefined,
      hire_date: undefined,
      employment_status: teacher.employment_status,
      title: teacher.title,
      teaching_subjects: teacher.teaching_subjects,
      homeroom_class_id: undefined,
      grade_level_id: undefined,
      subject_group_id: undefined,
      office_location: undefined,
      bio: undefined,
      is_active: teacher.is_active,
      created_at: '',
      updated_at: '',
    };
    setEditingTeacher(teacherForEdit);
    setFormOpen(true);
  };

  // Handle view teacher detail
  const handleViewDetail = async (teacher: TeacherListVO) => {
    try {
      setDetailLoading(true);
      setDetailOpen(true);
      const response = await teachersApi.getTeacherDetail(teacher.id);
      if (response.success && response.data) {
        setTeacherDetail(response.data);
      }
    } catch (error) {
      console.error('Failed to load teacher detail:', error);
      toast.error('加载教师详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  // Handle toggle teacher status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await teachersApi.toggleTeacherStatus(id, isActive);
      if (response.success) {
        toast.success(`教师已${isActive ? '启用' : '禁用'}`);
        loadTeachers();
        loadStats(); // Refresh statistics
      }
    } catch (error: any) {
      console.error('Failed to toggle teacher status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  // Handle form submit
  const handleFormSubmit = async (data: TeacherFormData) => {
    try {
      setFormLoading(true);
      
      if (editingTeacher) {
        // Update teacher
        const response = await teachersApi.updateTeacher(editingTeacher.id, data);
        if (response.success) {
          toast.success('教师信息更新成功');
          setFormOpen(false);
          loadTeachers();
          loadStats();
        }
      } else {
        // Create teacher
        const response = await teachersApi.createTeacher(data);
        if (response.success) {
          toast.success('教师创建成功');
          setFormOpen(false);
          loadTeachers();
          loadStats();
        }
      }
    } catch (error: any) {
      console.error('Failed to save teacher:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete teacher
  const handleDeleteTeacher = async (id: string) => {
    if (!confirm('确定要删除这个教师吗？此操作不可恢复。')) {
      return;
    }

    try {
      const response = await teachersApi.deleteTeacher(id);
      if (response.success) {
        toast.success('教师删除成功');
        loadTeachers();
        loadStats();
      }
    } catch (error: any) {
      console.error('Failed to delete teacher:', error);
      toast.error(error.response?.data?.message || '删除失败');
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await teachersApi.exportTeachers(searchParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `teachers-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export teachers:', error);
      toast.error('导出失败');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">教师管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的教师信息，包括教师档案、任职分配和教学能力</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleCreateTeacher}>
            <Plus className="w-4 h-4 mr-2" />
            新增教师
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">教师列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>教师列表</CardTitle>
              
              {/* Search and Filter Bar */}
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-[300px] max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="搜索教师姓名或工号..."
                      className="pl-10"
                      value={searchParams.name || ''}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>
                </div>
                
                <Select 
                  value={searchParams.employment_status || 'all'}
                  onValueChange={(value) => handleFilterChange('employment_status', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {EMPLOYMENT_STATUS_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select 
                  value={searchParams.is_active === undefined ? 'all' : searchParams.is_active.toString()}
                  onValueChange={(value) => handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="true">启用</SelectItem>
                    <SelectItem value="false">禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            
            <CardContent>
              <TeacherTable
                teachers={teachers}
                loading={loading}
                onEdit={handleEditTeacher}
                onDelete={handleDeleteTeacher}
                onViewDetail={handleViewDetail}
                onToggleStatus={handleToggleStatus}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: handlePageChange,
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">教师总数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">全校教师总人数</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">在职教师</CardTitle>
                <Users className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.active}</div>
                <p className="text-xs text-muted-foreground">
                  占比 {stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">其他状态</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.inactive}</div>
                <p className="text-xs text-muted-foreground">离职、退休等状态</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>在职状态分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats.byStatus).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{status}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ 
                            width: stats.total > 0 ? `${(count / stats.total) * 100}%` : '0%' 
                          }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Teacher Form Modal */}
      <TeacherForm
        teacher={editingTeacher}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />

      {/* Teacher Detail Modal */}
      <TeacherDetail
        teacher={teacherDetail}
        open={detailOpen}
        onClose={() => setDetailOpen(false)}
        loading={detailLoading}
      />
    </div>
  );
};

export default TeacherManagementPage;