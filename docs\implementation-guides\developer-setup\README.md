# Developer Environment Setup Guide

This guide provides comprehensive setup instructions for the Deep-Mate development environment.

## Prerequisites

### System Requirements
- **Operating System**: macOS, Linux, or Windows with WSL2
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: At least 10GB free space
- **Network**: Stable internet connection for package downloads

### Required Software

#### Backend Development (Rust)
```bash
# Install Rust via rustup
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version

# Install SQLx CLI for database migrations
cargo install sqlx-cli --no-default-features --features postgres
```

#### Frontend Development (Node.js)
```bash
# Install Node.js (version 18 or higher)
# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Verify installation
node --version
npm --version
```

#### Database (PostgreSQL)
```bash
# macOS with Homebrew
brew install postgresql@18
brew services start postgresql@18

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-18 postgresql-contrib-18

# Create development database
createdb deep_mate_dev
```

## Project Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd deep-mate
```

### 2. Backend Setup
```bash
cd backend

# Copy environment template
cp .env.example .env

# Edit .env with your database configuration
# DATABASE_URL=postgresql://username:password@localhost/deep_mate_dev
# JWT_SECRET=your-jwt-secret-key

# Install dependencies and run migrations
cargo build
sqlx migrate run

# Run backend server
cargo run
```

### 3. Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your API configuration
# VITE_API_BASE_URL=http://localhost:3000

# Start development server
npm run dev
```

### 4. Verify Setup
- Backend should be running on `http://localhost:3000`
- Frontend should be running on `http://localhost:5173`
- Database should be accessible and migrations applied

## Development Workflow

### Daily Development
1. **Start Services**
   ```bash
   # Terminal 1: Backend
   cd backend && cargo run
   
   # Terminal 2: Frontend
   cd frontend && npm run dev
   
   # Terminal 3: Database (if not running as service)
   postgres -D /usr/local/var/postgres
   ```

2. **Code Changes**
   - Backend changes trigger automatic recompilation
   - Frontend changes trigger hot reload
   - Database changes require migration files

3. **Testing**
   ```bash
   # Backend tests
   cd backend && cargo test
   
   # Frontend tests
   cd frontend && npm test
   ```

## IDE Configuration

### VS Code (Recommended)
Install these extensions:
- `rust-analyzer` - Rust language support
- `ES7+ React/Redux/React-Native snippets` - React snippets
- `Prettier - Code formatter` - Code formatting
- `ESLint` - JavaScript linting
- `PostgreSQL` - Database management

### Settings
Create `.vscode/settings.json`:
```json
{
  "rust-analyzer.checkOnSave.command": "clippy",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## Environment Variables

### Backend (.env)
```env
# Database
DATABASE_URL=postgresql://username:password@localhost/deep_mate_dev

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRATION=24h

# Server Configuration
HOST=127.0.0.1
PORT=3000

# External Services
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-provider.com

# File Storage
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=deep-mate-dev
```

### Frontend (.env)
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_AI_GRADING=true
VITE_ENABLE_CROSS_TENANT=true
```

## Common Issues and Solutions

### Rust Compilation Issues
```bash
# Clear cargo cache
cargo clean

# Update Rust toolchain
rustup update

# Rebuild dependencies
cargo build --release
```

### Node.js Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Database Connection Issues
```bash
# Check PostgreSQL status
brew services list | grep postgresql

# Restart PostgreSQL
brew services restart postgresql@18

# Test connection
psql -d deep_mate_dev -c "SELECT version();"
```

## Next Steps

After completing the basic setup:

1. Review [Task-Specific Setup Guides](./task-specific/) for your assigned task
2. Read [Testing Strategies](../testing/README.md)
3. Familiarize yourself with [Code Review Standards](../quality/standards.md)
4. Set up your development workflow with [Project Management Tools](../project-management/task-management.md)

## Support

If you encounter issues not covered here:
1. Check the [Troubleshooting Guide](../troubleshooting/README.md)
2. Review task-specific documentation
3. Consult the team lead or senior developer