-- Admin System Setup Migration
-- Migration: 20250104_admin_system_setup.migrations_temp

-- =============================================
-- ADMIN SYSTEM TABLES
-- =============================================

-- Admin roles definition
CREATE TABLE IF NOT EXISTS public.admin_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]',
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin user assignments
CREATE TABLE IF NOT EXISTS public.admin_user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES public.admin_roles(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES public.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_id)
);

-- System audit logs
CREATE TABLE IF NOT EXISTS public.system_audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id),
    tenant_id UUID REFERENCES public.tenants(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id UUID,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System maintenance logs
CREATE TABLE IF NOT EXISTS public.system_maintenance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    maintenance_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    started_by UUID REFERENCES public.users(id),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed', 'cancelled')),
    result_summary TEXT,
    affected_tenants JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for admin system
CREATE INDEX IF NOT EXISTS idx_admin_roles_name ON public.admin_roles(name);
CREATE INDEX IF NOT EXISTS idx_admin_user_roles_user ON public.admin_user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_user_roles_role ON public.admin_user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_admin_user_roles_active ON public.admin_user_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_system_audit_logs_user ON public.system_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_audit_logs_tenant ON public.system_audit_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_system_audit_logs_action ON public.system_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_system_audit_logs_created ON public.system_audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_system_maintenance_logs_type ON public.system_maintenance_logs(maintenance_type);
CREATE INDEX IF NOT EXISTS idx_system_maintenance_logs_status ON public.system_maintenance_logs(status);
CREATE INDEX IF NOT EXISTS idx_system_maintenance_logs_created ON public.system_maintenance_logs(created_at);

-- Insert default admin roles
INSERT INTO public.admin_roles (name, description, permissions, is_system_role) VALUES
('super_admin', 'Super Administrator with full system access', 
 '["system:*", "tenant:*", "user:*", "admin:*"]'::jsonb, true),
('tenant_admin', 'Tenant Administrator with tenant management access', 
 '["tenant:create", "tenant:read", "tenant:update", "tenant:delete", "user:read", "user:update"]'::jsonb, true),
('system_operator', 'System Operator with maintenance access', 
 '["system:read", "system:maintenance", "tenant:read", "user:read"]'::jsonb, true),
('auditor', 'System Auditor with read-only access', 
 '["system:read", "tenant:read", "user:read", "audit:read"]'::jsonb, true)
ON CONFLICT (name) DO NOTHING;

-- Note: Default admin user and role assignment will be created in a separate migration

-- Insert default system configuration
INSERT INTO public.system_config (config_key, config_value, description, is_public) VALUES
('system.name', '"Deep-Mate"', 'System name', true),
('system.version', '"1.0.0"', 'System version', true),
('system.maintenance_mode', 'false', 'System maintenance mode flag', false),
('auth.session_timeout_hours', '24', 'Session timeout in hours', false),
('auth.max_failed_attempts', '5', 'Maximum failed login attempts', false),
('notification.email_enabled', 'false', 'Email notification enabled', false),
('notification.sms_enabled', 'false', 'SMS notification enabled', false)
ON CONFLICT (config_key) DO NOTHING;

-- Insert default notification templates
INSERT INTO public.notification_templates (name, title, content, notification_type, variables) VALUES
('user_welcome', '欢迎使用Deep-Mate系统', 
 '欢迎您，{{user_name}}！您已成功注册Deep-Mate系统。', 
 'welcome', '["user_name"]'::jsonb),
('password_reset', '密码重置通知', 
 '您好，{{user_name}}！您的密码重置验证码是：{{verification_code}}，有效期5分钟。', 
 'password_reset', '["user_name", "verification_code"]'::jsonb),
('system_maintenance', '系统维护通知', 
 '系统将于{{maintenance_time}}进行维护，预计持续{{duration}}小时。', 
 'maintenance', '["maintenance_time", "duration"]'::jsonb)
ON CONFLICT (name) DO NOTHING;
