import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Filter, Download, Upload, Settings } from 'lucide-react';
import { toast } from 'sonner';

import GradeTable from './components/GradeTable';
import GradeForm from './components/GradeForm';
import GradeStatistics from './components/GradeStatistics';
import { gradeApi } from '@/services/gradeApi';
import { 
  GradeLevel, 
  GradeLevelQueryParams, 
  GradeLevelFormData,
  DEFAULT_GRADE_QUERY,
  GRADE_SORT_OPTIONS,
  GRADE_STATUS_OPTIONS
} from '@/types/grade';

const GradeManagementPage: React.FC = () => {
  // State management
  const [grades, setGrades] = useState<GradeLevel[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<GradeLevelQueryParams>(DEFAULT_GRADE_QUERY);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  });
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingGrade, setEditingGrade] = useState<GradeLevel | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // Load grades data
  const loadGrades = async (params?: Partial<GradeLevelQueryParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...queryParams, ...params };
      const response = await gradeApi.getGrades(finalParams);
      
      if (response.success && response.data) {
        setGrades(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.page_size,
          total: response.data.total,
          totalPages: response.data.total_pages,
        });
        setQueryParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load grades:', error);
      toast.error('加载年级列表失败');
    } finally {
      setLoading(false);
    }
  };

  // Initialize data
  useEffect(() => {
    loadGrades();
  }, []);

  // Handle search
  const handleSearch = (search: string) => {
    loadGrades({ ...queryParams, search, page: 1 });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof GradeLevelQueryParams, value: any) => {
    const newParams = { ...queryParams, [key]: value, page: 1 };
    loadGrades(newParams);
  };

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    loadGrades({ ...queryParams, page, page_size: pageSize });
  };

  // Handle create grade
  const handleCreateGrade = () => {
    setEditingGrade(undefined);
    setFormOpen(true);
  };

  // Handle edit grade
  const handleEditGrade = (grade: GradeLevel) => {
    setEditingGrade(grade);
    setFormOpen(true);
  };

  // Handle form submit
  const handleFormSubmit = async (data: GradeLevelFormData) => {
    try {
      setFormLoading(true);
      
      if (editingGrade) {
        // Update grade
        const response = await gradeApi.updateGrade(editingGrade.id, data);
        if (response.success) {
          toast.success('年级更新成功');
          setFormOpen(false);
          loadGrades();
        }
      } else {
        // Create grade
        const response = await gradeApi.createGrade(data);
        if (response.success) {
          toast.success('年级创建成功');
          setFormOpen(false);
          loadGrades();
        }
      }
    } catch (error: any) {
      console.error('Failed to save grade:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete grade
  const handleDeleteGrade = async (id: string) => {
    if (!confirm('确定要删除这个年级吗？此操作不可恢复。')) {
      return;
    }

    try {
      const response = await gradeApi.deleteGrade(id);
      if (response.success) {
        toast.success('年级删除成功');
        loadGrades();
      }
    } catch (error: any) {
      console.error('Failed to delete grade:', error);
      toast.error(error.response?.data?.message || '删除失败');
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await gradeApi.toggleGradeStatus(id, isActive);
      if (response.success) {
        toast.success(`年级已${isActive ? '启用' : '禁用'}`);
        loadGrades();
      }
    } catch (error: any) {
      console.error('Failed to toggle grade status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await gradeApi.exportGrades(queryParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `grades-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export grades:', error);
      toast.error('导出失败');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">年级管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的年级信息，包括年级的创建、编辑和状态管理</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleCreateGrade}>
            <Plus className="w-4 h-4 mr-2" />
            新增年级
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">年级列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>年级列表</CardTitle>
              
              {/* Search and Filter Bar */}
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-[300px] max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="搜索年级名称或代码..."
                      className="pl-10"
                      value={queryParams.search || ''}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>
                </div>
                
                <Select 
                  value={queryParams.is_active?.toString() || 'all'}
                  onValueChange={(value) => handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {GRADE_STATUS_OPTIONS.map(option => (
                      <SelectItem key={option.value.toString()} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select 
                  value={`${queryParams.order_by}-${queryParams.order_direction}`}
                  onValueChange={(value) => {
                    const [orderBy, orderDirection] = value.split('-');
                    handleFilterChange('order_by', orderBy);
                    handleFilterChange('order_direction', orderDirection);
                  }}
                >
                  <SelectTrigger className="w-36">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {GRADE_SORT_OPTIONS.map(option => (
                      <React.Fragment key={option.value}>
                        <SelectItem value={`${option.value}-asc`}>
                          {option.label} ↑
                        </SelectItem>
                        <SelectItem value={`${option.value}-desc`}>
                          {option.label} ↓
                        </SelectItem>
                      </React.Fragment>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            
            <CardContent>
              <GradeTable
                grades={grades}
                loading={loading}
                onEdit={handleEditGrade}
                onDelete={handleDeleteGrade}
                onToggleStatus={handleToggleStatus}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: handlePageChange,
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <GradeStatistics />
        </TabsContent>
      </Tabs>

      {/* Grade Form Modal */}
      <GradeForm
        grade={editingGrade}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />
    </div>
  );
};

export default GradeManagementPage;