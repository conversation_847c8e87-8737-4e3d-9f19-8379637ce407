export interface AdministrativeClassesStatistics {
  total_classes: Number,
  total_teacher: Number,
  total_students: Number,
}

export interface AdministrativeClasses {
  id: String,
  class_name: String,
  class_code: String,
  academic_year: Number,
  grade_level_code: String,
  teacher_id: String,
  created_at: String,
  updated_at: String,
}

export interface CreateAdministrativeClassesParams {
  class_name: String,
  class_code: String,
  academic_year: Number,
  grade_level_code: String,
  teacher_id: String,
}
export interface AdministrativeClassesDetail {
  id: String,
  class_name: String,
  class_code: String,
  academic_year: Number,
  grade_level_code: String,
  teacher_id: String,
  created_at: String,
  updated_at: String,
  //额外信息
  teacher_name: String,
  grade_level_name: String,
  total_student: Number,
}