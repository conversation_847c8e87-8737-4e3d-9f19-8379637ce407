# 6. 技术架构

## 6.1 系统架构
- **前端架构**：React + TypeScript + shadcn/ui
- **后端架构**：Rust + Axum
- **数据库**：PostgreSQL18(主库) + Redis(缓存)
- **文件存储**：MinIO分布式对象存储
- **消息队列**：Redis Streams
- **搜索引擎**：Tantivy
- **权限模型**：Casbin-RS (RBAC+ABAC)

## 6.2 部署架构
- **部署策略**：优先采用 WebAssembly (WASM)
- **容器化部署**：Docker + Kubernetes
- **服务治理**：Istio Service Mesh
- **存储集群**：MinIO分布式集群部署
- **监控体系**：Prometheus + Grafana
- **日志收集**：ELK Stack

## 6.3 数据模型

### 6.3.1 实体关系图 (ER Diagram)
```mermaid
erDiagram
    %% =====================================
    %% Public Schema
    %% =====================================
    tenants {
        bigserial id PK
        varchar(50) code UK
        varchar(100) name
        varchar(50) schema_name
    }

    users {
        bigserial id PK
        varchar(20) phone_number UK
        varchar(50) username UK
        varchar(255) password_hash
        varchar(20) user_type
    }

    user_tenant_links {
        bigserial id PK
        int user_id FK
        int tenant_id FK
    }

    roles {
        bigserial id PK
        varchar(50) name
    }

    parent_student_relations {
        bigserial id PK
        int parent_user_id FK
        int tenant_id FK
        varchar(50) student_id
    }

    question_bank {
        bigserial id PK
        text question_content
        varchar(50) subject
        int grade_level
    }

    question_answers {
        bigserial id PK
        int question_id FK
        varchar(30) answer_type
        text answer_content
    }

    question_explanations {
        bigserial id PK
        int question_id FK
        varchar(30) explanation_type
        text explanation_content
    }

    question_difficulties {
        bigserial id PK
        int question_id FK
        varchar(30) difficulty_type
        varchar(20) difficulty_level
    }

    question_knowledge_points {
        bigserial id PK
        int question_id FK
        int knowledge_point_id FK
        varchar(30) relation_type
    }

    exam_papers {
        bigserial id PK
        varchar(200) title
        varchar(50) subject
    }

    exam_paper_questions {
        bigserial id PK
        int exam_paper_id FK
        int question_id FK
        varchar(20) question_number
    }

    textbooks {
        bigserial id PK
        varchar(200) title
    }

    textbook_tenant_access {
        bigserial id PK
        int textbook_id FK
        int tenant_id FK
    }

    textbook_mixed_contents {
        bigserial id PK
        int textbook_id FK
        int chapter_id
        int module_id
        varchar(30) module_type
        varchar(30) content_type
        int sequence_number
        varchar(200) title
        jsonb content_data
        int question_ref FK
        varchar(20) difficulty_level
        boolean is_optional
        text learning_notes
    }

    content_reading_orders {
        bigserial id PK
        int textbook_id FK
        int chapter_id
        int module_id
        int content_id FK
        int reading_sequence
        varchar(30) target_audience
        text skip_conditions
    }

    %% =====================================
    %% Tenant Schema (Example: tenant_001)
    %% =====================================
    "tenant_001.students" {
        bigserial id PK
        varchar(50) student_id UK
        varchar(100) name
        int administrative_class_id FK
        int user_id FK
    }

    "tenant_001.teachers" {
        bigserial id PK
        varchar(50) teacher_id UK
        varchar(100) name
        varchar(100) title
        date hire_date
        int user_id FK
    }

    "tenant_001.classes" {
        bigserial id PK
        varchar(100) name
        varchar(20) type
    }

    "tenant_001.exams" {
        bigserial id PK
        varchar(100) name
        varchar(20) type
    }

    "tenant_001.user_identities" {
        bigserial id PK
        int user_id FK
        int role_id FK
        varchar(30) target_type
        int target_id
    }

    "tenant_001.paper_scans" {
        bigserial id PK
        int exam_id FK
        int student_id FK
        text front_page_url
    }

    "tenant_001.grading_records" {
        bigserial id PK
        int exam_id FK
        int student_id FK
        int question_id
        int grader_user_id FK
        decimal final_score
    }

    "tenant_001.academic_statistics" {
        bigserial id PK
        int exam_id FK
        int student_id FK
        decimal total_score
        int class_rank
    }

    "tenant_001.answer_card_blocks" {
        bigserial id PK
        int exam_id FK
        varchar(100) block_name
    }

    "tenant_001.card_block_question_links" {
        bigserial id PK
        int card_block_id FK
        int question_id
    }


    %% =====================================
    %% Relationships
    %% =====================================
    users                               ||--o{ user_tenant_links : "has_access_to"
    tenants                             ||--o{ user_tenant_links : "is_accessed_by"
    users                               ||--o{ "tenant_001.user_identities" : "has"
    roles                               ||--o{ "tenant_001.user_identities" : "defines"
    users                               ||--o{ parent_student_relations : "is_parent"
    tenants                             ||--o{ parent_student_relations : "has_student_in"
    users                               o|--o{ "tenant_001.students" : "is_student"
    users                               o|--o{ "tenant_001.teachers" : "is_teacher"
    "tenant_001.classes"                ||--o{ "tenant_001.students" : "has"
    tenants                             ||--o{ "tenant_001.exams" : "hosts"
    "tenant_001.exams"                  ||--o{ "tenant_001.paper_scans" : "has"
    "tenant_001.students"               ||--o{ "tenant_001.paper_scans" : "submits"
    "tenant_001.exams"                  ||--o{ "tenant_001.grading_records" : "has"
    "tenant_001.students"               ||--o{ "tenant_001.grading_records" : "has"
    users                               o|--o{ "tenant_001.grading_records" : "graded_by"
    "tenant_001.exams"                  ||--o{ "tenant_001.academic_statistics" : "generates"
    "tenant_001.students"               ||--o{ "tenant_001.academic_statistics" : "has"
    exam_papers                         ||--|{ exam_paper_questions : "contains"
    question_bank                       ||--o{ exam_paper_questions : "is_part_of"
    textbooks                           ||--o{ textbook_tenant_access : "can_be_accessed_by"
    tenants                             ||--o{ textbook_tenant_access : "can_access"
    question_bank                       ||--o{ question_answers : "has_multiple"
    question_bank                       ||--o{ question_explanations : "has_multiple"
    question_bank                       ||--o{ question_difficulties : "has_multiple"
    question_bank                       ||--o{ question_knowledge_points : "relates_to"
    "tenant_001.exams"                  ||--o{ "tenant_001.answer_card_blocks" : "defines"
    "tenant_001.answer_card_blocks"     ||--o{ "tenant_001.card_block_question_links" : "links_to"
    textbooks                           ||--o{ textbook_mixed_contents : "contains"
    question_bank                       ||--o{ textbook_mixed_contents : "referenced_by"
    textbook_mixed_contents             ||--o{ content_reading_orders : "ordered_by"
```

### 6.3.2 数据表结构
```sql
-- =====================================
-- Public Schema（全局共享表）
-- =====================================

-- 租户表
CREATE TABLE public.tenants (
  id BIGSERIAL PRIMARY KEY, -- 租户ID
  code VARCHAR(50) UNIQUE NOT NULL, -- 租户唯一标识码
  name VARCHAR(100) NOT NULL, -- 租户名称
  schema_name VARCHAR(50) NOT NULL, -- 对应的数据库Schema名称
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 角色表
CREATE TABLE public.roles (
  id BIGSERIAL PRIMARY KEY, -- 角色ID
  name VARCHAR(50) NOT NULL, -- 角色名称
  description TEXT, -- 角色描述
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 用户表（所有用户：系统、租户、家长）
CREATE TABLE public.users (
  id BIGSERIAL PRIMARY KEY, -- 用户ID
  phone_number VARCHAR(20) UNIQUE, -- 手机号，可作为主要登录方式
  username VARCHAR(50) UNIQUE, -- 用户名，可作为备用登录方式
  email VARCHAR(100) UNIQUE, -- 邮箱
  password_hash VARCHAR(255) NOT NULL, -- 密码哈希
  user_type VARCHAR(20) CHECK (user_type IN ('system', 'tenant', 'parent', 'unverified')) DEFAULT 'unverified', -- 用户类型, 'unverified' 表示仅注册但未绑定任何身份
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 用户-租户关联表 (中央映射)
CREATE TABLE public.user_tenant_links (
  id BIGSERIAL PRIMARY KEY, -- 关联ID
  user_id INTEGER REFERENCES public.users(id) NOT NULL, -- 用户ID
  tenant_id INTEGER REFERENCES public.tenants(id) NOT NULL, -- 租户ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  UNIQUE(user_id, tenant_id) -- 唯一性约束
);

-- 家长学生关联表（支持跨租户）
CREATE TABLE public.parent_student_relations (
  id BIGSERIAL PRIMARY KEY, -- 关联ID
  parent_user_id INTEGER REFERENCES public.users(id), -- 家长用户ID
  tenant_id INTEGER REFERENCES public.tenants(id), -- 学生所属租户ID
  student_id VARCHAR(50) NOT NULL, -- 租户内学生ID
  relation_type VARCHAR(20) CHECK (relation_type IN ('father', 'mother', 'guardian')), -- 关系类型
  is_verified BOOLEAN DEFAULT false, -- 是否已验证
  verified_by INTEGER REFERENCES public.users(id), -- 验证人ID
  verified_at TIMESTAMP, -- 验证时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  UNIQUE(parent_user_id, tenant_id, student_id) -- 唯一性约束
);

-- 用户身份切换记录表（Public Schema - 支持跨租户身份切换）
CREATE TABLE public.user_identity_switches (
  id BIGSERIAL PRIMARY KEY, -- 切换记录ID
  user_id INTEGER REFERENCES public.users(id) NOT NULL, -- 用户ID
  from_tenant_id INTEGER REFERENCES public.tenants(id), -- 切换前租户ID
  to_tenant_id INTEGER REFERENCES public.tenants(id), -- 切换后租户ID
  from_identity_info JSONB, -- 切换前身份信息（包含租户内身份ID、角色等）
  to_identity_info JSONB, -- 切换后身份信息（包含租户内身份ID、角色等）
  switch_type VARCHAR(30) CHECK (switch_type IN ('tenant_switch', 'role_switch', 'student_switch')), -- 切换类型
  switch_reason VARCHAR(100), -- 切换原因
  session_id VARCHAR(100), -- 会话ID
  ip_address INET, -- IP地址
  user_agent TEXT, -- 用户代理
  switched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 切换时间
);

-- 公共教辅表
CREATE TABLE public.textbooks (
  id BIGSERIAL PRIMARY KEY, -- 教辅ID
  title VARCHAR(200) NOT NULL, -- 标题
  subject_id INTEGER REFERENCES public.subjects(id), -- 学科ID
  grade_level_id INTEGER REFERENCES public.grade_levels(id), -- 年级ID
  publisher VARCHAR(100), -- 出版社
  publication_year INTEGER, -- 出版年份
  isbn VARCHAR(20), -- ISBN号
  version VARCHAR(50), -- 版本
  status VARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft', -- 状态
  creator_id INTEGER REFERENCES public.users(id), -- 创建人ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 公共教辅章节表
CREATE TABLE public.textbook_chapters (
  id BIGSERIAL PRIMARY KEY, -- 章节ID
  textbook_id INTEGER REFERENCES public.textbooks(id), -- 教辅ID
  chapter_number INTEGER NOT NULL, -- 章节号
  title VARCHAR(200) NOT NULL, -- 标题
  content TEXT, -- 内容
  knowledge_points JSONB, -- 知识点
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 公共教辅练习题表
CREATE TABLE public.textbook_exercises (
  id BIGSERIAL PRIMARY KEY, -- 练习题ID
  textbook_id INTEGER REFERENCES public.textbooks(id), -- 教辅ID
  chapter_id INTEGER REFERENCES public.textbook_chapters(id), -- 章节ID
  question_content TEXT NOT NULL, -- 题目内容
  answer_content TEXT, -- 答案内容
  difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5), -- 难度等级
  knowledge_points JSONB, -- 知识点
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 教辅租户授权访问表
CREATE TABLE public.textbook_tenant_access (
  id BIGSERIAL PRIMARY KEY,
  textbook_id INTEGER REFERENCES public.textbooks(id),
  tenant_id INTEGER REFERENCES public.tenants(id),
  granted_by INTEGER REFERENCES public.users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(textbook_id, tenant_id)
);

-- 公共题库表
CREATE TABLE public.question_bank (
  id BIGSERIAL PRIMARY KEY, -- 题目ID
  source_type VARCHAR(30) CHECK (source_type IN ('manual', 'textbook_exercise')), -- 来源类型
  source_id BIGINT, -- 来源ID (如 textbook_exercises.id)
  question_type VARCHAR(20) CHECK (question_type IN ('choice', 'blank', 'essay', 'calculation')), -- 题型
  question_content TEXT NOT NULL, -- 题目内容
  subject VARCHAR(50) NOT NULL, -- 学科
  grade_level INTEGER NOT NULL, -- 适用年级
  creator_id INTEGER REFERENCES public.users(id), -- 创建人
  version INTEGER DEFAULT 1, -- 版本号
  status VARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 题目答案表（支持多答案）
CREATE TABLE public.question_answers (
  id BIGSERIAL PRIMARY KEY,
  question_id INTEGER REFERENCES public.question_bank(id) ON DELETE CASCADE,
  answer_type VARCHAR(30) NOT NULL, -- standard, reference, scoring_criteria
  answer_content TEXT NOT NULL,
  answer_format VARCHAR(20) DEFAULT 'text', -- text, formula, image, code
  is_primary BOOLEAN DEFAULT FALSE,
  score_weight DECIMAL(3,2) DEFAULT 1.0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 题目解析表（支持多层次解析）
CREATE TABLE public.question_explanations (
  id BIGSERIAL PRIMARY KEY,
  question_id INTEGER REFERENCES public.question_bank(id) ON DELETE CASCADE,
  explanation_type VARCHAR(30) NOT NULL, -- basic, advanced, approach, common_errors, extension
  explanation_content TEXT NOT NULL,
  target_audience VARCHAR(30), -- beginner, intermediate, advanced
  display_order INTEGER DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 题目难度表（支持多维度难度）
CREATE TABLE public.question_difficulties (
  id BIGSERIAL PRIMARY KEY,
  question_id INTEGER REFERENCES public.question_bank(id) ON DELETE CASCADE,
  difficulty_type VARCHAR(30) NOT NULL, -- cognitive, computational, time_consumption
  difficulty_level VARCHAR(20) NOT NULL,
  difficulty_value DECIMAL(3,2), -- 数值化难度系数
  assessment_basis TEXT, -- 难度评定依据
  assessed_by INTEGER REFERENCES public.users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 题目知识点关联表（支持多知识点）
CREATE TABLE public.question_knowledge_points (
  id BIGSERIAL PRIMARY KEY,
  question_id INTEGER REFERENCES public.question_bank(id) ON DELETE CASCADE,
  knowledge_point_id INTEGER REFERENCES public.knowledge_points(id),
  relation_type VARCHAR(30) NOT NULL, -- primary, secondary, prerequisite
  weight DECIMAL(3,2) DEFAULT 1.0, -- 知识点在题目中的权重
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(question_id, knowledge_point_id, relation_type)
);

-- =============================================
-- 混合内容组织系统（公共Schema内）
-- =============================================

-- 教辅混合内容表（增强版，支持个性化）
CREATE TABLE public.textbook_mixed_contents (
  id BIGSERIAL PRIMARY KEY, -- 混合内容ID
  textbook_id INTEGER REFERENCES public.textbooks(id) ON DELETE CASCADE, -- 教辅ID
  chapter_id INTEGER NOT NULL, -- 章节ID
  module_id INTEGER NOT NULL, -- 模块ID
  module_type VARCHAR(30) NOT NULL CHECK (module_type IN ('knowledge_guide', 'knowledge_exploration', 'consolidation_training')), -- 模块类型
  content_type VARCHAR(30) NOT NULL CHECK (content_type IN ('knowledge_explanation', 'example_question', 'practice_question')), -- 内容类型
  sequence_number INTEGER NOT NULL, -- 在模块中的序号
  title VARCHAR(200), -- 内容标题
  content_data JSONB, -- 内容数据（知识讲解的富文本内容）
  question_ref INTEGER REFERENCES public.question_bank(id), -- 题目引用（例题和试题使用）
  display_format VARCHAR(30) DEFAULT 'rich_text', -- 显示格式
  is_interactive BOOLEAN DEFAULT FALSE, -- 是否可交互
  solution_display BOOLEAN DEFAULT FALSE, -- 是否显示解答过程（例题使用）
  -- 个性化支持字段
  difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')), -- 难度级别
  is_optional BOOLEAN DEFAULT FALSE, -- 是否为可选内容
  learning_notes TEXT, -- 学习注释和建议
  prerequisites TEXT, -- 前置知识要求
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  UNIQUE(textbook_id, chapter_id, module_id, sequence_number) -- 唯一性约束
);

-- 内容阅读顺序控制表（增强版，支持个性化）
CREATE TABLE public.content_reading_orders (
  id BIGSERIAL PRIMARY KEY, -- 阅读顺序ID
  textbook_id INTEGER REFERENCES public.textbooks(id) ON DELETE CASCADE, -- 教辅ID
  chapter_id INTEGER NOT NULL, -- 章节ID
  module_id INTEGER NOT NULL, -- 模块ID
  content_id INTEGER REFERENCES public.textbook_mixed_contents(id) ON DELETE CASCADE, -- 内容ID
  reading_sequence INTEGER NOT NULL, -- 阅读序列
  dependency_level INTEGER DEFAULT 1, -- 依赖级别
  is_prerequisite BOOLEAN DEFAULT FALSE, -- 是否为前置条件
  learning_objective TEXT, -- 学习目标
  estimated_time_minutes INTEGER, -- 预计用时（分钟）
  -- 个性化支持字段
  target_audience VARCHAR(30) CHECK (target_audience IN ('beginner', 'intermediate', 'advanced', 'all')), -- 目标受众
  skip_conditions TEXT, -- 跳过条件说明
  importance_level INTEGER CHECK (importance_level BETWEEN 1 AND 5) DEFAULT 3, -- 重要程度（1-5）
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  UNIQUE(textbook_id, chapter_id, module_id, reading_sequence) -- 唯一性约束
);

-- 公共试卷模板表
CREATE TABLE public.exam_papers (
  id BIGSERIAL PRIMARY KEY, -- 试卷模板ID
  title VARCHAR(200) NOT NULL, -- 模板标题
  subject VARCHAR(50) NOT NULL, -- 学科
  grade_level INTEGER NOT NULL, -- 适用年级
  total_score DECIMAL(5,2), -- 总分
  description TEXT, -- 描述
  structure JSONB, -- 试卷结构 (e.g., sections, instructions)
  creator_id INTEGER REFERENCES public.users(id), -- 创建人
  version INTEGER DEFAULT 1, -- 版本号
  status VARCHAR(20) CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 试卷-题目关联表
CREATE TABLE public.exam_paper_questions (
  id BIGSERIAL PRIMARY KEY,
  exam_paper_id INTEGER REFERENCES public.exam_papers(id),
  question_id INTEGER REFERENCES public.question_bank(id),
  question_number VARCHAR(20), -- 题目在试卷中的编号
  score DECIMAL(5,2), -- 分值
  section_name VARCHAR(100), -- 所属大题
  display_order INTEGER,
  UNIQUE(exam_paper_id, question_id),
  UNIQUE(exam_paper_id, question_number)
);

-- =====================================
-- 租户Schema（业务数据隔离）
-- 由系统管理员或运维人员添加学校机构时动态创建
-- =====================================

-- 学科组表（每个租户Schema内）
CREATE TABLE tenant_001.subject_groups (
  id BIGSERIAL PRIMARY KEY, -- 学科组ID
  name VARCHAR(100) NOT NULL, -- 学科组名称
  subject VARCHAR(50) NOT NULL, -- 所属学科
  description TEXT, -- 描述
  leader_user_id INTEGER REFERENCES public.users(id), -- 组长用户ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 班级表（每个租户Schema内）
CREATE TABLE tenant_001.classes (
  id BIGSERIAL PRIMARY KEY, -- 班级ID
  name VARCHAR(100) NOT NULL, -- 班级名称
  type VARCHAR(20) CHECK (type IN ('administrative', 'teaching')), -- 班级类型：行政班、教学班
  grade_level_id INTEGER REFERENCES public.grade_levels(id), -- 所属年级ID
  subject_group_id INTEGER REFERENCES tenant_001.subject_groups(id), -- 教学班关联的学科组ID
  capacity INTEGER DEFAULT 50, -- 班级容量
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 考试表（每个租户Schema内）
CREATE TABLE tenant_001.exams (
  id BIGSERIAL PRIMARY KEY, -- 考试ID
  name VARCHAR(100) NOT NULL, -- 考试名称
  type VARCHAR(20) CHECK (type IN ('single', 'joint')), -- 考试类型：单校、联考
  start_time TIMESTAMP NOT NULL, -- 开始时间
  end_time TIMESTAMP NOT NULL, -- 结束时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 学生表（每个租户Schema内）
CREATE TABLE tenant_001.students (
  id BIGSERIAL PRIMARY KEY, -- 学生ID
  student_id VARCHAR(50) UNIQUE NOT NULL, -- 学号（租户内唯一）
  name VARCHAR(100) NOT NULL, -- 学生姓名
  administrative_class_id INTEGER REFERENCES tenant_001.classes(id), -- 所属行政班ID
  grade_level_id INTEGER REFERENCES public.grade_levels(id), -- 所属年级ID
  profile_level VARCHAR(20) CHECK (profile_level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')), -- 综合能力等级
  profile_tags JSONB, -- 学生标签
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 老师表（每个租户Schema内）
CREATE TABLE tenant_001.teachers (
  id BIGSERIAL PRIMARY KEY, -- 老师ID
  teacher_id VARCHAR(50) UNIQUE NOT NULL, -- 教师工号（租户内唯一）
  user_id INTEGER REFERENCES public.users(id) NOT NULL, -- 关联的全局用户ID
  name VARCHAR(100) NOT NULL, -- 教师姓名
  title VARCHAR(100), -- 职称
  hire_date DATE, -- 入职日期
  department VARCHAR(100), -- 所属部门
  is_active BOOLEAN DEFAULT TRUE, -- 是否在职
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 学生分级标签表（租户Schema内）
CREATE TABLE tenant_001.student_profile_levels (
  id BIGSERIAL PRIMARY KEY, -- 分级ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  subject VARCHAR(50) NOT NULL, -- 学科
  level VARCHAR(20) CHECK (level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')), -- 等级
  level_description TEXT, -- 等级描述
  assessment_date DATE NOT NULL, -- 评估日期
  assessed_by INTEGER REFERENCES public.users(id), -- 评估人ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  UNIQUE(student_id, subject, assessment_date) -- 唯一性约束
);

-- 学生标签表（租户Schema内）
CREATE TABLE tenant_001.student_profile_tags (
  id BIGSERIAL PRIMARY KEY, -- 标签ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  tag_name VARCHAR(50) NOT NULL, -- 标签名称
  tag_value VARCHAR(100), -- 标签值
  tag_category VARCHAR(30) CHECK (tag_category IN ('academic', 'behavior', 'interest', 'ability', 'other')), -- 标签分类
  created_by INTEGER REFERENCES public.users(id), -- 创建人ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  UNIQUE(student_id, tag_name) -- 唯一性约束
);

-- 学生教学班关联表（每个租户Schema内）
CREATE TABLE tenant_001.student_teaching_classes (
  id BIGSERIAL PRIMARY KEY, -- 关联ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  class_id INTEGER REFERENCES tenant_001.classes(id), -- 教学班ID
  subject VARCHAR(50) NOT NULL, -- 学科
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 用户身份表（每个租户Schema内）
CREATE TABLE tenant_001.user_identities (
  id BIGSERIAL PRIMARY KEY, -- 身份ID
  user_id INTEGER REFERENCES public.users(id) NOT NULL, -- 用户ID
  role_id INTEGER REFERENCES public.roles(id) NOT NULL, -- 角色ID
  -- 关联目标，用于确定身份的作用范围，如：校长角色作用于全校，年级长作用于某年级
  target_type VARCHAR(30) CHECK (target_type IN ('school', 'subject_group', 'grade', 'class', 'student')), -- 目标类型
  target_id UUID, -- 例如：class_id, grade_id, subject_group_id。对于校级角色可为NULL
  subject VARCHAR(50), -- 适用于特定学科的角色，如“数学老师”
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  UNIQUE(user_id, role_id, target_id, subject) -- 确保用户在同一范围内不会重复拥有完全相同的身份
);

-- 用户权限分配表（租户Schema内）
CREATE TABLE tenant_001.user_permissions (
  id BIGSERIAL PRIMARY KEY, -- 权限ID
  user_id INTEGER REFERENCES public.users(id), -- 用户ID
  permission_type VARCHAR(30) CHECK (permission_type IN ('subject_group', 'grade', 'class', 'subject')), -- 权限类型
  target_id INTEGER, -- 目标ID
  target_type VARCHAR(30) CHECK (target_type IN ('subject_group', 'grade', 'class', 'subject')), -- 目标类型
  permission_scope JSONB, -- 权限范围的JSON描述
  granted_by INTEGER REFERENCES public.users(id), -- 授权人ID
  expires_at TIMESTAMP, -- 权限过期时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 权限访问日志表（租户Schema内）
CREATE TABLE tenant_001.permission_logs (
  id BIGSERIAL PRIMARY KEY, -- 日志ID
  user_id INTEGER REFERENCES public.users(id), -- 用户ID
  action VARCHAR(50) NOT NULL, -- 操作
  resource_type VARCHAR(50) NOT NULL, -- 资源类型
  resource_id INTEGER, -- 资源ID
  permission_source VARCHAR(50), -- 权限来源：role, subject_group, grade, class, special
  access_scope JSONB, -- 访问范围的JSON描述
  accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 访问时间
  ip_address INET, -- IP地址
  user_agent TEXT -- 用户代理
);



-- 试卷扫描记录表（租户Schema内）
CREATE TABLE tenant_001.paper_scans (
  id BIGSERIAL PRIMARY KEY, -- 扫描记录ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  paper_sequence INTEGER NOT NULL, -- 试卷序号
  front_page_url TEXT, -- 首页URL
  back_page_url TEXT, -- 背面URL
  scan_quality INTEGER CHECK (scan_quality BETWEEN 1 AND 10), -- 扫描质量
  scan_method VARCHAR(20) CHECK (scan_method IN ('image_upload', 'scanner_direct', 'third_party')), -- 扫描方式
  scan_device VARCHAR(100), -- 扫描设备
  is_duplicate BOOLEAN DEFAULT FALSE, -- 是否重复
  is_blank BOOLEAN DEFAULT FALSE, -- 是否空白
  is_abnormal BOOLEAN DEFAULT FALSE, -- 是否异常
  abnormal_reason TEXT, -- 异常原因
  needs_review BOOLEAN DEFAULT FALSE, -- 是否需要复核
  reviewed_by INTEGER REFERENCES public.users(id), -- 复核人ID
  reviewed_at TIMESTAMP, -- 复核时间
  minio_bucket VARCHAR(100), -- MinIO存储桶
  minio_object_key VARCHAR(500), -- MinIO对象键
  file_size BIGINT, -- 文件大小
  scan_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 扫描时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 评分监控统计表（租户Schema内）
CREATE TABLE tenant_001.grading_statistics (
  id BIGSERIAL PRIMARY KEY, -- 统计ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  question_id INTEGER NOT NULL, -- 题目ID
  question_type VARCHAR(20) NOT NULL, -- 题型
  grader_user_id INTEGER REFERENCES public.users(id), -- 阅卷员ID
  total_papers INTEGER DEFAULT 0, -- 批阅总数
  avg_score DECIMAL(5,2), -- 平均分
  min_score DECIMAL(5,2), -- 最低分
  max_score DECIMAL(5,2), -- 最高分
  score_distribution JSONB, -- 分数分布
  consistency_score DECIMAL(3,2), -- 一致性得分
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- AI阅卷记录表（租户Schema内）
CREATE TABLE tenant_001.ai_grading_records (
  id BIGSERIAL PRIMARY KEY, -- 记录ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  question_id INTEGER NOT NULL, -- 题目ID
  ai_agent_id VARCHAR(100) NOT NULL, -- AI代理ID
  ai_model_version VARCHAR(50) NOT NULL, -- AI模型版本
  ai_model_result JSONB NOT NULL, -- AI模型结果
  confidence_score DECIMAL(3,2), -- 置信度得分
  processing_time INTEGER, -- 处理时间（毫秒）
  error_message TEXT, -- 错误信息
  reviewed_by_human BOOLEAN DEFAULT FALSE, -- 是否人工复核
  human_reviewer_id INTEGER REFERENCES public.users(id), -- 人工复核员ID
  human_review_result JSONB, -- 人工复核结果
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 阅卷记录表（租户Schema内）
CREATE TABLE tenant_001.grading_records (
  id BIGSERIAL PRIMARY KEY, -- 记录ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  question_id INTEGER NOT NULL, -- 题目ID
  grader_user_id INTEGER REFERENCES public.users(id), -- 阅卷员ID
  original_score DECIMAL(5,2), -- 原始分数
  final_score DECIMAL(5,2), -- 最终分数
  grading_method VARCHAR(20) CHECK (grading_method IN ('manual', 'ai', 'hybrid')), -- 批改方式
  grading_time INTEGER, -- 批改用时（秒）
  grading_notes TEXT, -- 批改笔记
  is_reviewed BOOLEAN DEFAULT FALSE, -- 是否已复核
  reviewed_by INTEGER REFERENCES public.users(id), -- 复核人ID
  reviewed_at TIMESTAMP, -- 复核时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 评阅分发记录表（租户Schema内）
CREATE TABLE tenant_001.grading_assignments (
  id BIGSERIAL PRIMARY KEY, -- 分发ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  question_id INTEGER NOT NULL, -- 题目ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  grader_user_id INTEGER REFERENCES public.users(id), -- 阅卷员ID
  assignment_type VARCHAR(20) CHECK (assignment_type IN ('manual', 'ai', 'hybrid')), -- 分发类型
  assignment_method VARCHAR(20) CHECK (assignment_method IN ('by_quantity', 'by_difficulty', 'by_subject', 'random')), -- 分发方法
  priority_level INTEGER CHECK (priority_level BETWEEN 1 AND 5) DEFAULT 3, -- 优先级
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 分发时间
  started_at TIMESTAMP, -- 开始时间
  completed_at TIMESTAMP, -- 完成时间
  status VARCHAR(20) CHECK (status IN ('assigned', 'in_progress', 'completed', 'paused', 'cancelled')) DEFAULT 'assigned', -- 状态
  estimated_time INTEGER, -- 预计用时（秒）
  actual_time INTEGER, -- 实际用时（秒）
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 学情分析统计表（租户Schema内）
CREATE TABLE tenant_001.academic_statistics (
  id BIGSERIAL PRIMARY KEY, -- 统计ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  subject VARCHAR(50) NOT NULL, -- 学科
  total_score DECIMAL(5,2), -- 总分
  class_rank INTEGER, -- 班级排名
  grade_rank INTEGER, -- 年级排名
  school_rank INTEGER, -- 学校排名
  is_absent BOOLEAN DEFAULT FALSE, -- 是否缺考
  absent_reason TEXT, -- 缺考原因
  performance_trend JSONB, -- 表现趋势
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 题目分析统计表（租户Schema内）
CREATE TABLE tenant_001.question_analysis (
  id BIGSERIAL PRIMARY KEY, -- 分析ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  question_id INTEGER NOT NULL, -- 题目ID
  question_type VARCHAR(20) NOT NULL, -- 题型
  total_students INTEGER, -- 学生总数
  correct_count INTEGER, -- 正确人数
  score_rate DECIMAL(5,2), -- 得分率
  average_score DECIMAL(5,2), -- 平均分
  score_distribution JSONB, -- 分数分布
  option_distribution JSONB, -- 选项分布（选择题）
  zero_score_count INTEGER, -- 零分人数
  full_score_count INTEGER, -- 满分人数
  difficulty_coefficient DECIMAL(3,2), -- 难度系数
  discrimination_index DECIMAL(3,2), -- 区分度
  knowledge_points_mastery JSONB, -- 知识点掌握情况
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 阅卷控制状态表（租户Schema内）
CREATE TABLE tenant_001.grading_control_states (
  id BIGSERIAL PRIMARY KEY, -- 控制ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  question_id INTEGER, -- 题目ID
  grader_user_id INTEGER REFERENCES public.users(id), -- 阅卷员ID
  control_level VARCHAR(20) CHECK (control_level IN ('global', 'question', 'grader')), -- 控制级别
  control_action VARCHAR(20) CHECK (control_action IN ('start', 'pause', 'resume', 'stop')), -- 控制动作
  control_reason TEXT, -- 控制原因
  controlled_by INTEGER REFERENCES public.users(id), -- 控制人ID
  is_active BOOLEAN DEFAULT TRUE, -- 是否激活
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 题目成绩记录表（租户Schema内）
CREATE TABLE tenant_001.question_scores (
  id BIGSERIAL PRIMARY KEY, -- 成绩ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  question_id INTEGER NOT NULL, -- 题目ID
  question_type VARCHAR(20) NOT NULL, -- 题型
  max_score DECIMAL(5,2) NOT NULL, -- 满分
  actual_score DECIMAL(5,2) NOT NULL, -- 得分
  score_percentage DECIMAL(5,2) GENERATED ALWAYS AS (actual_score / max_score * 100) STORED, -- 得分率
  answer_content TEXT, -- 答案内容
  is_correct BOOLEAN, -- 是否正确
  difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5), -- 难度
  knowledge_points JSONB, -- 知识点
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 学号异常处理记录表（租户Schema内）
CREATE TABLE tenant_001.student_id_exceptions (
  id BIGSERIAL PRIMARY KEY, -- 异常ID
  scan_id INTEGER REFERENCES tenant_001.paper_scans(id), -- 扫描记录ID
  detected_student_id VARCHAR(50), -- 检测到的学号
  exception_type VARCHAR(30) CHECK (exception_type IN ('unrecognized', 'blurred', 'missing', 'invalid', 'duplicate')), -- 异常类型
  suggested_students JSONB, -- 建议的学生列表
  confirmed_student_id INTEGER REFERENCES tenant_001.students(id), -- 确认的学生ID
  confirmed_by INTEGER REFERENCES public.users(id), -- 确认人ID
  confirmed_at TIMESTAMP, -- 确认时间
  resolution_method VARCHAR(20) CHECK (resolution_method IN ('auto_match', 'manual_input', 'name_match', 'teacher_confirm')), -- 解决方式
  processing_notes TEXT, -- 处理笔记
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 试卷扫描异常记录表（租户Schema内）
CREATE TABLE tenant_001.paper_scan_exceptions (
  id BIGSERIAL PRIMARY KEY, -- 异常ID
  scan_id INTEGER REFERENCES tenant_001.paper_scans(id), -- 扫描记录ID
  exception_type VARCHAR(30) CHECK (exception_type IN ('duplicate', 'blank', 'blurred', 'damaged', 'orientation', 'other')), -- 异常类型
  exception_description TEXT, -- 异常描述
  auto_detected BOOLEAN DEFAULT TRUE, -- 是否自动检测
  confidence_score DECIMAL(3,2), -- 置信度
  resolution_status VARCHAR(20) CHECK (resolution_status IN ('pending', 'resolved', 'ignored')) DEFAULT 'pending', -- 解决状态
  resolved_by INTEGER REFERENCES public.users(id), -- 解决人ID
  resolved_at TIMESTAMP, -- 解决时间
  resolution_notes TEXT, -- 解决笔记
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 特殊权限申请表（租户Schema内）
CREATE TABLE tenant_001.special_permission_requests (
  id BIGSERIAL PRIMARY KEY, -- 申请ID
  applicant_user_id INTEGER REFERENCES public.users(id), -- 申请人ID
  permission_type VARCHAR(30) CHECK (permission_type IN ('temporary_management', 'cross_level_view', 'emergency_operation', 'data_recovery')), -- 权限类型
  target_type VARCHAR(30) CHECK (target_type IN ('subject_group', 'grade', 'class', 'student', 'exam', 'system')), -- 目标类型
  target_ids INTEGER[], -- 目标对象ID数组
  request_reason TEXT NOT NULL, -- 申请原因
  business_justification TEXT NOT NULL, -- 业务理由
  urgency_level VARCHAR(20) CHECK (urgency_level IN ('normal', 'urgent', 'critical')) DEFAULT 'normal', -- 紧急程度
  requested_start_time TIMESTAMP NOT NULL, -- 申请开始时间
  requested_end_time TIMESTAMP NOT NULL, -- 申请结束时间
  permission_scope JSONB NOT NULL, -- 详细权限范围描述
  status VARCHAR(20) CHECK (status IN ('pending', 'approved', 'rejected', 'expired', 'revoked')) DEFAULT 'pending', -- 状态
  risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high')), -- 风险等级
  auto_risk_score INTEGER CHECK (auto_risk_score BETWEEN 0 AND 100), -- 自动风险评分
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 特殊权限审批记录表（租户Schema内）
CREATE TABLE tenant_001.special_permission_approvals (
  id BIGSERIAL PRIMARY KEY, -- 审批ID
  request_id INTEGER REFERENCES tenant_001.special_permission_requests(id), -- 申请ID
  approver_user_id INTEGER REFERENCES public.users(id), -- 审批人ID
  approval_level INTEGER NOT NULL, -- 审批级别：1=直属上级，2=跨级，3=多级联合
  approval_action VARCHAR(20) CHECK (approval_action IN ('approve', 'reject', 'request_more_info')) NOT NULL, -- 审批动作
  approval_comment TEXT, -- 审批意见
  approved_scope JSONB, -- 实际批准的权限范围（可能与申请范围不同）
  approved_start_time TIMESTAMP, -- 批准的开始时间
  approved_end_time TIMESTAMP, -- 批准的结束时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 特殊权限使用日志表（租户Schema内）
CREATE TABLE tenant_001.special_permission_usage_logs (
  id BIGSERIAL PRIMARY KEY, -- 日志ID
  request_id INTEGER REFERENCES tenant_001.special_permission_requests(id), -- 申请ID
  user_id INTEGER REFERENCES public.users(id), -- 使用人ID
  action VARCHAR(50) NOT NULL, -- 操作
  resource_type VARCHAR(50) NOT NULL, -- 资源类型
  resource_id INTEGER, -- 资源ID
  access_details JSONB, -- 访问详情
  is_within_scope BOOLEAN NOT NULL, -- 是否在授权范围内
  risk_score INTEGER CHECK (risk_score BETWEEN 0 AND 100), -- 风险评分
  ip_address INET, -- IP地址
  user_agent TEXT, -- 用户代理
  accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 访问时间
);

-- 特殊权限监控告警表（租户Schema内）
CREATE TABLE tenant_001.special_permission_alerts (
  id BIGSERIAL PRIMARY KEY, -- 告警ID
  request_id INTEGER REFERENCES tenant_001.special_permission_requests(id), -- 申请ID
  alert_type VARCHAR(30) CHECK (alert_type IN ('scope_violation', 'suspicious_activity', 'excessive_usage', 'approaching_expiry')), -- 告警类型
  alert_level VARCHAR(20) CHECK (alert_level IN ('info', 'warning', 'critical')) NOT NULL, -- 告警级别
  alert_message TEXT NOT NULL, -- 告警信息
  alert_details JSONB, -- 告警详情
  is_resolved BOOLEAN DEFAULT false, -- 是否已解决
  resolved_by INTEGER REFERENCES public.users(id), -- 解决人ID
  resolved_at TIMESTAMP, -- 解决时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

-- 题卡块表（租户Schema内）
CREATE TABLE tenant_001.answer_card_blocks (
  id BIGSERIAL PRIMARY KEY, -- 题卡块ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  block_name VARCHAR(100) NOT NULL, -- 题卡块名称
  block_type VARCHAR(30) CHECK (block_type IN ('single_question', 'multi_question', 'composite')), -- 题卡块类型
  position_info JSONB NOT NULL, -- 位置信息（坐标、尺寸等）
  template_version VARCHAR(50), -- 模板版本
  max_score DECIMAL(5,2), -- 题卡块满分
  is_active BOOLEAN DEFAULT TRUE, -- 是否激活
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 题卡块与试题关联表（租户Schema内）
CREATE TABLE tenant_001.card_block_question_links (
  id BIGSERIAL PRIMARY KEY, -- 关联ID
  card_block_id INTEGER REFERENCES tenant_001.answer_card_blocks(id), -- 题卡块ID
  question_id INTEGER NOT NULL, -- 试题ID
  link_type VARCHAR(20) CHECK (link_type IN ('one_to_one', 'one_to_many', 'many_to_one', 'many_to_many')), -- 关联类型
  weight_ratio DECIMAL(3,2) DEFAULT 1.0, -- 权重比例
  score_mapping JSONB, -- 分数映射规则
  is_primary BOOLEAN DEFAULT TRUE, -- 是否主要关联
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  UNIQUE(card_block_id, question_id) -- 唯一性约束
);

-- 题卡块评分记录表（租户Schema内）
CREATE TABLE tenant_001.card_block_grading_records (
  id BIGSERIAL PRIMARY KEY, -- 评分记录ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  student_id INTEGER REFERENCES tenant_001.students(id), -- 学生ID
  card_block_id INTEGER REFERENCES tenant_001.answer_card_blocks(id), -- 题卡块ID
  grader_user_id INTEGER REFERENCES public.users(id), -- 评分员ID
  raw_score DECIMAL(5,2) NOT NULL, -- 原始分数
  adjusted_score DECIMAL(5,2), -- 调整后分数
  grading_method VARCHAR(20) CHECK (grading_method IN ('manual', 'ai', 'hybrid')), -- 评分方式
  confidence_score DECIMAL(3,2), -- 置信度
  grading_notes TEXT, -- 评分备注
  quality_level VARCHAR(20) CHECK (quality_level IN ('excellent', 'good', 'fair', 'poor')), -- 质量等级
  is_reviewed BOOLEAN DEFAULT FALSE, -- 是否已复核
  reviewed_by INTEGER REFERENCES public.users(id), -- 复核人ID
  reviewed_at TIMESTAMP, -- 复核时间
  grading_duration INTEGER, -- 评分用时（秒）
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

-- 学习记录表（租户Schema内）
CREATE TABLE tenant_001.learning_records (
  id BIGSERIAL PRIMARY KEY, -- 学习记录ID
  student_id INTEGER REFERENCES tenant_001.students(id) NOT NULL, -- 学生ID
  question_id INTEGER NOT NULL, -- 题目ID
  grading_record_id INTEGER REFERENCES tenant_001.card_block_grading_records(id), -- 评分记录ID
  exam_id INTEGER REFERENCES tenant_001.exams(id), -- 考试ID
  subject VARCHAR(50) NOT NULL, -- 学科
  knowledge_points JSONB, -- 知识点
  difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5), -- 难度等级
  student_score DECIMAL(5,2) NOT NULL, -- 学生得分
  max_score DECIMAL(5,2) NOT NULL, -- 满分
  score_rate DECIMAL(5,2) GENERATED ALWAYS AS (student_score / max_score * 100) STORED, -- 得分率
  mastery_level VARCHAR(20) CHECK (mastery_level IN ('excellent', 'good', 'fair', 'poor', 'not_mastered')), -- 掌握程度
  learning_suggestions JSONB, -- 学习建议
  recommended_exercises JSONB, -- 推荐练习
  improvement_areas JSONB, -- 改进方向
  historical_comparison JSONB, -- 历史对比
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 生��时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  UNIQUE(student_id, question_id, exam_id) -- 唯一性约束
);

-- 学习记录版本表（租户Schema内）
CREATE TABLE tenant_001.learning_record_versions (
  id BIGSERIAL PRIMARY KEY, -- 版本ID
  learning_record_id INTEGER REFERENCES tenant_001.learning_records(id), -- 学习记录ID
  version_number INTEGER NOT NULL, -- 版本号
  change_reason VARCHAR(100), -- 变更原因
  changed_fields JSONB, -- 变更字段
  previous_data JSONB, -- 变更前数据
  changed_by INTEGER REFERENCES public.users(id), -- 变更人ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
```

## 6.4 API架构设计

### 6.4.1 REST API规范
- **版本控制**：URL路径版本控制 `/api/v1/`
- **认证方式**：JWT Token + API Key双重认证
- **数据格式**：JSON格式，统一响应结构
- **错误处理**：标准HTTP状态码 + 详细错误信息
- **限流策略**：基于用户角色的分级限流
- **文档规范**：OpenAPI 3.0规范，自动生成API文档

### 6.4.2 核心API模块
```http
# 用户认证模块
POST /api/v1/auth/send-verification-code # 发送手机验证码
POST /api/v1/auth/register             # 用户使用手机号和验证码注册
POST /api/v1/auth/login                # 登录 (支持手机号/用户名)
POST /api/v1/auth/logout
POST /api/v1/auth/refresh
GET  /api/v1/auth/profile

# 身份绑定与管理模块
POST /api/v1/identity/bind             # (注册用户) 通过个人信息（姓名+学号/工号）进行自服务身份绑定
POST /api/v1/identity/unbind           # (用户) 用户自己解绑身份
POST /api/v1/admin/identity/unbind     # (系统运营) 强制解绑指定用户的身份，用于处理误绑申诉

# 租户管理模块
GET    /api/v1/tenants
POST   /api/v1/tenants
PUT    /api/v1/tenants/{id}
DELETE /api/v1/tenants/{id}

# 组织架构管理模块
GET    /api/v1/subject-groups        # 获取学科组列表
POST   /api/v1/subject-groups        # 创建学科组
PUT    /api/v1/subject-groups/{id}   # 更新学科组
DELETE /api/v1/subject-groups/{id}   # 删除学科组
POST   /api/v1/subject-groups/{id}/assign-leader # DEPRECATED: Use User Identities module

GET    /api/v1/grades                # 获取年级列表
POST   /api/v1/grades                # 创建年级
PUT    /api/v1/grades/{id}           # 更新年级
DELETE /api/v1/grades/{id}           # 删除年级
POST   /api/v1/grades/{id}/assign-leader # DEPRECATED: Use User Identities module

GET    /api/v1/classes               # 获取班级列表
POST   /api/v1/classes               # 创建班级
PUT    /api/v1/classes/{id}          # 更新班级
DELETE /api/v1/classes/{id}          # 删除班级
POST   /api/v1/classes/{id}/copy     # 班级升学复制
POST   /api/v1/classes/{id}/assign-teacher # DEPRECATED: Use User Identities module

# 用户身份管���模块 (New)
GET    /api/v1/users/{userId}/identities  # 获取用户的多重身份
POST   /api/v1/users/{userId}/identities  # 为用户添加新身份
DELETE /api/v1/users/{userId}/identities/{identityId} # 移除用户身份
POST   /api/v1/users/{userId}/switch-identity # 切换用户身份
GET    /api/v1/users/{userId}/identity-history # 获取身份切换历史

# 权限管理模块
GET    /api/v1/permissions/user/{userId}     # 获取用户权限信息
POST   /api/v1/permissions/calculate        # 计算用户数据访问范围
GET    /api/v1/permissions/matrix           # 获取权限矩阵
POST   /api/v1/permissions/assign           # 分配特殊权限
DELETE /api/v1/permissions/{id}             # 撤销权限
GET    /api/v1/permissions/logs             # 获取权限访问日志

# 特殊权限管理模块
POST   /api/v1/special-permissions/request         # 申请特殊权限
GET    /api/v1/special-permissions/requests        # 获取特殊权限申请列表
GET    /api/v1/special-permissions/requests/{id}   # 获取特殊权限申请详情
PUT    /api/v1/special-permissions/requests/{id}   # 更新特殊权限申请
DELETE /api/v1/special-permissions/requests/{id}   # 撤销特殊权限申请

POST   /api/v1/special-permissions/approve/{id}    # 审批特殊权限申请
POST   /api/v1/special-permissions/reject/{id}     # 拒绝特殊权限申请
POST   /api/v1/special-permissions/revoke/{id}     # 撤销已授权的特殊权限
POST   /api/v1/special-permissions/extend/{id}     # 延期特殊权限

GET    /api/v1/special-permissions/active          # 获取当前生效的特殊权限
GET    /api/v1/special-permissions/pending         # 获取待审批的特殊权限
GET    /api/v1/special-permissions/history         # 获取特殊权限历史记录

GET    /api/v1/special-permissions/usage-logs      # 获取特殊权限使用日志
GET    /api/v1/special-permissions/alerts          # 获取特殊权限告警
POST   /api/v1/special-permissions/alerts/{id}/resolve # 解决特殊权限告警

POST   /api/v1/special-permissions/risk-assessment # 特殊权限风险评估
GET    /api/v1/special-permissions/audit-report    # 生成特殊权限审计报告

# 考试管理模块
GET    /api/v1/exams
POST   /api/v1/exams
PUT    /api/v1/exams/{id}
DELETE /api/v1/exams/{id}
POST   /api/v1/exams/{id}/invite      # 联考邀请
POST   /api/v1/exams/{id}/join        # 加入联考

# 阅卷管理模块
POST   /api/v1/grading/upload         # 试卷上传
POST   /api/v1/grading/scan           # 扫描识别
GET    /api/v1/grading/progress       # 阅卷进度
POST   /api/v1/grading/review         # 人工复核
POST   /api/v1/grading/publish        # 成绩发布

# 题卡块管理模块 (New)
GET    /api/v1/card-blocks            # 获取题卡块列表
POST   /api/v1/card-blocks            # 创建题卡块
PUT    /api/v1/card-blocks/{id}       # 更新题卡块
DELETE /api/v1/card-blocks/{id}       # 删除题卡块
GET    /api/v1/card-blocks/{id}/questions # 获取题卡块关联的试题
POST   /api/v1/card-blocks/{id}/link-questions # 关联试题到题卡块
DELETE /api/v1/card-blocks/{id}/unlink-questions # 取消题卡块与试题关联
GET    /api/v1/card-blocks/{id}/grading-records # 获取题卡块评分记录
POST   /api/v1/card-blocks/{id}/grade # 对题卡块进行评分

# 学情分析模块
GET    /api/v1/analytics/scores       # 成绩分析
GET    /api/v1/analytics/trends       # 趋势分析
GET    /api/v1/analytics/reports      # 报告生成
GET    /api/v1/analytics/compare      # 对比分析

# 学习记录管理模块 (New)
GET    /api/v1/learning-records       # 获取学习记录列表
GET    /api/v1/learning-records/{id}  # 获取学习记录详情
POST   /api/v1/learning-records/generate # 生成学习记录
PUT    /api/v1/learning-records/{id}  # 更新学习记录
GET    /api/v1/learning-records/student/{studentId} # 获取学生的学习记录
GET    /api/v1/learning-records/exam/{examId} # 获取考试的学习记录
GET    /api/v1/learning-records/{id}/versions # 获取学习记录版本历史
POST   /api/v1/learning-records/batch-generate # 批量生成学习记录

# 教辅混合内容管理模块 (New)
GET    /api/v1/textbooks/{id}/mixed-contents # 获取教辅混合内容列表
POST   /api/v1/textbooks/{id}/mixed-contents # 创建教辅混合内容
PUT    /api/v1/textbooks/{id}/mixed-contents/{contentId} # 更新混合内容
DELETE /api/v1/textbooks/{id}/mixed-contents/{contentId} # 删除混合内容
GET    /api/v1/textbooks/{id}/chapters/{chapterId}/modules/{moduleId}/contents # 获取模块内容
POST   /api/v1/textbooks/{id}/chapters/{chapterId}/modules/{moduleId}/reorder # 重新排序模块内容

# 内容阅读顺序管理模块 (Enhanced)
GET    /api/v1/content-reading-orders/textbook/{textbookId} # 获取教辅阅读顺序
PUT    /api/v1/content-reading-orders/textbook/{textbookId} # 更新教辅阅读顺序
GET    /api/v1/content-reading-orders/module/{moduleId} # 获取模块阅读顺序
POST   /api/v1/content-reading-orders/validate # 验证阅读顺序有效性
GET    /api/v1/content-reading-orders/personalized # 获取个性化阅读顺序

# 题库增强功能模块 (Enhanced)
GET    /api/v1/questions/{id}/answers # 获取题目多答案
POST   /api/v1/questions/{id}/answers # 添加题目答案
PUT    /api/v1/questions/{id}/answers/{answerId} # 更新答案
DELETE /api/v1/questions/{id}/answers/{answerId} # 删除答案
GET    /api/v1/questions/{id}/explanations # 获取题目多层次解析
POST   /api/v1/questions/{id}/explanations # 添加题目解析
PUT    /api/v1/questions/{id}/explanations/{explanationId} # 更新解析
DELETE /api/v1/questions/{id}/explanations/{explanationId} # 删除解析
GET    /api/v1/questions/{id}/difficulties # 获取题目多维度难度
POST   /api/v1/questions/{id}/difficulties # 添加难度信息
PUT    /api/v1/questions/{id}/difficulties/{difficultyId} # 更新难度
DELETE /api/v1/questions/{id}/difficulties/{difficultyId} # 删除难度
GET    /api/v1/questions/{id}/knowledge-points # 获取题目知识点关联
POST   /api/v1/questions/{id}/knowledge-points # 关联知识点
DELETE /api/v1/questions/{id}/knowledge-points/{kpId} # 取消知识点关联


```

### 6.4.3 API响应格式

```json
{
   "success": true,
   "code": 200,
   "message": "操作成功",
   "data": {
      "id": 1,
      "name": "张三"
   },
   "meta": {
      "timestamp": "2025-01-01T00:00:00Z",
      "request_id": "req_123456",
      "pagination": {
         "page": 1,
         "per_page": 20,
         "total": 100,
         "total_pages": 5
      }
   }
}
```

### 6.4.4 错误处理规范
| HTTP状态码 | 错误类型  | 处理方式            |
|---------|-------|-----------------|
| 400     | 参数错误  | 返回详细字段错误信息      |
| 401     | 认证失败  | 引导用户重新登录        |
| 403     | 权限不足  | 返回权限要求说明        |
| 404     | 资源不存在 | 返回资源标识信息        |
| 429     | 请求过频  | 返回限流信息和重试建议     |
| 500     | 服务器错误 | 记录错误日志，返回通用错误信息 |

### 6.4.5 实时通信架构
- **WebSocket连接**：考试状态、阅卷进度实时���送
- **Server-Sent Events**：成绩发布、系统通知推送
- **消息队列**：Redis Streams处理异步任务
- **事件驱动**：发布订阅模式处理跨模块通信

## 6.5 第三方集成架构

### 6.5.1 教务系统集成
- **标准接口**：支持REST API和SOAP协议
- **数据同步**：学生信息、班级信息、课程信息同步
- **认证集成**：支持LDAP、CAS、OAuth2.0单点登录
- **数据映射**：灵活的字段映射配置

### 6.5.2 文件存储集成
- **主存储方案**：MinIO分布式对象存储为主要存储方案
- **兼容性支持**：兼容S3 API，支持阿里云OSS、AWS S3作为备用存储
- **存储策略**：按租户隔离的bucket策略，支持数据分层存储
- **备份策略**：MinIO集群多副本机制，支持跨区域数据复制
- **图片处理**：集成ImageMagick进行图像处理和格式转换
- **安全控制**：基于IAM的访问控制，支持数据加密存储

### 6.5.3 AI服务集成
- **OCR识别**：百度、腾讯、阿里云OCR服务
- **智能评分**：自研AI算法和第三方AI服务
- **图像识别**：答题卡识别和试卷结构分析

## 6.6 权限模型实现

### 6.6.1 Casbin-RS 权限模型架构

平台采用Casbin-RS作为权限模型实现库，基于RBAC+ABAC模型设计，支持复杂的权限控制需求。

#### 6.6.1.1 技术选型

- **核心库**：Casbin-RS (Rust版Casbin)
- **数据库适配器**：sqlx-adapter (支持PostgreSQL持久化)
- **模型类型**：RBAC (基于角色) + ABAC (基于属性)
- **权限粒度**：支持到资源级别的细粒度权限控制

#### 6.6.1.2 权限模型配置

```conf
# 权限模型定义 (model.conf)
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act, eft

[role_definition]
g = _, _
g2 = _, _

[policy_effect]
e = some(where (p.eft == allow)) && !some(where (p.eft == deny))

[matchers]
m = g(r.sub, p.sub) && g2(r.obj, p.obj) && r.act == p.act || \
    r.sub.CanAccess(r.obj, r.act) || \
    r.sub.HasRole("系统管理员")
```

#### 6.6.1.3 数据库持久化

Casbin策略将持久化到PostgreSQL数据库中，使用以下表结构：

```sql
-- 权限策略表 (public schema)
CREATE TABLE IF NOT EXISTS public.casbin_rule (
    id SERIAL PRIMARY KEY,
    ptype VARCHAR(12) NOT NULL,
    v0 VARCHAR(128) NOT NULL,
    v1 VARCHAR(128) NOT NULL,
    v2 VARCHAR(128) NOT NULL,
    v3 VARCHAR(128) NOT NULL,
    v4 VARCHAR(128) NOT NULL,
    v5 VARCHAR(128) NOT NULL,
    CONSTRAINT unique_key_casbin UNIQUE(ptype, v0, v1, v2, v3, v4, v5)
);
```

#### 6.6.1.4 权限模型集成架构

```
┌─────────────────────────┐
│     Axum Web Server     │
├─────────────────────────┤
│  Authorization Middleware │
├─────────────────────────┤
│     Casbin Enforcer     │
├─────────────────────────┤
│  Dynamic Policy Loader  │
├─────────────────────────┤
│   PostgreSQL Adapter    │
└─────────────────────────┘
```

#### 6.6.1.5 权限管理API

系统提供以下API用于管理员管理权限策略：

```
GET    /api/v1/permissions/policies         # 获取所有权限策略
POST   /api/v1/permissions/policies         # 添加权限策略
DELETE /api/v1/permissions/policies         # 删除权限策略
GET    /api/v1/permissions/roles            # 获取角色列表
POST   /api/v1/permissions/roles            # 添加角色
DELETE /api/v1/permissions/roles/{role}     # 删除角色
POST   /api/v1/permissions/roles/assignment # 分配角色
GET    /api/v1/permissions/check            # 检查权限
```

#### 6.6.1.6 权限缓存机制

为提高性能，系统使用Redis缓存权限检查结果：

```rust
// 缓存键格式: casbin:result:{subject}:{object}:{action}
// 缓存值: true/false (是否有权限)
// 缓存过期时间: 5分钟
```

### 6.6.2 权限模型实现细节

#### 6.6.2.1 动态权限计算

系统支持基于用户身份、组织架构和特殊授权的动态权限计算：



#### 6.6.2.2 权限策略示例

```
# 角色权限策略
p, role:校长, resource:*, action:*, allow
p, role:教导主任, resource:teaching:*, action:*, allow
p, role:学科组长, resource:subject:{subject}:*, action:*, allow
p, role:年级长, resource:grade:{grade}:*, action:*, allow
p, role:班主任, resource:class:{class}:*, action:*, allow
p, role:任课老师, resource:class:{class}:subject:{subject}:*, action:read, allow

# 角色继承关系
g, user:alice, role:校长
g, user:bob, role:学科组长
g, user:charlie, role:任课老师

# 属性权限策略
p, user:alice, resource:exam:*, action:create, allow, alice.role == "校长"
p, user:bob, resource:exam:math:*, action:create, allow, bob.subject == "math"
```

### 6.6.3 权限管理最佳实践

#### 6.6.3.1 安全策略

1. **最小权限原则**：用户只获得完成工作所需的最小权限
2. **权限分离**：敏感操作需要多重权限验证
3. **定期审计**：定期检查和清理过期权限
4. **权限继承控制**：严格控制角色继承关系

#### 6.6.3.2 性能优化

1. **权限缓存**：使用Redis缓存频繁访问的权限检查结果
2. **批量加载**：批量加载用户权限，减少数据库查询
3. **索引优化**：为权限表创建合适的索引
4. **异步处理**：权限变更异步更新缓存



#### 6.6.3.3 管理员操作界面

系统提供Web界面供管理员管理权限：

1. **策略管理**：可视化添加、编辑、删除权限策略
2. **角色管理**：管理角色定义和角色分配
3. **权限测试**：测试特定用户对特定资源的权限
4. **审计日志**：查看权限访问和变更历史
5. **批量操作**：支持批量导入/导出权限配置

#### 6.6.3.4 迁移和升级策略

1. **渐进式迁移**：先在新功能中使用Casbin，逐步迁移现有功能
2. **双写验证**：迁移期间同时写入新旧权限系统，验证一致性
3. **回滚机制**：保留回滚到原权限系统的能力
4. **版本兼容**：确保权限策略的向后兼容性

## 6.7 数据架构扩展

### 6.7.1 数据仓库设计
```sql
-- 数据仓库Schema
CREATE SCHEMA dw_deep_mate;

-- 事实表：考试成绩事实
CREATE TABLE dw_deep_mate.fact_exam_scores (
  id BIGSERIAL PRIMARY KEY,
  tenant_id INTEGER NOT NULL,
  exam_id INTEGER NOT NULL,
  student_id INTEGER NOT NULL,
  subject_id INTEGER NOT NULL,
  score DECIMAL(5,2) NOT NULL,
  full_score DECIMAL(5,2) NOT NULL,
  exam_date DATE NOT NULL,
  grade_level INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 维度表：时间维度
CREATE TABLE dw_deep_mate.dim_time (
  date_key INTEGER PRIMARY KEY,
  full_date DATE NOT NULL,
  year INTEGER NOT NULL,
  quarter INTEGER NOT NULL,
  month INTEGER NOT NULL,
  week INTEGER NOT NULL,
  day INTEGER NOT NULL,
  is_holiday BOOLEAN DEFAULT FALSE
);

-- 维度表：学生维度
CREATE TABLE dw_deep_mate.dim_student (
  student_key INTEGER PRIMARY KEY,
  tenant_id INTEGER NOT NULL,
```
