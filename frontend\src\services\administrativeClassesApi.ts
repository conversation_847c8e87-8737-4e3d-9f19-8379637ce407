import { ApiResponse } from '@/types';
import { AdministrativeClasses, AdministrativeClassesDetail, CreateAdministrativeClassesParams } from '@/types/administrativeClasses';
import { ClassesStatistics } from '@/types/classess';
import apiClient from './examApi';

/**
 * 作者：张瀚
 * 说明：行政班级管理API
 */
export const AdministrativeClassesApi = {
  /**
   * 作者：张瀚
   * 说明：统计班级管理信息
   */
  getStatistics: async (tenantName: string): Promise<ApiResponse<ClassesStatistics>> => {
    return apiClient.get(`/api/v1/tenants/${tenantName}/administrativeClasses/getStatistics`);
  },
  /**
   * 作者：张瀚
   * 说明：获取有权限的行政班列表
   */
  getUserClassList: async (tenantName: string): Promise<ApiResponse<AdministrativeClassesDetail[]>> => {
    return apiClient.get(`/api/v1/tenants/${tenantName}/administrativeClasses/getUserClassList`);
  },
  /**
   * 作者：张瀚
   * 说明：创建班级
   */
  createClasses: async (tenantName: string, params: CreateAdministrativeClassesParams): Promise<ApiResponse<AdministrativeClasses>> => {
    return apiClient.post(`/api/v1/tenants/${tenantName}/administrativeClasses/createClasses`, params);
  },
};
