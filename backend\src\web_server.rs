use crate::middleware::auth_middleware::auth_middleware;
use crate::routes_builder::{create_protected_routes, create_public_routes};
use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;
use crate::service::classes::classes_service::ClassesService;
use crate::service::education_stage::EducationStageService;
use crate::service::grade::grade_service::GradeService;
use crate::service::homework::homework_service::HomeworkService;
use crate::service::role::role_service::RoleService;
use crate::service::storage::StorageService;
use crate::service::student::student_service::StudentService;
use crate::service::subject::SubjectService;
use crate::service::teacher::teacher_service::TeacherService;
use crate::service::teaching_aids::TeachingAidsService;
use crate::service::teaching_classes::teaching_classes_service::TeachingClassesService;
use crate::service::user::auth_integration::AuthIntegration;
use crate::service::user::UserService;
use axum::{middleware, Router};
use std::sync::Arc;
use tower_http::cors::{Any, CorsLayer};

#[derive(Clone)]
pub struct AppState {
    pub db: sqlx::PgPool,
    pub storage_service: Arc<dyn StorageService>,
    pub auth_integration: Arc<AuthIntegration>,
    pub classes_service: Arc<ClassesService>,
    pub administrative_classes_service: Arc<AdministrativeClassesService>,
    pub teaching_classes_service: Arc<TeachingClassesService>,
    pub user_service: UserService,
    pub role_service: RoleService,
    pub subject_service: SubjectService,
    pub grade_service: GradeService,
    pub student_service: StudentService,
    pub teacher_service: TeacherService,
    pub homework_service: Arc<HomeworkService>,
    pub education_stage_service: EducationStageService,
    pub teaching_aids_service: Arc<TeachingAidsService>,
}

/// 构建 Router，并注入数据库连接池
pub fn build_app(state: AppState) -> Router {
    // Public routes (no auth required)
    let public_auth_routes = create_public_routes(&state);

    let protected_auth_routes = create_protected_routes(&state).route_layer(
        middleware::from_fn_with_state(state.db.clone(), auth_middleware),
    );

    Router::new()
        .with_state(state)
        .nest("/api/v1", public_auth_routes)
        .nest("/api/v1", protected_auth_routes)
        .layer(
            CorsLayer::new()
                .allow_origin(Any)
                .allow_methods(Any)
                .allow_headers(Any),
        )
}
