{"rustc": 1842507548689473721, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 2241668132362809309, "path": 7193815413178644501, "deps": [[1288403060204016458, "tokio_util", false, 14161935976751839612], [1906322745568073236, "pin_project_lite", false, 16220677842723031536], [7620660491849607393, "futures_core_03", false, 4917151281608340555], [9538054652646069845, "tokio_dep", false, 6155195535907693298], [15932120279885307830, "memchr", false, 7712901758548144593], [16066129441945555748, "bytes", false, 92163385925654338]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\combine-f763bc2dc85e2e37\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}