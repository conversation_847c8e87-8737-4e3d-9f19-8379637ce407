import {
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary,
    Tenant, WorkflowCategory,
    workflowCategoryOptions,
    WorkflowSettingQueryParams, WorkflowSummary
} from "@/types";
import {FC, useEffect, useRef, useState} from "react"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";
import {questionTypeApi} from "@/services/questionApi.ts";
import subjectApi from "@/services/subjectApi.ts";
import gradeApi from "@/services/gradeApi.ts";
import {getTenants} from "@/services/tenantApi.ts";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table.tsx";

interface WorkflowSettingFilterPreviewProps {
    ignoreCategory?: boolean
    questionTypeSummaries?: QuestionTypeSummary[] | false
    subjectSummaries?: SubjectSummary[] | false
    gradeLevelSummaries?: GradeLevelSummary[] | false
    tenants?: Tenant[] | false
    onFilterChange?: (filter: WorkflowSettingQueryParams) => void;
    preview?: WorkflowSummary[] | false;
}

/**
 * WorkflowSettingFilterPreview 组件
 *
 * 用于在工作流配置中提供筛选功能，并可选地展示工作流预览表格。
 *
 * ### 筛选项显示逻辑
 * - **传入数组**：直接使用该数组作为选项。
 * - **未传入 (`undefined`)**：组件内部会自动请求并加载选项数据。
 * - **传入 `false`**：隐藏该筛选项。
 *
 * ### preview 显示逻辑
 * - **传入数组**：渲染表格，数组为空则显示“暂无数据”。
 * - **未传入 (`undefined`)**：自动设置为空数组并显示“暂无数据”。
 * - **传入 `false`**：隐藏预览表格区域。
 *
 * @param ignoreCategory           是否忽略“流程类型”筛选（true 则不渲染）
 * @param questionTypeSummaries    题型选项，见显示逻辑
 * @param subjectSummaries         学科选项，见显示逻辑
 * @param gradeLevelSummaries      年级选项，见显示逻辑
 * @param tenants                  租户选项，见显示逻辑
 * @param onFilterChange           当筛选条件变化时触发的回调
 * @param preview                  工作流预览数据，见显示逻辑
 *
 * @example
 * // 题型自动加载，学科禁用，年级自定义数据，租户自动加载，预览表格禁用
 * <WorkflowSettingFilterPreview
 *   questionTypeSummaries={undefined}
 *   subjectSummaries={false}
 *   gradeLevelSummaries={[{ code: 'G1', name: '一年级' }]}
 *   tenants={undefined}
 *   preview={false}
 *   onFilterChange={(filter) => console.log(filter)}
 * />
 */

const WorkflowSettingFilterPreview: FC<WorkflowSettingFilterPreviewProps> = ({
                                                                                 ignoreCategory,
                                                                                 questionTypeSummaries,
                                                                                 subjectSummaries,
                                                                                 gradeLevelSummaries,
                                                                                 tenants,
                                                                                 onFilterChange,
                                                                                 preview
                                                                             }) => {
    const [workflowCategoryValue, setWorkflowCategoryValue] = useState<WorkflowCategory | '' | undefined>(undefined);
    const [questionTypeSelect, setQuestionTypeSelect] = useState<string | undefined>(undefined);
    const [subjectSelect, setSubjectSelect] = useState<string | undefined>(undefined);
    const [gradeLevelSelect, setGradeLevelSelect] = useState<string | undefined>(undefined);
    const [tenantSelect, setTenantSelect] = useState<string | undefined>(undefined);

    const [innerQuestionTypeSummaries, setInnerQuestionTypeSummaries] = useState<QuestionTypeSummary[]>([]);
    const [innerSubjectSummaries, setInnerSubjectSummaries] = useState<SubjectSummary[]>([]);
    const [innerGradeLevelSummaries, setInnerGradeLevelSummaries] = useState<GradeLevelSummary[]>([]);
    const [innerTenants, setInnerTenants] = useState<Tenant[]>([]);

    // 三态处理加载
    useEffect(() => {
        const loadData = async () => {
            // 题型
            if (questionTypeSummaries === false) {
                setInnerQuestionTypeSummaries([]);
            } else if (questionTypeSummaries) {
                setInnerQuestionTypeSummaries(questionTypeSummaries);
            } else {
                const res = await questionTypeApi.getQuestionTypeSummaries();
                setInnerQuestionTypeSummaries(res.data ?? []);
            }

            // 学科
            if (subjectSummaries === false) {
                setInnerSubjectSummaries([]);
            } else if (subjectSummaries) {
                setInnerSubjectSummaries(subjectSummaries);
            } else {
                const res = await subjectApi.getSubjectSummaries();
                setInnerSubjectSummaries(res.data ?? []);
            }

            // 年级
            if (gradeLevelSummaries === false) {
                setInnerGradeLevelSummaries([]);
            } else if (gradeLevelSummaries) {
                setInnerGradeLevelSummaries(gradeLevelSummaries);
            } else {
                const res = await gradeApi.getGradeSummaries();
                setInnerGradeLevelSummaries(res.data ?? []);
            }

            // 租户
            if (tenants === false) {
                setInnerTenants([]);
            } else if (tenants) {
                setInnerTenants(tenants);
            } else {
                const res = await getTenants();
                setInnerTenants(res ?? []);
            }
        };
        loadData().then();
    }, [questionTypeSummaries, subjectSummaries, gradeLevelSummaries, tenants]);

    // 监听筛选条件变化
    const firstRender = useRef(true);
    useEffect(() => {
        if (firstRender.current) {
            firstRender.current = false;
            return;
        }
        onFilterChange?.({
            workflow_type: workflowCategoryValue || undefined,
            question_type_code: questionTypeSelect || undefined,
            subject_code: subjectSelect || undefined,
            grade_level_code: gradeLevelSelect || undefined,
            schema_name: tenantSelect || undefined,
        });
    }, [workflowCategoryValue, questionTypeSelect, subjectSelect, gradeLevelSelect, tenantSelect]);

    return (
        <div>
            <div className="w-full flex justify-between gap-4 p-4">
                {/* 流程类型 */}
                {!ignoreCategory && (
                    <Select
                        value={workflowCategoryValue}
                        onValueChange={(value) => setWorkflowCategoryValue(value === 'none' ? '' : value as WorkflowCategory)}
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="请选择流程类型"/>
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={'none'}>未选择</SelectItem>
                            {workflowCategoryOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )}

                {/* 题型 */}
                {questionTypeSummaries !== false && (
                    <Select
                        value={questionTypeSelect}
                        onValueChange={(value) => setQuestionTypeSelect(value === 'none' ? '' : value)}
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择题型"/>
                        </SelectTrigger>
                        <SelectContent>
                            {innerQuestionTypeSummaries.length===0?<SelectItem value={'none'}>无题型数据</SelectItem>:<SelectItem value={'none'}>未选择</SelectItem>}
                            {innerQuestionTypeSummaries.map(qt => (
                                <SelectItem key={qt.code} value={String(qt.code)}>{qt.type_name}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )}

                {/* 学科 */}
                {subjectSummaries !== false && (
                    <Select
                        value={subjectSelect}
                        onValueChange={(value) => setSubjectSelect(value === 'none' ? '' : value)}
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择学科"/>
                        </SelectTrigger>
                        <SelectContent>
                            {innerSubjectSummaries.length===0?<SelectItem value={'none'}>无学科数据</SelectItem>:<SelectItem value={'none'}>未选择</SelectItem>}
                            {innerSubjectSummaries.map(s => (
                                <SelectItem key={s.code} value={String(s.code)}>{s.name}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )}

                {/* 年级 */}
                {gradeLevelSummaries !== false  && (
                    <Select
                        value={gradeLevelSelect}
                        onValueChange={(value) => setGradeLevelSelect(value === 'none' ? '' : value)}
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择年级"/>
                        </SelectTrigger>
                        <SelectContent>
                            {innerGradeLevelSummaries.length===0?<SelectItem value={'none'}>无年级数据</SelectItem>:<SelectItem value={'none'}>未选择</SelectItem>}
                            {innerGradeLevelSummaries.map(g => (
                                <SelectItem key={g.code} value={String(g.code)}>{g.name}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )}

                {/* 租户 */}
                {tenants !== false && (
                    <Select
                        value={tenantSelect}
                        onValueChange={(value) => setTenantSelect(value === 'none' ? '' : value)}
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="选择租户"/>
                        </SelectTrigger>
                        <SelectContent>
                            {innerTenants.length===0?<SelectItem value={'none'}>无租户数据</SelectItem>:<SelectItem value={'none'}>未选择</SelectItem>}
                            {innerTenants.map(t => (
                                <SelectItem key={t.schemaName} value={String(t.schemaName)}>{t.name}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )}
            </div>

            {/* 预览表格 */}
            {preview !== false && (
                preview && preview.length > 0 ? (
                    <div className="rounded-lg border">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>工作流名称</TableHead>
                                    <TableHead>工作流ID</TableHead>
                                    <TableHead>类型</TableHead>
                                    <TableHead>描述</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {preview.map(ws => (
                                    <TableRow key={ws.workflow_id}>
                                        <TableCell>{ws.workflow_name}</TableCell>
                                        <TableCell>{ws.workflow_id}</TableCell>
                                        <TableCell>
                                            {workflowCategoryOptions.find(option => option.value === ws.workflow_type)?.label ?? ws.workflow_type}
                                        </TableCell>
                                        <TableCell>{ws.description}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                ) : (
                    preview && (
                        <div className="text-center py-8 text-gray-500">暂无数据</div>
                    )
                )
            )}
        </div>
    );
};
export default WorkflowSettingFilterPreview;