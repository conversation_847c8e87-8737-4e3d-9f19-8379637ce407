use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct Homework {
    pub id: Uuid,
    pub name: String,
    pub r#type: String,
    pub grade_level: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub status: String,
    pub created_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

///作业统计结果
#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct HomeworkStatistics {
    pub total_homeworks: i32,
    pub completed_homeworks: i32,
    pub in_progress_homeworks: i32,
    pub total_students: i32,
}

#[derive(Debug, Deserialize)]
pub struct CreateHomeworkPayload {
    pub name: String,
    pub r#type: String,
    pub grade_level: String,
    pub description: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub status: String,
    pub created_by: Option<Uuid>,
}

#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct HomeworkSubject {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub subject_id: Option<Uuid>,
    pub paper_template_id: Option<Uuid>,
    pub total_score: Option<f64>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudent {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub class_id: Option<Uuid>,
    pub is_submitted: bool,
    pub submitted_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}
