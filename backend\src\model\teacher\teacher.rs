use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use time::Date;
use uuid::Uuid;

use crate::model::PageParams;

/// 教师信息模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Teacher {
    pub id: Uuid,
    pub tenant_id: Uuid,
    pub user_id: Option<Uuid>,
    pub employee_id: String,                    // 工号
    pub name: String,                           // 姓名
    pub phone: Option<String>,                  // 电话
    pub email: Option<String>,                  // 邮箱
    pub gender: Option<String>,                 // 性别
    pub date_of_birth: Option<NaiveDate>,   // 出生日期
    pub id_card_number: Option<String>,         // 身份证号
    pub highest_education: Option<String>,      // 最高学历
    pub graduation_school: Option<String>,      // 毕业院校
    pub major: Option<String>,                  // 专业
    pub hire_date: Option<NaiveDate>,       // 入职日期
    pub employment_status: String,              // 在职状态
    pub title: Option<String>,                  // 职称
    pub teaching_subjects: Option<Vec<String>>, // 任教学科
    pub homeroom_class_id: Option<i64>,         // 担任班主任的班级ID
    pub grade_level_id: Option<i32>,            // 负责年级ID
    pub subject_group_id: Option<i64>,          // 学科组ID
    pub office_location: Option<String>,        // 办公地点
    pub bio: Option<String>,                    // 个人简介
    pub is_active: bool,                        // 是否活跃
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 教师查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeacherQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub name: Option<String>,              // 按姓名搜索
    pub employee_id: Option<String>,       // 按工号搜索
    pub employment_status: Option<String>, // 按在职状态过滤
    pub teaching_subject: Option<String>,  // 按任教学科过滤
    pub grade_level_id: Option<i32>,       // 按年级过滤
    pub subject_group_id: Option<i64>,     // 按学科组过滤
    pub is_active: Option<bool>,           // 按活跃状态过滤
}

/// 教师详细信息视图对象（包含关联信息）
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TeacherDetailVO {
    pub id: Uuid,
    pub tenant_id: Uuid,
    pub user_id: Option<Uuid>,
    pub employee_id: String,
    pub name: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub gender: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub id_card_number: Option<String>,
    pub highest_education: Option<String>,
    pub graduation_school: Option<String>,
    pub major: Option<String>,
    pub hire_date: Option<NaiveDate>,
    pub employment_status: String,
    pub title: Option<String>,
    pub teaching_subjects: Option<Vec<String>>,
    pub homeroom_class_id: Option<i64>,
    pub homeroom_class_name: Option<String>, // 班主任班级名称
    pub grade_level_id: Option<i32>,
    pub grade_level_name: Option<String>, // 年级名称
    pub subject_group_id: Option<i64>,
    pub subject_group_name: Option<String>, // 学科组名称
    pub office_location: Option<String>,
    pub bio: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 教师列表视图对象（简化信息）
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TeacherListVO {
    pub id: Uuid,
    pub employee_id: String,
    pub name: String,
    pub phone: Option<String>,
    pub employment_status: String,
    pub title: Option<String>,
    pub teaching_subjects: Option<Vec<String>>,
    pub homeroom_class_name: Option<String>,
    pub grade_level_name: Option<String>,
    pub is_active: bool,
}

#[derive(Debug, FromRow, Serialize)]
pub struct TeacherSummary {
    pub id: Uuid,
    pub name: String,
    pub is_active: bool,
}

impl Default for CreateTeacherParams {
    fn default() -> Self {
        Self {
            user_id: Option::from(Uuid::new_v4()),
            employee_id: String::new(),
            name: String::new(),
            phone: None,
            email: None,
            gender: Some("未知".to_string()),
            date_of_birth: None,
            id_card_number: None,
            highest_education: None,
            graduation_school: None,
            major: None,
            hire_date: None,
            employment_status: "在职".to_string(),
            title: None,
            teaching_subjects: None,
            homeroom_class_id: None,
            grade_level_id: None,
            subject_group_id: None,
            office_location: None,
            bio: None,
            is_active: true,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct FindAllParams {
    pub name_like: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageAllTeacherParams {
    pub page_params: PageParams,
    pub employee_id: Option<String>,
    pub name_like: Option<String>,
    pub phone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTeacherParams {
    pub employee_id: String,
    pub is_active: bool,
    pub name: String,
    pub employment_status: String,
    pub user_id: Option<Uuid>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub gender: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub id_card_number: Option<String>,
    pub highest_education: Option<String>,
    pub graduation_school: Option<String>,
    pub major: Option<String>,
    pub hire_date: Option<NaiveDate>,
    pub title: Option<String>,
    pub teaching_subjects: Option<Vec<String>>,
    pub homeroom_class_id: Option<i64>,
    pub grade_level_id: Option<i32>,
    pub subject_group_id: Option<i64>,
    pub office_location: Option<String>,
    pub bio: Option<String>,

}

/// 更新教师请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTeacherParams {
    pub id: Uuid,
    pub user_id: Option<Uuid>,
    pub employee_id: Option<String>,
    pub name: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub gender: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub id_card_number: Option<String>,
    pub highest_education: Option<String>,
    pub graduation_school: Option<String>,
    pub major: Option<String>,
    pub hire_date: Option<NaiveDate>,
    pub employment_status: Option<String>,
    pub title: Option<String>,
    pub teaching_subjects: Option<Vec<String>>,
    pub homeroom_class_id: Option<i64>,
    pub grade_level_id: Option<i32>,
    pub subject_group_id: Option<i64>,
    pub office_location: Option<String>,
    pub bio: Option<String>,
    pub is_active: Option<bool>,
}
