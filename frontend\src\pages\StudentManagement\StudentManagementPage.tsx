import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Download, UserCheck } from 'lucide-react';
import { toast } from 'sonner';

import StudentTable from './components/StudentTable';
import StudentForm from './components/StudentForm';
import StudentDetail from './components/StudentDetail';
import { studentsApi } from '@/services/studentApi';
import { 
  Student, 
  StudentSearchParams, 
  StudentFormData,
  StudentDetail as StudentDetailType,
  DEFAULT_STUDENT_SEARCH,
  STUDENT_STATUS_OPTIONS
} from '@/types/student';

const StudentManagementPage: React.FC = () => {
  // State management
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<StudentSearchParams>(DEFAULT_STUDENT_SEARCH);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  });
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // Detail state
  const [detailOpen, setDetailOpen] = useState(false);
  const [studentDetail, setStudentDetail] = useState<StudentDetailType | undefined>();
  const [detailLoading, setDetailLoading] = useState(false);

  // Load students data
  const loadStudents = async (params?: Partial<StudentSearchParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...searchParams, ...params };
      const response = await studentsApi.getStudents(finalParams);
      
      if (response.success && response.data) {
        setStudents(response.data);
        // Note: Adjust pagination based on actual API response structure
        setPagination({
          current: finalParams.offset ? Math.floor(finalParams.offset / (finalParams.limit || 20)) + 1 : 1,
          pageSize: finalParams.limit || 20,
          total: response.data.length, // This should come from API meta
          totalPages: Math.ceil(response.data.length / (finalParams.limit || 20)),
        });
        setSearchParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load students:', error);
      toast.error('加载学生列表失败');
    } finally {
      setLoading(false);
    }
  };

  // Initialize data
  useEffect(() => {
    loadStudents();
  }, []);

  // Handle search
  const handleSearch = (search: string) => {
    loadStudents({ ...searchParams, name: search, offset: 0 });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof StudentSearchParams, value: any) => {
    const newParams = { ...searchParams, [key]: value, offset: 0 };
    loadStudents(newParams);
  };

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    loadStudents({ ...searchParams, offset: (page - 1) * pageSize, limit: pageSize });
  };

  // Handle create student
  const handleCreateStudent = () => {
    setEditingStudent(undefined);
    setFormOpen(true);
  };

  // Handle edit student
  const handleEditStudent = (student: Student) => {
    setEditingStudent(student);
    setFormOpen(true);
  };

  // Handle view student detail
  const handleViewDetail = async (student: Student) => {
    try {
      setDetailLoading(true);
      setDetailOpen(true);
      const response = await studentsApi.getStudentDetail(student.id);
      if (response.success && response.data) {
        setStudentDetail(response.data);
      }
    } catch (error) {
      console.error('Failed to load student detail:', error);
      toast.error('加载学生详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (data: StudentFormData) => {
    try {
      setFormLoading(true);
      
      if (editingStudent) {
        // Update student
        const response = await studentsApi.updateStudent(editingStudent.id, data);
        if (response.success) {
          toast.success('学生信息更新成功');
          setFormOpen(false);
          loadStudents();
        }
      } else {
        // Create student
        const response = await studentsApi.createStudent(data);
        if (response.success) {
          toast.success('学生创建成功');
          setFormOpen(false);
          loadStudents();
        }
      }
    } catch (error: any) {
      console.error('Failed to save student:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete student
  const handleDeleteStudent = async (id: string) => {
    if (!confirm('确定要删除这个学生吗？此操作不可恢复。')) {
      return;
    }

    try {
      const response = await studentsApi.deleteStudent(id);
      if (response.success) {
        toast.success('学生删除成功');
        loadStudents();
      }
    } catch (error: any) {
      console.error('Failed to delete student:', error);
      toast.error(error.response?.data?.message || '删除失败');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">学生管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的学生信息，包括学生档案、班级关系和学业表现</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => toast.info('导出功能开发中')}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleCreateStudent}>
            <Plus className="w-4 h-4 mr-2" />
            新增学生
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">学生列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>学生列表</CardTitle>
              
              {/* Search and Filter Bar */}
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-[300px] max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="搜索学生姓名或学号..."
                      className="pl-10"
                      value={searchParams.name || ''}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>
                </div>
                
                <Select 
                  value={searchParams.status || 'all'}
                  onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {STUDENT_STATUS_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select 
                  value={searchParams.profile_level || 'all'}
                  onValueChange={(value) => handleFilterChange('profile_level', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger className="w-36">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部等级</SelectItem>
                    <SelectItem value="A+">A+</SelectItem>
                    <SelectItem value="A">A</SelectItem>
                    <SelectItem value="B+">B+</SelectItem>
                    <SelectItem value="B">B</SelectItem>
                    <SelectItem value="C+">C+</SelectItem>
                    <SelectItem value="C">C</SelectItem>
                    <SelectItem value="D+">D+</SelectItem>
                    <SelectItem value="D">D</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            
            <CardContent>
              <StudentTable
                students={students}
                loading={loading}
                onEdit={handleEditStudent}
                onDelete={handleDeleteStudent}
                onViewDetail={handleViewDetail}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: handlePageChange,
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UserCheck className="w-5 h-5 mr-2" />
                学生统计分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                统计分析功能开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Student Form Modal */}
      <StudentForm
        student={editingStudent}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />

      {/* Student Detail Modal */}
      <StudentDetail
        student={studentDetail}
        open={detailOpen}
        onClose={() => setDetailOpen(false)}
        loading={detailLoading}
      />
    </div>
  );
};

export default StudentManagementPage;