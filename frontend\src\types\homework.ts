export interface Homework {
  id: string;
  name: string;
  type: 'single' | 'joint';
  grade_level: string;
  description?: string;
  start_time: string; // ISO 8601 format
  end_time: string;   // ISO 8601 format
  status: 'draft' | 'published' | 'in_progress' | 'completed';
  created_by?: string;
  created_at: string; // ISO 8601 format
  updated_at: string; // ISO 8601 format
}

export interface HomeworkSubject {
  id: string;
  homework_id: string;
  subject_id?: string;
  paper_template_id?: string;
  total_score?: number;
  created_at: string; // ISO 8601 format
}

export interface HomeworkStudent {
  id: string;
  homework_id: string;
  student_id: string;
  class_id?: string;
  is_submitted: boolean;
  submitted_at?: string; // ISO 8601 format
  created_at: string;   // ISO 8601 format
}

export type CreateHomeworkPayload = Omit<Homework, 'id' | 'created_at' | 'updated_at'>;
