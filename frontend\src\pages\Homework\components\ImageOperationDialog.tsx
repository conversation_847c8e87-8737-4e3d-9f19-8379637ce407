import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BatchImage, StudentSearchResult } from "@/services/scanApi";

interface ImageOperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operationType: 'replace' | 'edit_page' | 'edit_student' | 'delete' | 'add' | null;
  currentImage: BatchImage | null;
  uploadFile: File | null;
  uploadPreview: string | null;
  editPageNumber: number;
  editStudentId: string;
  editStudentName: string;
  isMarkingError: boolean;
  studentSearchResults: StudentSearchResult[];
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onPageNumberChange: (value: number) => void;
  onStudentIdChange: (value: string) => void;
  onStudentNameChange: (value: string) => void;
  onMarkingErrorChange: (value: boolean) => void;
  onStudentSelect: (student: StudentSearchResult) => void;
  onSave: () => void;
  onCancel: () => void;
}

const ImageOperationDialog: React.FC<ImageOperationDialogProps> = ({
  open,
  onOpenChange,
  operationType,
  currentImage,
  uploadFile,
  uploadPreview,
  editPageNumber,
  editStudentId,
  editStudentName,
  isMarkingError,
  studentSearchResults,
  onFileUpload,
  onPageNumberChange,
  onStudentIdChange,
  onStudentNameChange,
  onMarkingErrorChange,
  onStudentSelect,
  onSave,
  onCancel,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {operationType === 'replace' && '替换图片'}
            {operationType === 'add' && '新增图片'}
            {operationType === 'edit_page' && '修改页码'}
            {operationType === 'edit_student' && '修改学号'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 上传图片操作 */}
          {(operationType === 'replace' || operationType === 'add') && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="file-upload">选择图片文件</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept="image/*"
                  onChange={onFileUpload}
                  className="mt-1"
                />
              </div>
              
              {uploadPreview && (
                <div className="border rounded-lg p-2">
                  <Label>预览</Label>
                  <img 
                    src={uploadPreview} 
                    alt="上传预览"
                    className="w-full max-h-48 object-contain mt-2"
                  />
                </div>
              )}
            </div>
          )}
          
          {/* 修改页码操作 */}
          {operationType === 'edit_page' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="page-number">页码</Label>
                <Select value={editPageNumber.toString()} onValueChange={(v) => onPageNumberChange(Number(v))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        第 {num} 页
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          
          {/* 修改学号操作 */}
          {operationType === 'edit_student' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="student-search">学号搜索</Label>
                <Input
                  id="student-search"
                  placeholder="输入学号进行搜索..."
                  value={editStudentId}
                  onChange={(e) => onStudentIdChange(e.target.value)}
                />
              </div>
              
              {studentSearchResults.length > 0 && (
                <div className="border rounded-lg max-h-32 overflow-y-auto">
                  {studentSearchResults.map((student) => (
                    <div 
                      key={student.id}
                      className="p-2 hover:bg-muted cursor-pointer border-b last:border-b-0"
                      onClick={() => onStudentSelect(student)}
                    >
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {student.student_id} - {student.class}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="mark-error"
                  checked={isMarkingError}
                  onChange={(e) => onMarkingErrorChange(e.target.checked)}
                />
                <Label htmlFor="mark-error">标注为填涂错误</Label>
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button onClick={onSave}>
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImageOperationDialog;
