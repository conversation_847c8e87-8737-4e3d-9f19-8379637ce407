use crate::controller::administrative_classes::administrative_classes_controller;
use crate::controller::auth::auth_controller;
use crate::controller::classes::classes_controller;
use crate::controller::education_stage::education_stage_controller;
use crate::controller::grade::grade_controller;
use crate::controller::homework::homework_controller;
use crate::controller::role::role_controller;
use crate::controller::student::student_controller;
use crate::controller::subject::subject_controller;
use crate::controller::teacher::teacher_controller;
use crate::controller::teaching_aids::teaching_aids_controller;
use crate::controller::teaching_classes::teaching_classes_controller;
use crate::controller::tenant::tenant_controller;
use crate::controller::user::{identity_controller, parent_controller, user_controller};
use crate::web_server::AppState;
use axum::Router;

pub fn create_protected_routes(app_state: &AppState) -> Router {
    Router::new()
        .nest(
            "/auth",
            auth_controller::create_router().with_state(app_state.auth_integration.auth_service()),
        )
        .nest(
            "/user",
            user_controller::crate_router().with_state(app_state.user_service.clone()),
        )
        .nest(
            "/identity",
            identity_controller::create_router()
                .with_state(app_state.auth_integration.identity_service()),
        )
        .nest(
            "/parent",
            parent_controller::create_router()
                .with_state(app_state.auth_integration.parent_service()),
        )
        .nest(
            "/tenants",
            tenant_controller::create_router()
                .with_state(app_state.auth_integration.tenant_service()),
        )
        .nest(
            "/roles",
            role_controller::create_router().with_state(app_state.role_service.clone()),
        )
        .nest(
            "/subjects",
            subject_controller::create_router().with_state(app_state.subject_service.clone()),
        )
        .nest(
            "/students",
            student_controller::create_router().with_state(app_state.student_service.clone()),
        )
        .nest(
            "/teachers",
            teacher_controller::create_router().with_state(app_state.teacher_service.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/classes",
            classes_controller::create_router().with_state(app_state.classes_service.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/administrativeClasses",
            administrative_classes_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/teachingClasses",
            teaching_classes_controller::create_router().with_state(app_state.clone()),
        )
        .nest(
            "/grades",
            grade_controller::create_router().with_state(app_state.grade_service.clone()),
        )
        .nest(
            "/education-stages",
            education_stage_controller::create_router()
                .with_state(app_state.education_stage_service.clone()),
        )
        .nest(
            "/tenants/{tenant_name}/homework",
            homework_controller::create_router().with_state(app_state.homework_service.clone()),
        )
        .nest(
            "/teaching-aids",
            teaching_aids_controller::create_router()
                .with_state(app_state.teaching_aids_service.clone()),
        )
}

pub fn create_public_routes(app_state: &AppState) -> Router {
    Router::new().nest(
        "/auth",
        auth_controller::create_public_router()
            .with_state(app_state.auth_integration.auth_service()),
    )
}
