use std::collections::{HashMap, HashSet};

use axum::{
    extract::{Path, State},
    routing::{get, post},
    Json, Router,
};
use uuid::{uuid, Uuid};

use crate::{
    model::teaching_classes::teaching_classes::{
        CreateTeachingClassesParams, TeachingClasses, TeachingClassesDetail,
        TeachingClassesStatistics,
    },
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getStatistics", get(get_statistics))
        .route("/getUserClassList", get(get_user_class_list))
        .route("/createClasses", post(create_classes))
}

pub async fn get_statistics(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<TeachingClassesStatistics>, ApiResponse<()>> {
    let user_id = uuid!("********-0000-0000-0000-ffff********");
    match state
        .teaching_classes_service
        .get_statistics(&tenant_name, &user_id)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}

pub async fn get_user_class_list(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<Vec<TeachingClassesDetail>>, ApiResponse<()>> {
    let user_id = uuid!("********-0000-0000-0000-ffff********");
    match state
        .teaching_classes_service
        .get_user_class_list(&tenant_name, &user_id)
        .await
    {
        Ok(data) => {
            //联查额外信息
            let mut teacher_id_set = HashSet::<Uuid>::new();
            let mut grade_level_code_set = HashSet::<String>::new();
            let mut class_id_set = HashSet::<Uuid>::new();
            data.iter().for_each(|c| {
                teacher_id_set.insert(c.teacher_id.clone());
                grade_level_code_set.insert(c.grade_level_code.clone());
                class_id_set.insert(c.id.clone());
            });
            //联查教师信息
            let teacher_list = state
                .teacher_service
                .find_all_by_id_in(&tenant_name, teacher_id_set.into_iter().collect())
                .await
                .map_err(|e| responses::error(e.to_string().as_str(), None))?;
            let mut teacher_id_to_name_map = HashMap::<Uuid, String>::new();
            teacher_list.iter().for_each(|teacher| {
                teacher_id_to_name_map.insert(teacher.id, teacher.name.clone());
            });
            //联查年级信息
            let grade_level_all_list = state
                .grade_service
                .get_all_grades()
                .await
                .map_err(|e| responses::error(e.to_string().as_str(), None))?;
            let mut grade_level_code_to_name_map = HashMap::<String, String>::new();
            grade_level_all_list.iter().for_each(|item| {
                grade_level_code_to_name_map.insert(item.code.clone(), item.name.clone());
            });
            //联查班级人数统计信息
            let class_id_to_student_count_map = state
                .student_service
                .batch_count_by_class(&tenant_name, &class_id_set.into_iter().collect())
                .await
                .map_err(|e| responses::error(e.to_string().as_str(), None))?;
            Ok(responses::success(
                data.into_iter()
                    .map(|classes| {
                        let TeachingClasses {
                            id,
                            class_name,
                            class_code,
                            academic_year,
                            grade_level_code,
                            teacher_id,
                            created_at,
                            updated_at,
                        } = classes;
                        TeachingClassesDetail {
                            id,
                            class_name,
                            class_code,
                            academic_year,
                            grade_level_code: grade_level_code.clone(),
                            teacher_id,
                            created_at,
                            updated_at,
                            teacher_name: teacher_id_to_name_map
                                .get(&teacher_id)
                                .unwrap_or(&"/".to_string())
                                .clone(),
                            grade_level_name: grade_level_code_to_name_map
                                .get(&grade_level_code)
                                .unwrap_or(&"/".to_string())
                                .clone(),
                            total_student: class_id_to_student_count_map
                                .get(&id)
                                .unwrap_or(&0)
                                .clone(),
                        }
                    })
                    .collect(),
                None,
            ))
        }
        Err(msg) => Err(responses::error(&msg, None)),
    }
}

pub async fn create_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<CreateTeachingClassesParams>,
) -> Result<ApiResponse<TeachingClasses>, ApiResponse<()>> {
    let user_id = uuid!("********-0000-0000-0000-ffff********");
    match state
        .teaching_classes_service
        .create_classes(&tenant_name, &user_id, &params)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}
