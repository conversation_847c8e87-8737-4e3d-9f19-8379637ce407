use crate::model::teacher::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UpdateT<PERSON><PERSON>};
use crate::service::teacher::teacher_service::TeacherService;
use crate::utils::api_response::ApiResponse;
use crate::utils::error::AppError;
use crate::middleware::tenant_middleware::TenantExtractor;
use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, patch},
    Router,
};
use uuid::Uuid;

/// 创建教师管理路由
pub fn create_router() -> Router<TeacherService> {
    Router::new()
        .route("/", get(list_teachers).post(create_teacher))
        .route("/{id}", get(get_teacher).put(update_teacher).delete(delete_teacher))
        .route("/{id}/toggle-status", patch(toggle_teacher_status))
}

/// 获取教师列表
#[axum::debug_handler]
async fn list_teachers(
    State(teacher_service): State<TeacherService>,
    TenantExtractor(tenant_context): TenantExtractor,
    Query(params): Query<TeacherQueryParams>,
) -> Result<Json<ApiResponse<crate::model::base::PageResult<crate::model::teacher::TeacherListVO>>>, AppError> {
    let result = teacher_service.get_teachers(&tenant_context.schema_name, params).await?;
    Ok(Json(ApiResponse::success(result, None)))
}

/// 获取单个教师详情
#[axum::debug_handler]
async fn get_teacher(
    State(teacher_service): State<TeacherService>,
    TenantExtractor(tenant_context): TenantExtractor,
    Path(teacher_id): Path<Uuid>,
) -> Result<Json<ApiResponse<crate::model::teacher::TeacherDetailVO>>, AppError> {
    let teacher = teacher_service.get_teacher_by_id(&tenant_context.schema_name, teacher_id).await?
        .ok_or(AppError::NotFound("教师不存在".to_string()))?;
    Ok(Json(ApiResponse::success(teacher, None)))
}

/// 创建教师
#[axum::debug_handler]
async fn create_teacher(
    State(teacher_service): State<TeacherService>,
    TenantExtractor(tenant_context): TenantExtractor,
    Json(payload): Json<CreateTeacher>,
) -> Result<Json<ApiResponse<crate::model::teacher::Teacher>>, AppError> {
    let teacher = teacher_service.create_teacher(&tenant_context.schema_name, tenant_context.tenant_id, payload).await?;
    Ok(Json(ApiResponse::success(teacher, None)))
}

/// 更新教师信息
#[axum::debug_handler]
async fn update_teacher(
    State(teacher_service): State<TeacherService>,
    TenantExtractor(tenant_context): TenantExtractor,
    Path(teacher_id): Path<Uuid>,
    Json(payload): Json<UpdateTeacher>,
) -> Result<Json<ApiResponse<crate::model::teacher::Teacher>>, AppError> {
    let teacher = teacher_service.update_teacher(&tenant_context.schema_name, teacher_id, payload).await?;
    Ok(Json(ApiResponse::success(teacher, None)))
}

/// 删除教师
#[axum::debug_handler]
async fn delete_teacher(
    State(teacher_service): State<TeacherService>,
    TenantExtractor(tenant_context): TenantExtractor,
    Path(teacher_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, AppError> {
    teacher_service.delete_teacher(&tenant_context.schema_name, teacher_id).await?;
    Ok(Json(ApiResponse::success((), None)))
}

#[derive(serde::Deserialize)]
struct ToggleStatusPayload {
    is_active: bool,
}

/// 切换教师状态
#[axum::debug_handler]
async fn toggle_teacher_status(
    State(teacher_service): State<TeacherService>,
    TenantExtractor(tenant_context): TenantExtractor,
    Path(teacher_id): Path<Uuid>,
    Json(payload): Json<ToggleStatusPayload>,
) -> Result<Json<ApiResponse<crate::model::teacher::Teacher>>, AppError> {
    let teacher = teacher_service.toggle_teacher_status(&tenant_context.schema_name, teacher_id, payload.is_active).await?;
    Ok(Json(ApiResponse::success(teacher, None)))
}