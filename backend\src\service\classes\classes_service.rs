use minio::s3::utils::utc_now;
use sqlx::{PgPool, QueryBuilder};
use uuid::Uuid;

use crate::{
    model::{
        base::IdStringParams,
        classes::classes::{
            AddTeacherParams, AdministrativeDetail, ClassType, Classes, ClassesDetail, ClassesSummary,
            ClassesStatistics, CreateClassesParams, HeadTeacher, PageAdministrativeClassesParams,
            PageClassesStudentParams, PageTeachingClassesParams, RemoveTeacherParams,
            TeachingDetail, TeachingTeacher, UpdateClassesParams,
        },
        grade::grade::GradeLevel,
        Student, Teacher,
    },
    utils::api_response::{responses, ApiResponse, PaginatedApiResponse},
};

#[derive(Clone)]
pub struct ClassesService {
    pool: PgPool,
}

/**
 * 班级管理service
 */
impl ClassesService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /**
     * 获取班级简要信息列表（用于下拉选择）
     */
    pub async fn get_classes_summaries(
        &self,
        tenant_name: String,
        is_active: Option<bool>
    ) -> Result<Vec<ClassesSummary>, sqlx::Error> {

        let table_name = format!("{}.administrative_classes", tenant_name);
        let mut query_builder: QueryBuilder<sqlx::Postgres> = QueryBuilder::new(
            format!("SELECT id, class_name as name, code, is_active FROM {}", table_name)
        );

        query_builder.push(" WHERE code IS NOT NULL AND code != ''");

        if let Some(active) = is_active {
            query_builder.push(" AND is_active = ").push_bind(active);
        }

        query_builder.push(" ORDER BY class_name ASC");

        let query = query_builder.build_query_as::<ClassesSummary>();

        query.fetch_all(&self.pool).await
    }
    /**
     * 作者：张瀚
     * 说明：查询班级统计信息
     */
    pub async fn get_statistics(
        &self,
        tenant_id: String,
    ) -> Result<ApiResponse<ClassesStatistics>, ApiResponse<()>> {
        let data = ClassesStatistics {
            total_classes: 5,
            total_teacher: 20,
            total_students: 300,
        };
        Ok(ApiResponse::success(data, None))
    }

    /**
     * 作者：张瀚
     * 说明：获取当前租户可以查看的租户内所有班级列表
     */
    pub async fn page_administrative_classes(
        &self,
        tenant_id: String,
        params: PageAdministrativeClassesParams,
    ) -> Result<ApiResponse<Vec<ClassesDetail>>, ApiResponse<()>> {
        let class_list: Vec<ClassesDetail> = vec![
            ClassesDetail {
                classes: Classes {
                    id: Uuid::new_v4(),
                    name: "1年级1班".to_string(),
                    code: "000".to_string(),
                    class_type: ClassType::Administrative,
                    school_year: 2025,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                administrative_detail: Some(AdministrativeDetail {
                    head_teacher_list: vec![HeadTeacher {
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                teaching_detail: None,
                grade_level_list: Some(vec![GradeLevel {
                    id: Uuid::new_v4(),
                    code: "1".to_string(),
                    name: "1年级".to_string(),
                    description: None,
                    order_level: 1,
                    is_active: true,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                }]),
                student_totals: 50,
            },
            ClassesDetail {
                classes: Classes {
                    id: Uuid::new_v4(),
                    name: "1年级1班".to_string(),
                    code: "000".to_string(),
                    class_type: ClassType::Administrative,
                    school_year: 2025,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                administrative_detail: Some(AdministrativeDetail {
                    head_teacher_list: vec![HeadTeacher {
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                teaching_detail: Some(TeachingDetail {
                    teacher_list: vec![TeachingTeacher {
                        subject_code: "123".to_string(),
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                grade_level_list: Some(vec![GradeLevel {
                    id: Uuid::new_v4(),
                    code: "1".to_string(),
                    name: "1年级".to_string(),
                    description: None,
                    order_level: 1,
                    is_active: true,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                }]),
                student_totals: 50,
            },
        ];
        Ok(ApiResponse::success(class_list, None))
    }
    /**
     * 作者：张瀚
     * 说明：获取当前租户可以查看的租户内所有班级列表
     */
    pub async fn page_teaching_classes(
        &self,
        tenant_id: String,
        params: PageTeachingClassesParams,
    ) -> Result<ApiResponse<Vec<ClassesDetail>>, ApiResponse<()>> {
        let class_list: Vec<ClassesDetail> = vec![
            ClassesDetail {
                classes: Classes {
                    id: Uuid::new_v4(),
                    name: "1年级1班".to_string(),
                    code: "000".to_string(),
                    class_type: ClassType::Administrative,
                    school_year: 2025,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                administrative_detail: Some(AdministrativeDetail {
                    head_teacher_list: vec![HeadTeacher {
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                teaching_detail: None,
                grade_level_list: Some(vec![GradeLevel {
                    id: Uuid::new_v4(),
                    code: "1".to_string(),
                    name: "1年级".to_string(),
                    description: None,
                    order_level: 1,
                    is_active: true,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                }]),
                student_totals: 50,
            },
            ClassesDetail {
                classes: Classes {
                    id: Uuid::new_v4(),
                    name: "1年级1班".to_string(),
                    code: "000".to_string(),
                    class_type: ClassType::Administrative,
                    school_year: 2025,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                administrative_detail: Some(AdministrativeDetail {
                    head_teacher_list: vec![HeadTeacher {
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                teaching_detail: Some(TeachingDetail {
                    teacher_list: vec![TeachingTeacher {
                        subject_code: "123".to_string(),
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                grade_level_list: Some(vec![GradeLevel {
                    id: Uuid::new_v4(),
                    code: "1".to_string(),
                    name: "1年级".to_string(),
                    description: None,
                    order_level: 1,
                    is_active: true,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                }]),
                student_totals: 50,
            },
        ];
        Ok(ApiResponse::success(class_list, None))
    }

    /**
     * 作者：张瀚
     * 说明：创建一个班级
     */
    pub async fn create_classes(
        &self,
        tenant_id: String,
        params: CreateClassesParams,
    ) -> Result<ApiResponse<ClassesDetail>, ApiResponse<()>> {
        let CreateClassesParams {
            name,
            code,
            grade_level_code,
            subject_code,
            class_type,
            school_year,
        } = params;
        Ok(ApiResponse::success(
            ClassesDetail {
                classes: Classes {
                    id: Uuid::new_v4(),
                    name: "1年级1班".to_string(),
                    code: "000".to_string(),
                    class_type: ClassType::Administrative,
                    school_year: 2025,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                administrative_detail: Some(AdministrativeDetail {
                    head_teacher_list: vec![HeadTeacher {
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                teaching_detail: Some(TeachingDetail {
                    teacher_list: vec![TeachingTeacher {
                        subject_code: "123".to_string(),
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "asd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                grade_level_list: Some(vec![GradeLevel {
                    id: Uuid::new_v4(),
                    code: "1".to_string(),
                    name: "1年级".to_string(),
                    description: None,
                    order_level: 1,
                    is_active: true,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                }]),
                student_totals: 50,
            },
            None,
        ))
    }
    /**
     * 作者：张瀚
     * 说明：修改一个班级基本信息
     */
    pub async fn update_classes(
        &self,
        tenant_id: String,
        params: UpdateClassesParams,
    ) -> Result<ApiResponse<ClassesDetail>, ApiResponse<()>> {
        let UpdateClassesParams {
            id,
            name,
            code,
            grade_level_code,
            subject_code,
            class_type,
            school_year,
        } = params;
        Ok(ApiResponse::success(
            ClassesDetail {
                classes: Classes {
                    id: Uuid::new_v4(),
                    name: "1年级1班".to_string(),
                    code: "000".to_string(),
                    class_type: ClassType::Administrative,
                    school_year: 2025,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                administrative_detail: Some(AdministrativeDetail {
                    head_teacher_list: vec![HeadTeacher {
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "dd".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                teaching_detail: Some(TeachingDetail {
                    teacher_list: vec![TeachingTeacher {
                        subject_code: "123".to_string(),
                        teacher: Teacher {
                            id: Uuid::new_v4(),
                            tenant_id: Uuid::new_v4(),
                            user_id: Option::from(Uuid::new_v4()),
                            employee_id: "1".to_string(),
                            name: "李老师".to_string(),
                            phone: None,
                            email: None,
                            gender: None,
                            date_of_birth: None,
                            id_card_number: None,
                            highest_education: None,
                            graduation_school: None,
                            major: None,
                            hire_date: None,
                            employment_status: "".to_string(),
                            title: None,
                            teaching_subjects: None,
                            homeroom_class_id: None,
                            grade_level_id: None,
                            subject_group_id: None,
                            office_location: None,
                            bio: None,
                            is_active: true,
                            created_at: utc_now(),
                            updated_at: utc_now(),
                        },
                        end_time: None,
                    }],
                }),
                grade_level_list: Some(vec![GradeLevel {
                    id: Uuid::new_v4(),
                    code: "1".to_string(),
                    name: "1年级".to_string(),
                    description: None,
                    order_level: 1,
                    is_active: true,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                }]),
                student_totals: 50,
            },
            None,
        ))
    }
    /**
     * 作者：张瀚
     * 说明：删除一个班级
     */
    pub async fn delete_classes(
        &self,
        tenant_id: String,
        params: IdStringParams,
    ) -> Result<ApiResponse<()>, ApiResponse<()>> {
        let IdStringParams { id } = params;
        Ok(responses::success_no_data(None))
    }

    /**
     * 作者：张瀚
     * 说明：查询班级内的学生列表
     */
    pub async fn page_classes_student(
        &self,
        tenant_id: String,
        params: PageClassesStudentParams,
    ) -> Result<PaginatedApiResponse<Student>, ApiResponse<()>> {
        let PageClassesStudentParams { id, pagination } = params;
        Ok(responses::paginated_success(
            vec![
                Student {
                    id: Uuid::new_v4(),
                    student_number: "asd".to_string(),
                    name: "122".to_string(),
                    gender: None,
                    birth_date: None,
                    id_number: None,
                    phone: None,
                    email: None,
                    address: None,
                    guardian_name: None,
                    guardian_phone: None,
                    guardian_relation: None,
                    administrative_class_id: None,
                    grade_level_id: None,
                    user_id: None,
                    enrollment_date: None,
                    status: "ddd".to_string(),
                    profile_level: None,
                    profile_tags: None,
                    notes: None,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                Student {
                    id: Uuid::new_v4(),
                    student_number: "asd".to_string(),
                    name: "122".to_string(),
                    gender: None,
                    birth_date: None,
                    id_number: None,
                    phone: None,
                    email: None,
                    address: None,
                    guardian_name: None,
                    guardian_phone: None,
                    guardian_relation: None,
                    administrative_class_id: None,
                    grade_level_id: None,
                    user_id: None,
                    enrollment_date: None,
                    status: "ddd".to_string(),
                    profile_level: None,
                    profile_tags: None,
                    notes: None,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                Student {
                    id: Uuid::new_v4(),
                    student_number: "asd".to_string(),
                    name: "122".to_string(),
                    gender: None,
                    birth_date: None,
                    id_number: None,
                    phone: None,
                    email: None,
                    address: None,
                    guardian_name: None,
                    guardian_phone: None,
                    guardian_relation: None,
                    administrative_class_id: None,
                    grade_level_id: None,
                    user_id: None,
                    enrollment_date: None,
                    status: "ddd".to_string(),
                    profile_level: None,
                    profile_tags: None,
                    notes: None,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
                Student {
                    id: Uuid::new_v4(),
                    student_number: "asd".to_string(),
                    name: "122".to_string(),
                    gender: None,
                    birth_date: None,
                    id_number: None,
                    phone: None,
                    email: None,
                    address: None,
                    guardian_name: None,
                    guardian_phone: None,
                    guardian_relation: None,
                    administrative_class_id: None,
                    grade_level_id: None,
                    user_id: None,
                    enrollment_date: None,
                    status: "ddd".to_string(),
                    profile_level: None,
                    profile_tags: None,
                    notes: None,
                    created_at: utc_now(),
                    updated_at: utc_now(),
                },
            ],
            pagination.get_page(),
            pagination.get_page_size(),
            20,
            None,
        ))
    }

    /**
     * 作者：张瀚
     * 说明：添加班主任
     */
    pub async fn add_head_teacher(
        &self,
        tenant_id: String,
        params: AddTeacherParams,
    ) -> Result<ApiResponse<()>, ApiResponse<()>> {
        Ok(responses::success_no_data(None))
    }

    /**
     * 作者：张瀚
     * 说明：移除班主任
     */
    pub async fn remove_head_teacher(
        &self,
        tenant_id: String,
        params: RemoveTeacherParams,
    ) -> Result<(), ()> {
        Ok(())
    }
}
