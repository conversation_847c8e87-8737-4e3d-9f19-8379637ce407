use crate::model::textbooks::*;
use crate::utils::error_handler::AppResult;
use anyhow::Result;
use chrono::Utc;
use sqlx::PgPool;
use uuid::Uuid;

#[derive(Debug)]
pub struct TeachingAidsService {
    pool: PgPool,
}

impl TeachingAidsService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_textbook(&self, request: Textbook) -> AppResult<Textbook> {
        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
            "#,
            request.id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            request.created_at,
            request.updated_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(textbook)
    }

    pub async fn get_textbook(&self, id: Uuid) -> Result<Textbook> {
        let textbook =
            sqlx::query_as!(Textbook, "SELECT * FROM public.textbooks WHERE id = $1", id)
                .fetch_optional(&self.pool)
                .await?
                .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn get_textbooks(&self) -> Result<Vec<Textbook>> {
        let textbooks = sqlx::query_as!(Textbook, "SELECT * FROM public.textbooks")
            .fetch_all(&self.pool)
            .await?;

        Ok(textbooks)
    }

    pub async fn update_textbook(&self, id: Uuid, request: Textbook) -> Result<Textbook> {
        let now = Utc::now();

        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            UPDATE public.textbooks
            SET title = $2, subject_id = $3, grade_level_id = $4, publisher = $5, publication_year = $6, isbn = $7, version = $8, status = $9, creator_id = $10, updated_at = $11
            WHERE id = $1
            RETURNING *
            "#,
            id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            now
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn delete_textbook(&self, id: Uuid) -> Result<()> {
        let result = sqlx::query!("DELETE FROM public.textbooks WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        if result.rows_affected() == 0 {
            return Err(anyhow::anyhow!("Textbook not found"));
        }

        Ok(())
    }
}
