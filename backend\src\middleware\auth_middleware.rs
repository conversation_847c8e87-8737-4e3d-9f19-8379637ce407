pub(crate) use crate::utils::jwt::{validate_token, Claims};
use crate::utils::error::AppError;
use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Row};
use uuid::Uuid;
use serde::{Deserialize, Serialize};

/// 用户角色信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRole {
    pub role_id: Uuid,
    pub identity_type: String,
    pub tenant_id: Option<Uuid>,
    pub is_verified: bool,
}

/// 通用认证上下文
#[derive(Debug, Clone)]
pub struct AuthContext {
    pub user_id: Uuid,
    pub username: String,
    pub roles: Vec<UserRole>,

    pub phone_number: String,
    pub created_at: Option<DateTime<Utc>>,
    pub phone_verified: Option<bool>,
    pub is_active: Option<bool>,
}

impl AuthContext {
    /// 检查用户是否具有指定角色
    pub fn has_role(&self, role_type: &str) -> bool {
        self.roles.iter().any(|role| role.identity_type == role_type && role.is_verified)
    }

    /// 检查用户是否为系统管理员
    pub fn is_admin(&self) -> bool {
        self.has_role("admin")
    }

    /// 检查用户是否为教师
    pub fn is_teacher(&self) -> bool {
        self.has_role("teacher")
    }

    /// 检查用户是否为学生
    pub fn is_student(&self) -> bool {
        self.has_role("student")
    }

    /// 检查用户是否具有指定权限
    /// 简化版本：管理员拥有所有权限，其他根据角色判断
    pub fn has_permission(&self, resource: &str, action: &str) -> bool {
        // 系统管理员拥有所有权限
        if self.is_admin() {
            return true;
        }

        // 角色权限映射（简化版本）
        match (resource, action) {
            ("role", "read") => self.is_admin(),
            ("role", "create") => self.is_admin(),
            ("role", "update") => self.is_admin(),
            ("role", "delete") => self.is_admin(),
            ("role", "assign") => self.is_admin(),
            ("permission", "read") => self.is_admin(),
            _ => false,
        }
    }

    /// 获取用户在指定租户中的角色
    pub fn get_roles_for_tenant(&self, tenant_id: Uuid) -> Vec<&UserRole> {
        self.roles.iter()
            .filter(|role| role.tenant_id == Some(tenant_id) && role.is_verified)
            .collect()
    }
}

/// 通用角色认证中间件
/// 验证用户身份并加载角色信息
pub async fn role_auth_middleware(
    State(pool): State<PgPool>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 从Authorization header获取token
    let token = extract_token_from_headers(request.headers())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // 验证JWT token
    let claims = validate_token(&token)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // 获取用户ID
    let user_id = Uuid::parse_str(&claims.sub)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // 获取用户认证上下文
    let auth_context = get_user_auth_context(&pool, user_id).await
        .map_err(|_| StatusCode::FORBIDDEN)?;

    // 将认证上下文添加到请求扩展中
    request.extensions_mut().insert(auth_context);

    Ok(next.run(request).await)
}


/// 从请求头中提取Bearer token
fn extract_token_from_headers(headers: &HeaderMap) -> Option<String> {
    headers
        .get("Authorization")?
        .to_str()
        .ok()?        .strip_prefix("Bearer ")
        .map(|s| s.to_string())
}

/// 获取用户认证上下文（包含所有角色信息）- PRD 6.3.2 compliant
pub async fn get_user_auth_context(
    pool: &PgPool,
    user_id: Uuid,
) -> Result<AuthContext, AppError> {
    // 查询用户基本信息
    let user_record = sqlx::query!(
        r#"
            SELECT u.id, u.phone_number, u.created_at, u.username, u.phone_verified, u.is_active
        FROM public.users u
        WHERE u.id = $1 AND u.is_active = true
        "#,
        user_id
    )
    .fetch_optional(pool)
    .await
    .map_err(AppError::DatabaseError)?;

    let user = user_record.ok_or_else(|| AppError::NotFound("User not found or inactive".to_string()))?;

    // 查询所有活跃租户的schema
    let tenant_schemas = sqlx::query!(
        r#"
        SELECT id as tenant_id, schema_name
        FROM public.tenants
        WHERE status = 'active'
        ORDER BY created_at
        "#
    )
    .fetch_all(pool)
    .await
    .map_err(AppError::DatabaseError)?;

    let mut roles = Vec::new();

    // 遍历所有租户schema，查询用户在每个租户中的身份
    for tenant_record in tenant_schemas {
        let query = format!(
            r#"
            SELECT
                ui.id as identity_id,
                ui.role_id,
                ui.target_type,
                ui.target_id,
                ui.subject,
                r.code as role_code,
                r.name as role_name
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1
            ORDER BY ui.created_at DESC
            "#,
            schema_name = tenant_record.schema_name
        );

        let role_records = sqlx::query(&query)
            .bind(user_id)
            .fetch_all(pool)
            .await
            .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist or query fails

        for record in role_records {
            roles.push(UserRole {
                role_id: record.get("role_id"),
                identity_type: record.get("role_code"), // Use role code as identity_type
                tenant_id: Some(tenant_record.tenant_id),
                is_verified: true, // All roles in tenant schemas are verified
            });
        }
    }

    // Special handling for admin users - check if user is system admin
    let admin_role = sqlx::query!(
        r#"
        SELECT r.id as role_id, r.code as role_code
        FROM public.roles r
        WHERE r.code = 'admin' AND r.is_system = true
        LIMIT 1
        "#
    )
    .fetch_optional(pool)
    .await
    .map_err(AppError::DatabaseError)?;

    // Check if user has system admin role (for backwards compatibility)
    if let Some(admin) = admin_role {
        // Check if username indicates admin (temporary compatibility)
        if user.username == "admin" {
            roles.push(UserRole {
                role_id: admin.role_id,
                identity_type: admin.role_code,
                tenant_id: None, // System admin has no specific tenant
                is_verified: true,
            });
        }
    }

    Ok(AuthContext {
        user_id: user.id,
        username: user.username,
        roles,
        phone_number: user.phone_number,
        created_at: user.created_at,
        phone_verified: user.phone_verified,
        is_active: user.is_active,
    })
}

/// 通用认证上下文提取器（用于 Axum 处理器）
pub struct AuthExtractor(pub AuthContext);

impl<S> axum::extract::FromRequestParts<S> for AuthExtractor
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, &'static str);

    async fn from_request_parts(
        parts: &mut axum::http::request::Parts,
        _state: &S,
    ) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<AuthContext>()
            .cloned()
            .map(AuthExtractor)
            .ok_or((StatusCode::FORBIDDEN, "Auth context not found"))
    }
}


/// 获取当前请求的认证上下文
pub fn get_auth_context(request: &Request) -> Result<&AuthContext, AppError> {
    request
        .extensions()
        .get::<AuthContext>()
        .ok_or_else(|| AppError::Forbidden("Auth context not found".to_string()))
}


/// 通用认证中间件
/// 验证用户身份并加载角色信息，不限制特定角色
pub async fn auth_middleware(
    State(pool): State<PgPool>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 从Authorization header获取token
    let token = extract_token_from_headers(request.headers())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // 验证JWT token
    let claims = validate_token(&token)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // 获取用户ID
    let user_id = Uuid::parse_str(&claims.sub)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // 获取用户认证上下文
    let auth_context = get_user_auth_context(&pool, user_id).await
        .map_err(|_| StatusCode::FORBIDDEN)?;

    // 将认证上下文添加到请求扩展中
    request.extensions_mut().insert(auth_context);

    Ok(next.run(request).await)
}

/// 简化的角色检查中间件
/// 检查用户是否具有指定角色
pub async fn check_role_middleware(
    required_role: String,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 从请求扩展中获取认证上下文
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::FORBIDDEN)?;

    // 检查是否具有所需角色
    if !auth_context.has_role(&required_role) {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

/// 管理员角色检查中间件
pub async fn require_admin_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 从请求扩展中获取认证上下文
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::FORBIDDEN)?;

    // 检查是否为管理员
    if !auth_context.is_admin() {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

/// 教师角色检查中间件
pub async fn require_teacher_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::FORBIDDEN)?;

    if !auth_context.is_teacher() {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

/// 学生角色检查中间件
pub async fn require_student_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::FORBIDDEN)?;

    if !auth_context.is_student() {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

/// 多角色检查中间件
/// 用户只需具有其中一个角色即可通过验证
pub async fn require_any_role_middleware(
    roles: Vec<String>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::FORBIDDEN)?;

    // 检查是否具有任意一个所需角色
    let has_any_role = roles.iter().any(|role| auth_context.has_role(role));

    if !has_any_role {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

/// 租户角色检查中间件
/// 检查用户在特定租户中是否具有指定角色
pub async fn require_tenant_role_middleware(
    tenant_id: Uuid,
    required_role: String,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_context = request
        .extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::FORBIDDEN)?;

    // 检查用户在指定租户中是否具有所需角色
    let has_tenant_role = auth_context
        .get_roles_for_tenant(tenant_id)
        .iter()
        .any(|role| role.identity_type == required_role);

    if !has_tenant_role {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}