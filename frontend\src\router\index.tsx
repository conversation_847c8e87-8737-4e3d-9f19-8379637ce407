
import { createBrowserRouter } from "react-router-dom";
import LoginPage from "@/pages/LoginPage";
import RootLayout from "@/layouts/RootLayout";
import ProtectedRoute from "./ProtectedRoute";
import TeachingAidsPage from "@/pages/TeachingAids";
import ExamManagementPage from "@/pages/ExamManagement";
import GradingCenterPage from "@/pages/GradingCenter";
import { HomeworkManagementPage } from "@/pages/Homework";
import StatisticsPage from "@/pages/Statistics";
import TenantManagementPage from "@/pages/Tenant/TenantManagementPage.tsx";
import HomeworkListPage from "@/pages/Homework/HomeworkListPage";
import HomeworkDetailPage from "@/pages/Homework/HomeworkDetailPage";
import HomeworkForm from "@/pages/Homework/HomeworkForm";
import { RoleManagementPage } from "@/pages/RoleManagement";
import GradeManagementPage from "@/pages/GradeManagement";
import { SubjectManagementPage } from "@/pages/SubjectManagement";
import { StudentManagementPage } from "@/pages/StudentManagement";
import { TeacherManagementPage } from "@/pages/TeacherManagement";
import UserManagementPage from "@/pages/UserManagement";
import TeachingClassesPage from "@/pages/Class/TeachingClassesPage";
import AdministrativeClassesPage from "@/pages/Class/AdministrativeClassesPage";

export const router = createBrowserRouter([
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        element: <ProtectedRoute />,
        children: [
          { index: true, element: <GradingCenterPage /> },
          { path: "teaching-aids", element: <TeachingAidsPage /> },
          { path: "exam-management", element: <ExamManagementPage /> },
          { path: "homework-management", element: <HomeworkManagementPage /> },
          { path: "grading-center", element: <GradingCenterPage /> },
          { path: "statistics", element: <StatisticsPage /> },
          { path: "tenants", element: <TenantManagementPage /> },
          { path: "users", element: <UserManagementPage /> },
          { path: "homeworks", element: <HomeworkListPage /> },
          { path: "homeworks/new", element: <HomeworkForm /> },
          { path: "homeworks/:id", element: <HomeworkDetailPage /> },
          { path: "homeworks/edit/:id", element: <HomeworkForm /> },
          { path: "administrative-classes", element: <AdministrativeClassesPage /> },
          { path: "teaching-classes", element: <TeachingClassesPage /> },
          { path: "roles", element: <RoleManagementPage /> },
          { path: "subjects", element: <SubjectManagementPage /> },
          { path: "grades", element: <GradeManagementPage /> },
          { path: "students", element: <StudentManagementPage /> },
          { path: "teachers", element: <TeacherManagementPage /> },
        ],
      },
    ],
  },
]);
