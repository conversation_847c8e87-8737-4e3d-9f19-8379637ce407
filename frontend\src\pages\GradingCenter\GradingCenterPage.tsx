import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  <PERSON>an, 
  AlertTriangle, 
  CheckCircle2, 
  Clock, 
  Eye,
  Play,
  Pause,
  RefreshCw,
  FileText,
  Settings,
  BarChart3,
  TrendingUp,
  AlertCircle,
  Zap,
  FileImage
} from 'lucide-react';

import { 
  paperScanApi, 
  gradingTaskApi, 
  exceptionApi, 
  monitoringApi, 
  gradingControlApi,
  PaperScan,
  PaperScanStats,
  GradingStats,
  GraderStats,
  StudentException,
  ExceptionStats,
  QualityAlert,
  AIGradingStats
} from '@/services/gradingApi';

const GradingCenterPage: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Data states
  const [scanStats, setScanStats] = useState<PaperScanStats | null>(null);
  const [gradingStats, setGradingStats] = useState<GradingStats | null>(null);
  const [graderStats, setGraderStats] = useState<GraderStats[]>([]);
  const [exceptionStats, setExceptionStats] = useState<ExceptionStats | null>(null);
  const [qualityAlerts, setQualityAlerts] = useState<QualityAlert[]>([]);
  const [aiGradingStats, setAIGradingStats] = useState<AIGradingStats | null>(null);
  
  // Paper scan states
  const [paperScans, setPaperScans] = useState<PaperScan[]>([]);

  // Exception management states
  const [studentExceptions, setStudentExceptions] = useState<StudentException[]>([]);
  const [isExceptionDialogOpen, setIsExceptionDialogOpen] = useState(false);
  const [selectedException, setSelectedException] = useState<StudentException | null>(null);
  const [manualStudentNumber, setManualStudentNumber] = useState('');
  
  // Grading control states
  const [isControlDialogOpen, setIsControlDialogOpen] = useState(false);
  
  // Upload states
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Mock exam ID - in real app, this would come from route params or context
  const examId = '00000000-0000-0000-0000-000000000001';

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [
        scanStatsData,
        gradingStatsData,
        graderStatsData,
        exceptionStatsData,
        qualityAlertsData,
        aiGradingStatsData
      ] = await Promise.all([
        paperScanApi.getScanStats(examId),
        gradingTaskApi.getGradingStats(examId),
        gradingTaskApi.getGraderStats(examId),
        exceptionApi.getExceptionStats(examId),
        monitoringApi.getQualityAlerts(examId),
        gradingTaskApi.getAIGradingStats(examId)
      ]);
      
      setScanStats(scanStatsData);
      setGradingStats(gradingStatsData);
      setGraderStats(graderStatsData);
      setExceptionStats(exceptionStatsData);
      setQualityAlerts(qualityAlertsData);
      setAIGradingStats(aiGradingStatsData);
      setError(null);
    } catch (err) {
      setError('Failed to load grading center data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPaperScans = async () => {
    try {
      const scans = await paperScanApi.getPaperScans(examId);
      setPaperScans(scans);
    } catch (err) {
      setError('Failed to load paper scans');
      console.error('Error loading paper scans:', err);
    }
  };

  const loadStudentExceptions = async () => {
    try {
      const exceptions = await exceptionApi.getStudentExceptions(examId);
      setStudentExceptions(exceptions);
    } catch (err) {
      setError('Failed to load student exceptions');
      console.error('Error loading student exceptions:', err);
    }
  };

  const handleStartGrading = async () => {
    try {
      await gradingControlApi.startGrading(examId);
      await loadInitialData();
      setIsControlDialogOpen(false);
    } catch (err) {
      setError('Failed to start grading');
      console.error('Error starting grading:', err);
    }
  };

  const handlePauseGrading = async () => {
    try {
      await gradingControlApi.pauseGrading(examId, '用户手动暂停');
      await loadInitialData();
      setIsControlDialogOpen(false);
    } catch (err) {
      setError('Failed to pause grading');
      console.error('Error pausing grading:', err);
    }
  };

  const handleResumeGrading = async () => {
    try {
      await gradingControlApi.resumeGrading(examId);
      await loadInitialData();
      setIsControlDialogOpen(false);
    } catch (err) {
      setError('Failed to resume grading');
      console.error('Error resuming grading:', err);
    }
  };

  const handleStartAIGrading = async () => {
    try {
      await gradingTaskApi.startAIGrading(examId);
      await loadInitialData();
    } catch (err) {
      setError('Failed to start AI grading');
      console.error('Error starting AI grading:', err);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      setUploadFiles(Array.from(files));
    }
  };

  const handleUploadConfirm = async () => {
    if (uploadFiles.length === 0) return;
    
    try {
      setUploadProgress(0);
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);
      
      // Mock upload - in real app, this would upload to MinIO
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setIsUploadDialogOpen(false);
      setUploadFiles([]);
      setUploadProgress(0);
      loadInitialData();
    } catch (err) {
      setError('Failed to upload files');
      console.error('Error uploading files:', err);
    }
  };

  const handleManualResolve = () => {
    if (selectedException && manualStudentNumber) {
      handleResolveException(selectedException.id, {
        resolution_method: 'manual_input',
        correct_student_id: manualStudentNumber,
        resolution_notes: `手动输入学号：${manualStudentNumber}`
      });
    }
  };

  const handleResolveException = async (exceptionId: string, resolution: any) => {
    try {
      await exceptionApi.resolveStudentException(exceptionId, resolution);
      await loadStudentExceptions();
      setIsExceptionDialogOpen(false);
      setSelectedException(null);
    } catch (err) {
      setError('Failed to resolve exception');
      console.error('Error resolving exception:', err);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: '待处理', icon: Clock },
      processing: { variant: 'outline' as const, label: '处理中', icon: RefreshCw },
      completed: { variant: 'default' as const, label: '已完成', icon: CheckCircle2 },
      exception: { variant: 'destructive' as const, label: '异常', icon: AlertTriangle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getQualityBadge = (quality: number) => {
    if (quality >= 0.9) return <Badge variant="default">优秀</Badge>;
    if (quality >= 0.7) return <Badge variant="secondary">良好</Badge>;
    if (quality >= 0.5) return <Badge variant="outline">一般</Badge>;
    return <Badge variant="destructive">差</Badge>;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <AlertDialog>
          <AlertCircle className="h-4 w-4" />
          <AlertDialogDescription>{error}</AlertDialogDescription>
        </AlertDialog>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">阅卷中心</h1>
          <p className="text-muted-foreground">智能阅卷系统和流程管理</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadInitialData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                上传试卷
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>上传试卷图片</DialogTitle>
                <DialogDescription>
                  支持JPG、PNG格式，建议单张图片不超过10MB
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="file-upload">选择文件</Label>
                  <Input
                    id="file-upload"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleFileUpload}
                  />
                </div>
                {uploadFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>已选择文件 ({uploadFiles.length})</Label>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {uploadFiles.map((file, index) => (
                        <div key={index} className="text-sm p-2 bg-muted rounded">
                          {file.name} ({(file.size / 1024 / 1024).toFixed(2)}MB)
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {uploadProgress > 0 && (
                  <div className="space-y-2">
                    <Label>上传进度</Label>
                    <Progress value={uploadProgress} />
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsUploadDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleUploadConfirm} disabled={uploadFiles.length === 0}>
                  确认上传
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          <Dialog open={isControlDialogOpen} onOpenChange={setIsControlDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Settings className="h-4 w-4 mr-2" />
                阅卷控制
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>阅卷控制</DialogTitle>
                <DialogDescription>
                  管理阅卷流程的启动、暂停和恢复
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Button onClick={handleStartGrading} disabled={loading}>
                    <Play className="h-4 w-4 mr-2" />
                    {loading ? 'Starting...' : '开始阅卷'}
                  </Button>
                  <Button onClick={handlePauseGrading} disabled={loading}>
                    <Pause className="h-4 w-4 mr-2" />
                    {loading ? 'Pausing...' : '暂停阅卷'}
                  </Button>
                  <Button onClick={handleResumeGrading} disabled={loading}>
                    <Play className="h-4 w-4 mr-2" />
                    {loading ? 'Resuming...' : '恢复阅卷'}
                  </Button>
                  <Button onClick={handleStartAIGrading} disabled={loading}>
                    <Zap className="h-4 w-4 mr-2" />
                    {loading ? 'Starting AI...' : '启动AI阅卷'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="scanning">试卷扫描</TabsTrigger>
          <TabsTrigger value="grading">智能阅卷</TabsTrigger>
          <TabsTrigger value="exceptions">异常处理</TabsTrigger>
          <TabsTrigger value="monitoring">质量监控</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">扫描进度</CardTitle>
                <Scan className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{scanStats?.total_scanned || 0}</div>
                <div className="text-xs text-muted-foreground">
                  异常: {scanStats?.exception_count || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">阅卷进度</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{gradingStats?.completed_papers || 0}</div>
                <div className="text-xs text-muted-foreground">
                  总计: {gradingStats?.total_papers || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI阅卷率</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {((gradingStats?.ai_grading_ratio || 0) * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  置信度: {((aiGradingStats?.average_confidence || 0) * 100).toFixed(1)}%
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">异常处理</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{exceptionStats?.pending_exceptions || 0}</div>
                <div className="text-xs text-muted-foreground">
                  总计: {exceptionStats?.total_exceptions || 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Progress Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>阅卷进度</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>总体进度</span>
                      <span>{((gradingStats?.grading_progress || 0) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(gradingStats?.grading_progress || 0) * 100} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>人工复核</span>
                      <span>{((gradingStats?.manual_review_ratio || 0) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(gradingStats?.manual_review_ratio || 0) * 100} />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>质量告警</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {qualityAlerts.slice(0, 3).map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm">{alert.message}</span>
                      </div>
                      <Badge variant={alert.severity === 'high' ? 'destructive' : 'secondary'}>
                        {alert.severity}
                      </Badge>
                    </div>
                  ))}
                  {qualityAlerts.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      暂无质量告警
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="scanning" className="space-y-6">
          {/* Scan Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已扫描</CardTitle>
                <FileImage className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{scanStats?.total_scanned || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">处理中</CardTitle>
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{scanStats?.pending_processing || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">处理完成</CardTitle>
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{scanStats?.processing_complete || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">扫描异常</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{scanStats?.exception_count || 0}</div>
              </CardContent>
            </Card>
          </div>

          {/* Scan List */}
          <Card>
            <CardHeader>
              <CardTitle>扫描记录</CardTitle>
              <CardDescription>
                显示试卷扫描的详细记录和状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={loadPaperScans} className="mb-4">
                加载扫描记录
              </Button>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>学生ID</TableHead>
                    <TableHead>扫描时间</TableHead>
                    <TableHead>图片数量</TableHead>
                    <TableHead>扫描质量</TableHead>
                    <TableHead>扫描设备</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paperScans.map((scan) => (
                    <TableRow key={scan.id}>
                      <TableCell>{scan.student_id}</TableCell>
                      <TableCell>{new Date(scan.scan_time).toLocaleString()}</TableCell>
                      <TableCell>{scan.paper_images.length}</TableCell>
                      <TableCell>{getQualityBadge(scan.scan_quality)}</TableCell>
                      <TableCell>{scan.scanner_device}</TableCell>
                      <TableCell>{getStatusBadge(scan.scan_status)}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="grading" className="space-y-6">
          {/* Grading Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总试卷数</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{gradingStats?.total_papers || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已完成</CardTitle>
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{gradingStats?.completed_papers || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">平均分</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{(gradingStats?.average_score || 0).toFixed(1)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI成功率</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{((aiGradingStats?.success_rate || 0) * 100).toFixed(1)}%</div>
              </CardContent>
            </Card>
          </div>

          {/* Grader Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>阅卷员统计</CardTitle>
              <CardDescription>
                显示各阅卷员的工作量和评分情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>阅卷员</TableHead>
                    <TableHead>已分配</TableHead>
                    <TableHead>已完成</TableHead>
                    <TableHead>待处理</TableHead>
                    <TableHead>平均分</TableHead>
                    <TableHead>一致性</TableHead>
                    <TableHead>效率</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {graderStats.map((grader) => (
                    <TableRow key={grader.grader_id}>
                      <TableCell className="font-medium">{grader.grader_name}</TableCell>
                      <TableCell>{grader.assigned_count}</TableCell>
                      <TableCell>{grader.completed_count}</TableCell>
                      <TableCell>{grader.pending_count}</TableCell>
                      <TableCell>{grader.average_score.toFixed(1)}</TableCell>
                      <TableCell>{(grader.consistency_score * 100).toFixed(1)}%</TableCell>
                      <TableCell>{(grader.efficiency_score * 100).toFixed(1)}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exceptions" className="space-y-6">
          {/* Exception Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总异常数</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{exceptionStats?.total_exceptions || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已解决</CardTitle>
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{exceptionStats?.resolved_exceptions || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">待处理</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{exceptionStats?.pending_exceptions || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">解决率</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{((exceptionStats?.resolution_rate || 0) * 100).toFixed(1)}%</div>
              </CardContent>
            </Card>
          </div>

          {/* Exception List */}
          <Card>
            <CardHeader>
              <CardTitle>学号异常处理</CardTitle>
              <CardDescription>
                处理学号识别异常和学生信息匹配问题
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={loadStudentExceptions} className="mb-4">
                加载异常记录
              </Button>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>学号</TableHead>
                    <TableHead>异常类型</TableHead>
                    <TableHead>异常原因</TableHead>
                    <TableHead>可能学生</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentExceptions.map((exception) => (
                    <TableRow key={exception.id}>
                      <TableCell className="font-medium">{exception.student_number}</TableCell>
                      <TableCell>{exception.exception_type}</TableCell>
                      <TableCell>{exception.exception_reason}</TableCell>
                      <TableCell>{exception.possible_students.length}</TableCell>
                      <TableCell>
                        {exception.resolved ? (
                          <Badge variant="default">已解决</Badge>
                        ) : (
                          <Badge variant="secondary">待处理</Badge>
                        )}
                      </TableCell>
                      <TableCell>{new Date(exception.created_at).toLocaleString()}</TableCell>
                      <TableCell>
                        {!exception.resolved && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedException(exception);
                              setIsExceptionDialogOpen(true);
                            }}
                          >
                            处理
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-6">
          {/* Quality Alerts */}
          <Card>
            <CardHeader>
              <CardTitle>质量告警</CardTitle>
              <CardDescription>
                显示阅卷过程中的质量问题和告警信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {qualityAlerts.map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-4 border rounded">
                    <div className="flex items-center gap-4">
                      <AlertTriangle className={`h-5 w-5 ${
                        alert.severity === 'high' ? 'text-red-500' : 
                        alert.severity === 'medium' ? 'text-yellow-500' : 'text-blue-500'
                      }`} />
                      <div>
                        <div className="font-medium">{alert.message}</div>
                        <div className="text-sm text-muted-foreground">
                          {alert.alert_type} · {new Date(alert.created_at).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={alert.severity === 'high' ? 'destructive' : 'secondary'}>
                        {alert.severity}
                      </Badge>
                      {alert.status === 'active' && (
                        <Button variant="outline" size="sm">
                          处理
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
                {qualityAlerts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    暂无质量告警
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Exception Resolution Dialog */}
      <Dialog open={isExceptionDialogOpen} onOpenChange={setIsExceptionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>处理学号异常</DialogTitle>
            <DialogDescription>
              为学号 {selectedException?.student_number} 选择正确的学生
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>可能的学生</Label>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {selectedException?.possible_students.map((student, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {student.student_number} · {student.class_name}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleResolveException(selectedException.id, {
                        resolution_method: 'manual_bind',
                        correct_student_id: student.id,
                        resolution_notes: `手动绑定到学生：${student.name}`
                      })}
                    >
                      选择
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            <Separator />
            <div className="space-y-2">
              <Label>手动输入学号</Label>
              <Input placeholder="请输入正确的学号" value={manualStudentNumber} onChange={(e) => setManualStudentNumber(e.target.value)} />
              <Button variant="outline" size="sm" onClick={handleManualResolve}>
                确认绑定
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GradingCenterPage;
