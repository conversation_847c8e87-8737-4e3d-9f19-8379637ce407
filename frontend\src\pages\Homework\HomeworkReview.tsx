import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  FileText,
  TrendingUp,
  Zap,
  CheckCircle2,
  Play,
  Square,
  Info,
  ChevronRight,
} from "lucide-react";
import { GradingStats, AIGradingStats } from "@/services/gradingApi";
import { useParams, useNavigate } from 'react-router-dom';

// 题目相关类型定义
interface Question {
  id: string;
  questionNumber: string;
  questionType: "objective" | "subjective";
  title: string;
  content: string;
  type: string; // 选择题、填空题、阅读理解等
  gradingModel: string; // 语义相等、语文默写等
  totalScore: number;
  averageScore: number;
  gradingStatus: "not_started" | "in_progress" | "completed";
  completionRate: number;
  reviewRate: number; // 核查率
  pendingReviewCount: number; // 待核查数量
  answer: string; // 评分标准
  knowledgePoints: string[];
}

// 模拟数据
const mockQuestions: Question[] = [
  {
    id: "1",
    questionNumber: "1",
    questionType: "objective",
    title: "第1题",
    content: "下列哪个选项是正确的？",
    type: "选择题",
    gradingModel: "语义相等",
    totalScore: 5,
    averageScore: 4.2,
    gradingStatus: "completed",
    completionRate: 95,
    reviewRate: 0, // 客观题不需要核查
    pendingReviewCount: 0,
    answer: "A选项是正确答案，因为...",
    knowledgePoints: ["基础概念", "逻辑推理"],
  },
  {
    id: "2",
    questionNumber: "2",
    questionType: "objective",
    title: "第2题",
    content: "填空题：请填入正确答案",
    type: "填空题",
    gradingModel: "精确匹配",
    totalScore: 3,
    averageScore: 2.8,
    gradingStatus: "in_progress",
    completionRate: 78,
    reviewRate: 0, // 客观题不需要核查
    pendingReviewCount: 0,
    answer: "标准答案：xxx",
    knowledgePoints: ["基础知识"],
  },
  {
    id: "3",
    questionNumber: "3",
    questionType: "subjective",
    title: "第3题",
    content: "阅读理解题：请根据文章内容回答问题",
    type: "阅读理解",
    gradingModel: "语义理解",
    totalScore: 10,
    averageScore: 7.5,
    gradingStatus: "not_started",
    completionRate: 0,
    reviewRate: 0,
    pendingReviewCount: 45,
    answer: "参考答案：根据文章第二段可知...",
    knowledgePoints: ["阅读理解", "文本分析"],
  },
  {
    id: "4",
    questionNumber: "4",
    questionType: "subjective",
    title: "第4题",
    content: '作文题：请以"我的梦想"为题写一篇作文',
    type: "作文题",
    gradingModel: "语文作文",
    totalScore: 20,
    averageScore: 16.2,
    gradingStatus: "completed",
    completionRate: 88,
    reviewRate: 71,
    pendingReviewCount: 13,
    answer: "评分标准：内容20%，语言30%，结构25%，创意25%",
    knowledgePoints: ["写作能力", "语言表达"],
  },
];

const HomeworkReview: React.FC = () => {
  const navigate = useNavigate();
  const [gradingStats] = useState<GradingStats | null>(null);
  const [aiGradingStats] = useState<AIGradingStats | null>(null);

  // 页面状态
  const [currentView, setCurrentView] = useState<
    "overview" | "question_review"
  >("overview");
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(
    null
  );
  const [expandedQuestion, setExpandedQuestion] = useState<string | null>(null);

  useEffect(() => {}, []);

  // 处理题目卡片点击 - 只有主观题才能跳转核查页面
  const handleQuestionClick = (question: Question) => {
    if (question.questionType === "subjective") {
      setSelectedQuestion(question);
      // setCurrentView("question_review");
      navigate(`/homework-review/${question.id}`);
    }
  };

  // 处理返回概览
  const handleBackToOverview = () => {
    setCurrentView("overview");
    setSelectedQuestion(null);
  };

  // 处理开始/停止阅卷
  const handleGradingAction = (type: "start" | "stop", question: Question) => {
    console.log(`${type === "start" ? "开始" : "停止"}阅卷:`, question.title);
    // 这里调用API
  };

  // 切换题目详情展开状态
  const toggleQuestionDetail = (questionId: string) => {
    setExpandedQuestion(expandedQuestion === questionId ? null : questionId);
  };

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总试卷数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {gradingStats?.total_papers || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {gradingStats?.completed_papers || 0}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(gradingStats?.average_score || 0).toFixed(1)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI成功率</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {((aiGradingStats?.success_rate || 0) * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 客观题部分 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">客观题</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {mockQuestions
              .filter((q) => q.questionType === "objective")
              .map((question) => (
                <div key={question.id}>
                  <Popover>
                    <Card className="cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-200">
                      <CardContent className="p-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h2 className="text-xl font-bold">
                              {question.title}
                            </h2>
                            <Badge
                              variant={
                                question.gradingStatus === "completed"
                                  ? "default"
                                  : question.gradingStatus === "in_progress"
                                  ? "secondary"
                                  : "outline"
                              }
                              className="text-xs"
                            >
                              {question.gradingStatus === "completed"
                                ? "阅卷完成"
                                : question.gradingStatus === "in_progress"
                                ? "进行中"
                                : "未开始"}
                            </Badge>
                          </div>
                          <div className="space-y-1 flex justify-between items-center text-sm text-muted-foreground">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">
                                平均分:
                              </span>
                              <span className="font-medium">
                                {question.averageScore}/{question.totalScore}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">
                                阅卷进度:
                              </span>
                              <span className="font-medium">
                                {question.completionRate}%
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-6 text-xs flex-1"
                              // onClick={(e) => {
                              //   e.stopPropagation();
                              //   handleGradingAction(question.gradingStatus === 'not_started' ? 'start' : 'stop', question);
                              // }}
                            >
                              {question.gradingStatus === "not_started" ? (
                                <>
                                  <Play className="h-2 w-2 mr-1" />
                                  开始阅卷
                                </>
                              ) : (
                                <>
                                  <Square className="h-2 w-2 mr-1" />
                                  停止阅卷
                                </>
                              )}
                            </Button>
                            <PopoverTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleQuestionDetail(question.id);
                                }}
                              >
                                <Info className="h-4 w-4" />
                              </Button>
                            </PopoverTrigger>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <PopoverContent className="w-80">
                      <div className="space-y-3">
                        <div className="text-sm space-y-2">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              题目类型:
                            </span>
                            <span className="font-medium">{question.type}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              阅卷模型:
                            </span>
                            <span className="font-medium">
                              {question.gradingModel}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-2">原题:</div>
                          <div className="text-sm p-2 bg-muted rounded">
                            {question.content}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-2">答案:</div>
                          <div className="text-sm p-2 bg-muted rounded">
                            {question.answer}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-2">
                            知识点:
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {question.knowledgePoints.map((point, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="text-xs"
                              >
                                {point}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* 主观题部分 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">主观题</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {mockQuestions
              .filter((q) => q.questionType === "subjective")
              .map((question) => (
                <div key={question.id}>
                  <Popover>
                    <Card
                      className="cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-200"
                      onClick={() => handleQuestionClick(question)}
                    >
                      <CardContent className="p-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h2 className="text-xl font-bold">
                              {question.title}
                            </h2>
                            <Badge
                              variant={
                                question.gradingStatus === "completed"
                                  ? "default"
                                  : question.gradingStatus === "in_progress"
                                  ? "secondary"
                                  : "outline"
                              }
                              className="text-xs"
                            >
                              {question.gradingStatus === "completed"
                                ? "阅卷完成"
                                : question.gradingStatus === "in_progress"
                                ? "进行中"
                                : "未开始"}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground space-y-1 flex flex-wrap justify-between items-center">
                            <div className="w-1/2">
                              <span>平均分:</span>
                              <span>
                                {" "}
                                {question.averageScore}/{question.totalScore}
                              </span>
                            </div>
                            <div className="">
                              <span>阅卷进度:</span>
                              <span> {question.completionRate}%</span>
                            </div>
                            <div className="">
                              <span>待核查:</span>
                              <span className="text-red-600 font-medium">
                                {" "}
                                {question.pendingReviewCount}
                              </span>
                            </div>
                            <div className="">
                              <span>核查率:</span>
                              <span> {question.reviewRate}%</span>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-6 text-xs flex-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleGradingAction(
                                  question.gradingStatus === "not_started"
                                    ? "start"
                                    : "stop",
                                  question
                                );
                              }}
                            >
                              {question.gradingStatus === "not_started" ? (
                                <>
                                  <Play className="h-2 w-2 mr-1" />
                                  开始阅卷
                                </>
                              ) : (
                                <>
                                  <Square className="h-2 w-2 mr-1" />
                                  暂停阅卷
                                </>
                              )}
                            </Button>
                            <PopoverTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleQuestionDetail(question.id);
                                }}
                              >
                                <Info className="h-4 w-4" />
                              </Button>
                            </PopoverTrigger>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <PopoverContent className="w-80">
                      <div className="space-y-2 text-xs">
                        <div>
                          <span className="font-medium">原题:</span>
                          <div className="mt-1 p-2 bg-muted rounded text-xs">
                            {question.content}
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <span className="font-medium">题目类型:</span>{" "}
                            {question.type}
                          </div>
                          <div>
                            <span className="font-medium">阅卷模型:</span>{" "}
                            {question.gradingModel}
                          </div>
                        </div>
                        <div>
                          <span className="font-medium">答案:</span>
                          <div className="mt-1 p-2 bg-muted rounded text-xs">
                            {question.answer}
                          </div>
                        </div>
                        <div>
                          <span className="font-medium">知识点:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {question.knowledgePoints.map((point, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="text-xs"
                              >
                                {point}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomeworkReview;
