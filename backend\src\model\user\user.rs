use serde::Serialize;
use sqlx::postgres::PgRow;
use sqlx::Row;
use uuid::Uuid;
use crate::model::tenant::tenant::UserIdentitySelectVO;

pub struct UserRecord {
   pub id: Uuid,
   pub username: String,
   pub password: String,
}

// 实现从行数据到结构体的转换
impl sqlx::FromRow<'_, PgRow> for UserRecord {
    fn from_row(row: &PgRow) -> sqlx::Result<Self> {
        Ok(Self {
            id: row.get("id"),
            username: row.get("username"),
            password: row.get("password_hash"),
        })
    }
}

#[derive(Serialize)]
pub struct UserIdentityLoginVO {
    pub token: String,
    pub user_identities: Vec<UserIdentitySelectVO>,
}

#[derive(Serialize)]
pub struct UserInfo{
    pub id: Uuid,
    pub username: String,
}