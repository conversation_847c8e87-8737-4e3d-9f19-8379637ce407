use crate::model::user::auth::*;
use crate::service::auth::auth_service::AuthService;
use axum::{
    extract::State,
    http::{HeaderMap, StatusCode},
    response::Json,
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use axum::response::IntoResponse;
use tracing::{error, info};
use crate::middleware::auth_middleware::AuthExtractor;
use crate::utils::api_response::{responses, ApiResponse};

pub fn create_router() -> Router<Arc<AuthService>> {
    Router::new()
        .route("/send-verification-code", post(send_verification_code))
        .route("/register", post(register))
        .route("/refresh", post(refresh_token))
        .route("/logout", post(logout))
        .route("/me", get(get_current_user))
}

pub fn create_public_router() -> Router<Arc<AuthService>> {
    Router::new().route("/login", post(login))
}

async fn send_verification_code(
    State(auth_service): State<Arc<AuthService>>,
    <PERSON><PERSON>(request): Json<SendVerificationCodeRequest>,
) -> Result<Json<SendVerificationCodeResponse>, (StatusCode, Json<ErrorResponse>)> {
    match auth_service
        .send_verification_code(&request.phone_number, &request.code_type)
        .await
    {
        Ok(response) => {
            info!(
                "Verification code sent successfully to {}",
                mask_phone(&request.phone_number)
            );
            Ok(Json(response))
        }
        Err(e) => {
            error!("Failed to send verification code: {:?}", e);
            let error_response = ErrorResponse {
                success: false,
                message: format!("Failed to send verification code: {}", e),
                error_code: get_error_code(&e),
            };
            Ok(Json(SendVerificationCodeResponse {
                success: false,
                message: error_response.message.clone(),
                data: VerificationCodeData {
                    expires_in: 0,
                    can_resend_after: 60,
                },
            }))
        }
    }
}

async fn register(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<RegisterRequest>,
) -> Result<Json<RegisterResponse>, (StatusCode, Json<ErrorResponse>)> {
    match auth_service.register(request).await {
        Ok(response) => {
            info!("User registered successfully");
            Ok(Json(response))
        }
        Err(e) => {
            error!("Registration failed: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn login(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
    Json(request): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Extract IP address and user agent for security logging
    let ip_address = extract_ip_address(&headers);
    let user_agent = extract_user_agent(&headers);

    // Log the login attempt with appropriate identifier
    let identifier = match (&request.username, &request.phone_number) {
        (Some(username), _) => format!("username: {}", username),
        (None, Some(phone)) => format!("phone: {}", mask_phone(phone)),
        (None, None) => "unknown identifier".to_string(),
    };

    info!(
        "Login attempt with {} from IP: {:?}, User-Agent: {:?}",
        identifier, ip_address, user_agent
    );

    match auth_service.login(request).await {
        Ok(response) => {
            info!("User logged in successfully");
            Ok(Json(response))
        }
        Err(e) => {
            error!("Login failed: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn refresh_token(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<RefreshTokenRequest>,
) -> Result<Json<RefreshTokenResponse>, (StatusCode, Json<ErrorResponse>)> {
    match auth_service.refresh_token(&request.refresh_token).await {
        Ok((access_token, refresh_token)) => {
            info!("Token refreshed successfully");
            Ok(Json(RefreshTokenResponse {
                success: true,
                message: "Token refreshed successfully".to_string(),
                data: RefreshTokenData {
                    access_token,
                    refresh_token,
                    expires_in: 3600,
                },
            }))
        }
        Err(e) => {
            error!("Token refresh failed: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn logout(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
) -> Result<Json<LogoutResponse>, (StatusCode, Json<ErrorResponse>)> {
    let token = extract_bearer_token(&headers).ok_or_else(|| {
        (
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                success: false,
                message: "Missing authorization token".to_string(),
                error_code: "MISSING_TOKEN".to_string(),
            }),
        )
    })?;

    match auth_service.logout(&token).await {
        Ok(_) => {
            info!("User logged out successfully");
            Ok(Json(LogoutResponse {
                success: true,
                message: "Logged out successfully".to_string(),
            }))
        }
        Err(e) => {
            error!("Logout failed: {:?}", e);
            let (status, error_response) = map_auth_error(e);
            Err((status, Json(error_response)))
        }
    }
}

async fn get_current_user(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
) -> Result<Json<CurrentUserResponse>, (StatusCode, Json<ErrorResponse>)> {
    let token = extract_bearer_token(&headers).ok_or_else(|| {
        (
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                success: false,
                message: "Missing authorization token".to_string(),
                error_code: "MISSING_TOKEN".to_string(),
            }),
        )
    })?;

    // TODO: Implement get_current_user in AuthService
    // For now, return a placeholder response
    Ok(Json(CurrentUserResponse {
        success: true,
        data: CurrentUserData {
            user_id: uuid::Uuid::new_v4(),
            username: "user123".to_string(),
            phone_number: "+86138****1234".to_string(),
            phone_verified: true,
            current_identity: None,
            available_identities: vec![],
        },
    }))
}

// Helper functions
fn extract_bearer_token(headers: &HeaderMap) -> Option<String> {
    headers
        .get("authorization")
        .and_then(|value| value.to_str().ok())
        .and_then(|auth_header| {
            if auth_header.starts_with("Bearer ") {
                Some(auth_header[7..].to_string())
            } else {
                None
            }
        })
}

fn extract_ip_address(headers: &HeaderMap) -> Option<String> {
    headers
        .get("x-forwarded-for")
        .or_else(|| headers.get("x-real-ip"))
        .and_then(|value| value.to_str().ok())
        .map(|s| s.split(',').next().unwrap_or(s).trim().to_string())
}

fn extract_user_agent(headers: &HeaderMap) -> Option<String> {
    headers
        .get("user-agent")
        .and_then(|value| value.to_str().ok())
        .map(|s| s.to_string())
}

fn mask_phone(phone: &str) -> String {
    if phone.len() > 7 {
        let start = &phone[..3];
        let end = &phone[phone.len() - 4..];
        format!("{}****{}", start, end)
    } else {
        "***".to_string()
    }
}

fn map_auth_error(error: AuthError) -> (StatusCode, ErrorResponse) {
    let (status, message, code) = match error {
        AuthError::InvalidPhoneNumber => (
            StatusCode::BAD_REQUEST,
            "Invalid phone number format".to_string(),
            "INVALID_PHONE".to_string(),
        ),
        AuthError::InvalidVerificationCode => (
            StatusCode::BAD_REQUEST,
            "Invalid verification code".to_string(),
            "INVALID_CODE".to_string(),
        ),
        AuthError::VerificationCodeExpired => (
            StatusCode::BAD_REQUEST,
            "Verification code has expired".to_string(),
            "CODE_EXPIRED".to_string(),
        ),
        AuthError::TooManyAttempts => (
            StatusCode::TOO_MANY_REQUESTS,
            "Too many attempts, please try again later".to_string(),
            "TOO_MANY_ATTEMPTS".to_string(),
        ),
        AuthError::UserNotFound => (
            StatusCode::NOT_FOUND,
            "User not found".to_string(),
            "USER_NOT_FOUND".to_string(),
        ),
        AuthError::InvalidCredentials => (
            StatusCode::UNAUTHORIZED,
            "Invalid credentials".to_string(),
            "INVALID_CREDENTIALS".to_string(),
        ),
        AuthError::AccountLocked => (
            StatusCode::LOCKED,
            "Account is temporarily locked".to_string(),
            "ACCOUNT_LOCKED".to_string(),
        ),
        AuthError::PhoneAlreadyRegistered => (
            StatusCode::CONFLICT,
            "Phone number is already registered".to_string(),
            "PHONE_EXISTS".to_string(),
        ),
        AuthError::DatabaseError(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Internal server error".to_string(),
            "DATABASE_ERROR".to_string(),
        ),
        AuthError::SmsServiceError(_) => (
            StatusCode::SERVICE_UNAVAILABLE,
            "SMS service temporarily unavailable".to_string(),
            "SMS_SERVICE_ERROR".to_string(),
        ),
        AuthError::JwtError(_) => (
            StatusCode::UNAUTHORIZED,
            "Invalid or expired token".to_string(),
            "INVALID_TOKEN".to_string(),
        ),
        _ => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Internal server error".to_string(),
            "INTERNAL_ERROR".to_string(),
        ),
    };

    (
        status,
        ErrorResponse {
            success: false,
            message,
            error_code: code,
        },
    )
}

fn get_error_code(error: &AuthError) -> String {
    match error {
        AuthError::InvalidPhoneNumber => "INVALID_PHONE".to_string(),
        AuthError::InvalidVerificationCode => "INVALID_CODE".to_string(),
        AuthError::VerificationCodeExpired => "CODE_EXPIRED".to_string(),
        AuthError::TooManyAttempts => "TOO_MANY_ATTEMPTS".to_string(),
        AuthError::UserNotFound => "USER_NOT_FOUND".to_string(),
        AuthError::InvalidCredentials => "INVALID_CREDENTIALS".to_string(),
        AuthError::AccountLocked => "ACCOUNT_LOCKED".to_string(),
        AuthError::PhoneAlreadyRegistered => "PHONE_EXISTS".to_string(),
        AuthError::DatabaseError(_) => "DATABASE_ERROR".to_string(),
        AuthError::SmsServiceError(_) => "SMS_SERVICE_ERROR".to_string(),
        AuthError::JwtError(_) => "INVALID_TOKEN".to_string(),
        _ => "INTERNAL_ERROR".to_string(),
    }
}

// Additional DTOs for the controller
#[derive(serde::Deserialize)]
struct RefreshTokenRequest {
    refresh_token: String,
}

#[derive(serde::Serialize)]
struct RefreshTokenResponse {
    success: bool,
    message: String,
    data: RefreshTokenData,
}

#[derive(serde::Serialize)]
struct RefreshTokenData {
    access_token: String,
    refresh_token: String,
    expires_in: i64,
}

#[derive(serde::Serialize)]
struct LogoutResponse {
    success: bool,
    message: String,
}

#[derive(serde::Serialize)]
struct CurrentUserResponse {
    success: bool,
    data: CurrentUserData,
}

#[derive(serde::Serialize)]
struct CurrentUserData {
    user_id: uuid::Uuid,
    username: String,
    phone_number: String,
    phone_verified: bool,
    current_identity: Option<IdentityInfo>,
    available_identities: Vec<IdentityInfo>,
}

#[derive(serde::Serialize)]
struct ErrorResponse {
    success: bool,
    message: String,
    error_code: String,
}
