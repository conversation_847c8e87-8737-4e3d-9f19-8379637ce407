use crate::model::user::auth::*;
use crate::utils::jwt;
use chrono::{Duration, Utc};
use sqlx::{PgPool, Row};
use std::sync::Arc;
use tracing::{info, warn};
use uuid::Uuid;

pub struct IdentityService {
    db: PgPool,
}

impl IdentityService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// 获取用户在指定租户schema中的所有身份信息
    pub async fn get_user_identities_in_schema(
        &self, 
        user_id: Uuid, 
        schema_name: &str
    ) -> AuthResult<Vec<UserIdentity>> {
        let query = format!(
            r#"
            SELECT id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            FROM "{schema_name}".user_identities
            WHERE user_id = $1
            ORDER BY created_at ASC
            "#
        );

        let identities = sqlx::query_as::<_, UserIdentity>(&query)
            .bind(user_id)
            .fetch_all(&self.db)
            .await?;

        Ok(identities)
    }

    /// 在指定租户schema中创建用户身份
    pub async fn create_user_identity(
        &self,
        user_id: Uuid,
        role_id: Uuid,
        target_type: &str,
        target_id: Option<Uuid>,
        subject: Option<&str>,
        schema_name: &str,
    ) -> AuthResult<UserIdentity> {
        let identity_id = Uuid::new_v4();
        let now = Utc::now();

        let query = format!(
            r#"
            INSERT INTO "{schema_name}".user_identities
            (id, user_id, role_id, target_type, target_id, subject, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            "#
        );

        let identity = sqlx::query_as::<_, UserIdentity>(&query)
            .bind(identity_id)
            .bind(user_id)
            .bind(role_id)
            .bind(target_type)
            .bind(target_id)
            .bind(subject)
            .bind(now)
            .bind(now)
            .fetch_one(&self.db)
            .await?;

        info!("User identity created: user_id={}, identity_id={}, schema={}", 
              user_id, identity_id, schema_name);

        Ok(identity)
    }

    /// 获取用户在指定租户schema中的特定身份
    pub async fn get_user_identity_in_schema(
        &self,
        user_id: Uuid,
        identity_id: Uuid,
        schema_name: &str,
    ) -> AuthResult<Option<UserIdentity>> {
        let query = format!(
            r#"
            SELECT id, user_id, role_id, target_type, target_id, subject, created_at, updated_at
            FROM "{schema_name}".user_identities
            WHERE user_id = $1 AND id = $2
            "#
        );

        let identity = sqlx::query_as::<_, UserIdentity>(&query)
            .bind(user_id)
            .bind(identity_id)
            .fetch_optional(&self.db)
            .await?;

        Ok(identity)
    }

    /// 更新用户身份信息
    pub async fn update_user_identity(
        &self,
        identity_id: Uuid,
        target_type: Option<&str>,
        target_id: Option<Option<Uuid>>,
        subject: Option<Option<&str>>,
        schema_name: &str,
    ) -> AuthResult<()> {
        let now = Utc::now();
        
        // Build dynamic update query
        let mut set_clauses = Vec::new();
        let mut param_count = 1;

        if target_type.is_some() {
            set_clauses.push(format!("target_type = ${}", param_count));
            param_count += 1;
        }
        if target_id.is_some() {
            set_clauses.push(format!("target_id = ${}", param_count));
            param_count += 1;
        }
        if subject.is_some() {
            set_clauses.push(format!("subject = ${}", param_count));
            param_count += 1;
        }
        
        set_clauses.push(format!("updated_at = ${}", param_count));
        param_count += 1;

        if set_clauses.is_empty() {
            return Ok(());
        }

        let query = format!(
            r#"
            UPDATE "{schema_name}".user_identities
            SET {}
            WHERE id = ${}
            "#,
            set_clauses.join(", "),
            param_count
        );

        let mut query_builder = sqlx::query(&query);
        
        if let Some(target_type) = target_type {
            query_builder = query_builder.bind(target_type);
        }
        if let Some(target_id) = target_id {
            query_builder = query_builder.bind(target_id);
        }
        if let Some(subject) = subject {
            query_builder = query_builder.bind(subject);
        }
        
        query_builder = query_builder.bind(now).bind(identity_id);

        query_builder.execute(&self.db).await?;

        info!("User identity updated: identity_id={}, schema={}", identity_id, schema_name);
        Ok(())
    }

    /// 删除用户身份
    pub async fn delete_user_identity(
        &self,
        identity_id: Uuid,
        schema_name: &str,
    ) -> AuthResult<()> {
        let query = format!(
            r#"DELETE FROM "{schema_name}".user_identities WHERE id = $1"#
        );

        sqlx::query(&query)
            .bind(identity_id)
            .execute(&self.db)
            .await?;

        info!("User identity deleted: identity_id={}, schema={}", identity_id, schema_name);
        Ok(())
    }

    // Legacy compatibility methods (to be deprecated)
    pub async fn get_identity_suggestions(&self, user_id: Uuid) -> AuthResult<Vec<IdentityBindingSuggestion>> {
        // Return empty suggestions for now - this feature will be reimplemented later
        warn!("get_identity_suggestions called - returning empty results (method deprecated)");
        Ok(Vec::new())
    }

    pub async fn bind_identity(&self, user_id: Uuid, request: BindIdentityRequest) -> AuthResult<BindIdentityResponse> {
        // This is a legacy method - for now return an error directing to use new APIs
        warn!("bind_identity called - method deprecated, use create_user_identity instead");
        Err(AuthError::InsufficientPermissions)
    }

    pub async fn switch_identity(
        &self,
        user_id: Uuid,
        session_id: Uuid,
        request: SwitchIdentityRequest,
        ip_address: Option<std::net::IpAddr>,
        user_agent: Option<String>,
    ) -> AuthResult<SwitchIdentityResponse> {
        // This is a legacy method - for now return an error directing to use new session management
        warn!("switch_identity called - method deprecated, use new session management");
        Err(AuthError::InsufficientPermissions)
    }

    pub async fn get_user_identities(&self, user_id: Uuid) -> AuthResult<Vec<IdentityInfo>> {
        warn!("get_user_identities called without schema - method deprecated");
        // Return empty for now - controllers should specify tenant schema
        Ok(Vec::new())
    }

    pub async fn verify_identity(&self, identity_id: Uuid, verified_by: Uuid) -> AuthResult<()> {
        warn!("verify_identity called - method deprecated");
        // This method needs to be reimplemented with tenant schema context
        Err(AuthError::InsufficientPermissions)
    }
}