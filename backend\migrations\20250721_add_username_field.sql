-- Add username field to users table
-- Migration: 20250721_add_username_field

-- Add username field to users table
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE;

-- Create index for username lookups
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);

-- Update existing users to have a username based on their phone number
-- This ensures all existing users have a valid username
UPDATE public.users SET username = phone_number WHERE username IS NULL;

-- Make username NOT NULL after setting default values
ALTER TABLE public.users ALTER COLUMN username SET NOT NULL;

-- Update the default admin user to have 'admin' as username if it exists
UPDATE public.users SET username = 'admin' WHERE phone_number = 'admin';

-- Add a comment to the table explaining the authentication priority
COMMENT ON TABLE public.users IS 'User accounts with authentication priority: 1. Username/password, 2. SMS verification';