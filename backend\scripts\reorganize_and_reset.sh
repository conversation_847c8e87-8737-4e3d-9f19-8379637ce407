#!/bin/bash
# Reorganize migrations and reset the database

# 1. Create a backup directory with a timestamp
BACKUP_DIR="migrations_backup_$(date +%Y%m%d%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 2. Move existing migrations to the backup directory
mv migrations/* "$BACKUP_DIR/"
echo "Backed up existing migrations to $BACKUP_DIR"

# 3. Copy new migrations to the migrations directory
cp migrations_backup_new/* migrations/
echo "Copied new migrations to migrations/"

# 4. Run the reset script
./scripts/reset_migrations.sh

# 5. List the new migrations
echo "New migrations to be applied:"
ls -1 migrations
