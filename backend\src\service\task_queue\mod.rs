pub mod ocr_task;
pub mod grader_task;
pub mod tracer_task;

use std::future::Future;
use std::num::NonZeroUsize;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use std::time::Duration;
use futures::stream::FuturesUnordered;
use futures::StreamExt;
use rand::seq::IteratorRandom;
use reqwest::Client;
use redis::{Client as RedisClient, AsyncCommands, RedisResult};
use serde::de::DeserializeOwned;
use serde::{Deserialize, Serialize};
use sqlx::PgPool;
use tokio::sync::Mutex;
use tokio::task::JoinHandle;
use tokio::time::interval;
use tracing::log;
use grader_task::GraderTask;
use ocr_task::OcrTask;
use crate::service::task_queue::tracer_task::TracerTask;
use crate::utils::redis_client::RedisPool;

pub trait Task: Send + 'static {
    fn get_id(&self) -> (String, String);
    fn execute(&mut self, client: Client, db: PgPool, redis_pool: RedisPool) -> impl Future<Output = Result<(), String>> + Send;
}

#[derive(Clone)]
pub struct TaskQueueState {
    pub ocr: Arc<TaskQueue>,
    pub grader: Arc<TaskQueue>,
    pub tracer: Arc<TaskQueue>,
}
impl TaskQueueState {
    pub fn new(db: PgPool, pool: RedisPool) -> Self {
        Self {
            ocr: Arc::new(TaskQueue::new(db.clone(), pool.clone(), OCR_KEY.to_string())),
            grader: Arc::new(TaskQueue::new(db.clone(), pool.clone(), GRADER_KEY.to_string())),
            tracer: Arc::new(TaskQueue::new(db, pool, TRACE_KEY.to_string())),
        }
    }
    pub fn run(&self) {
        let ocr = self.ocr.clone();
        let grader = self.grader.clone();
        let tracer = self.tracer.clone();
        tokio::spawn(ocr.run::<OcrTask>());
        tokio::spawn(grader.run::<GraderTask>());
        tokio::spawn(tracer.run::<TracerTask>());
    }
}

pub struct TaskQueue {
    client: Client,
    redis_pool: RedisPool,
    key: String,
    db: PgPool,
    tasks: Mutex<FuturesUnordered<JoinHandle<()>>>,
    success_count: Arc<AtomicUsize>,
    failed_count: Arc<AtomicUsize>,
    worker_size: Arc<AtomicUsize>,
}

pub static REDIS_TASK_QUEUE_PREFIX: &str = "task_queue_";
pub static REDIS_HASH_QUEUE_PREFIX: &str = "task_hash_";
pub static OCR_KEY: &str = "ocr_";
pub static GRADER_KEY: &str = "grader_";
pub static TRACE_KEY: &str = "trace_";

#[derive(Serialize)]
pub struct Status {
    failed_per_thousand: usize,
    success_count: usize,
    failed_count: usize,
    worker_size: usize,
}

impl TaskQueue {
    pub fn new(db: PgPool, redis_pool: RedisPool, key: String) -> TaskQueue {
        let tasks = Mutex::new(FuturesUnordered::new());
        TaskQueue {
            client: Client::builder()
                .pool_max_idle_per_host(50)   // 把每主机空闲上限调到 20
                .build().expect("client create"),
            redis_pool,
            key,
            db,
            tasks,
            success_count: Arc::new(AtomicUsize::new(0)),
            failed_count: Arc::new(AtomicUsize::new(0)),
            worker_size: Arc::new(AtomicUsize::new(20)),
        }
    }

    pub async fn run<T: Task + DeserializeOwned>(self: Arc<Self>) -> anyhow::Result<()> {
        let mut conn = self.redis_pool.get().await?;
        log::warn!("Tasks {} running...", self.key);
        let mut idle_count = 0;
        let mut tick = interval(Duration::from_secs(1));
        loop {
            let size = self.idle_size().await;
            if size > 0 {
                let queue_key = {
                    let pattern = format!("{}{}*", REDIS_TASK_QUEUE_PREFIX, self.key);
                    let mut iter = conn.scan_match::<_, String>(&pattern).await.expect("scan_match");
                    let mut queue_keys = Vec::new();
                    while let Some(key) = iter.next_item().await {
                        queue_keys.push(key);
                    }
                    if let Some(index) = (0..queue_keys.len()).choose(&mut rand::rng()) {
                        idle_count = 0;
                        queue_keys[index].clone()
                    } else {
                        idle_count = (idle_count+1).min(60000);
                        "".to_string()
                    }
                };
                if idle_count > 0 {
                    tokio::time::sleep(Duration::from_millis(1000 + idle_count)).await;
                    continue;
                }
                // 从租户域中获取任务，然后分配add_task
                log::info!("get: {:?}", queue_key);
                let tasks: Vec<_> = match conn.lpop::<_,Vec<String>>(queue_key, NonZeroUsize::new(size)).await {
                    Ok(tasks) => tasks,
                    Err(e) => {
                        log::error!("get task: {}", e);
                        continue;
                    }
                };
                log::info!("tasks: {:?}", tasks);
                for task_id in tasks {
                    let hash_key = format!("{}{}", REDIS_HASH_QUEUE_PREFIX, self.key);
                    if let Ok(task_json) = conn.hget::<_, _, String>(&hash_key, task_id.clone()).await {
                        conn.hdel::<_, _, bool>(&hash_key, task_id).await.ok();
                        match serde_json::from_str::<T>(task_json.as_str()) {
                            Ok(t) => {
                                self.execute_task(t).await;
                            },
                            Err(e) => {
                                log::error!("Failed to deserialize task: {:?}", e);
                            }
                        }
                    }
                }
            }
            tick.tick().await;
        }
    }
    pub async fn add_task<T: Task + Serialize + DeserializeOwned>(self: Arc<Self>, task: T) -> anyhow::Result<()> {
        let (id, tenant) = task.get_id();
        let queue_key = format!("{}{}{}", REDIS_TASK_QUEUE_PREFIX, self.key, tenant);
        log::info!("add task to {}", queue_key);
        let hash_key = format!("{}{}", REDIS_HASH_QUEUE_PREFIX, self.key);
        let mut conn = self.redis_pool.get().await?;
        conn.rpush(&queue_key, id.clone()).await?;
        let task_json = serde_json::to_string(&task)?;
        conn.hset(hash_key, id.clone(), task_json).await?;
        Ok(())
    }
    async fn execute_task<T: Task>(&self, mut task: T) {
        let success_counter = Arc::clone(&self.success_count);
        let failed_count = Arc::clone(&self.failed_count);
        let client = self.client.clone();
        let db = self.db.clone();
        let redis_pool = self.redis_pool.clone();
        let handle = tokio::task::spawn(async move {
            match task.execute(client, db, redis_pool).await {
                Ok(()) => {
                    success_counter.fetch_add(1, Ordering::Relaxed);
                },
                Err(e) => {
                    failed_count.fetch_add(1, Ordering::Relaxed);
                    log::error!("{}", e);
                }
            }
        });
        self.tasks.lock().await.push(handle);
    }
    pub async fn set_worker_size(&self, worker_size: usize) {
        self.worker_size.store(worker_size, Ordering::SeqCst);
    }
    pub async fn reset(&self) {
        let success_count = self.success_count.load(Ordering::SeqCst);
        let failed_count = self.failed_count.load(Ordering::SeqCst);
        self.success_count.fetch_sub(success_count, Ordering::SeqCst);
        self.failed_count.fetch_sub(failed_count, Ordering::SeqCst);
        let mut queue = self.tasks.lock().await;
        for _ in 0..success_count + failed_count {
            queue.next().await;
        }
    }
    pub fn status(&self) -> Status {
        let success_count = self.success_count.load(Ordering::SeqCst);
        let failed_count = self.failed_count.load(Ordering::SeqCst);
        let worker_size = self.worker_size.load(Ordering::SeqCst);
        let failed_per_thousand = if failed_count == 0 {
            0
        } else {
            failed_count * 1000 / (success_count + failed_count)
        };
        Status {
            failed_per_thousand,
            success_count,
            failed_count,
            worker_size,
        }
    }
    async fn idle_size(&self) -> usize {
        let worker_size = self.worker_size.load(Ordering::SeqCst);
        let success_count = self.success_count.load(Ordering::SeqCst);
        let failed_count = self.failed_count.load(Ordering::SeqCst);
        let doing = self.tasks.lock().await.len() - success_count - failed_count;
        if doing > worker_size {
            0
        } else {
            worker_size - doing
        }
    }
}