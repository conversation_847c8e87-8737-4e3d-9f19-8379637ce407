import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, RefreshCw, UserPlus, Users, Calendar, Book, FileText } from 'lucide-react';
import EditHomework from './components/EditHomework';
import { UpdateHomeworkParams, HomeworkStatusEnum, Homework } from '@/types/homework';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import { homeworkApi } from '@/services/homeworkApi';
import { getIdentityInfoFromLocalStorage } from '@/lib/apiUtils';
import { homeworkStudentsApi } from '@/services/homeworkStudentsApi';
import { HomeworkStudents, HomeworkStudentsWithStudentBaseInfo } from '@/types/homeworkStudents';
import BatchBindToHomework from './components/BatchBindToHomework';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import { TeachingClassesDetail } from '@/types/teachingClasses';
import { Checkbox } from '@/components/ui/checkbox';
import { map } from 'zod';
import { set } from 'react-hook-form';
import { TeachingAid, teachingAidApi } from '@/types';
import AddpapersByToHomework from './components/AddpapersByToHomework';
import { Textbook } from '@/services/questionApi';
import { homeworkPapersApi } from '@/services/homeworkPapersApi';

const DetailPage: React.FC = () => {
  const identityInfo = getIdentityInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const tenantName = identityInfo?.tenant_name || '';
  const navigate = useNavigate();

  const { homeworkId } = useParams<{ homeworkId: string }>();
  // 查询条件
  const [searchName, setSearchName] = useState('');
  const [searchClass, setSearchClass] = useState('全部班级');
  const [searchNumber, setSearchNumber] = useState('');
  // 分页
  const [currentPage, setCurrentPage] = useState(1);
  // 对话框
  const [isBatchAddDialogOpen, setIsBatchAddDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [isEditHomeworkDialogOpen, setIsEditHomeworkDialogOpen] = useState(false);
  const [isBatchUnbindDialogOpen, setIsBatchUnbindDialogOpen] = useState(false);
  const [isAddPapersDialogOpen, setIsAddPapersDialogOpen] = useState(false);
  //数据
  const [homework, setHomework] = useState<Homework>();
  const [students, setStudents] = useState<HomeworkStudentsWithStudentBaseInfo[]>([]);
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [classList, setClassList] = useState<TeachingClassesDetail[]>([]);
  const [textbookList, setTextbookList] = useState<TeachingAid[]>([]);
  // 编辑作业表单（本地状态）
  const [editHomeworkForm, setEditHomeworkForm] = useState<UpdateHomeworkParams>({
    id: homeworkId as String,
    homework_name: '' as String,
    homework_status: HomeworkStatusEnum.Draft,
    subject_group_id: '',
    description: '',
  });
  // 选中学生id数组
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // 是否全选
  const isAllSelected = students.length > 0 && selectedIds.length === students.length;

  useEffect(() => {
    if (homeworkId) {
      console.log("homeworkId", homeworkId);
      loadInitialData();
    }

  }, [homeworkId]);
  const loadInitialData = async () => {
    //测试查询所有教辅
    teachingAidApi.getTeachingAids().then(res => {
      console.log("textbooks", res);
      setTextbookList(res);
    })

    //根据id查询班级信息
    homeworkApi.getHomeworkById(tenantId, tenantName, homeworkId as string).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setHomework(data);
    })
    //根据作业id查询学生列表
    homeworkStudentsApi.findAllByHomeworkId(tenantId, tenantName, {
      homework_id: homeworkId as String,
    }).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setStudents(Array.isArray(data) ? data : []);
    })

    //查询教学班列表
    TeachingClassesApi.getUserClassList(tenantId, tenantName).then(res => {
      const { success, data, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setClassList(Array.isArray(data) ? data : []);
    })
  }

  // 删除学生对话框
  const [deleteStudentDialog, setDeleteStudentDialog] = useState<{ open: boolean; student?: any }>({ open: false });

  //   // 过滤学生
  //   const filteredStudents = students.filter(s => {
  //     return (
  //       //(searchName === '' || s.name.includes(searchName)) &&
  //       //(searchClass === '全部班级' || s.class === searchClass) &&
  //       //(searchNumber === '' || s.student_number.includes(searchNumber))
  //     );
  //   });
  //   const totalPages = Math.ceil(filteredStudents.length / PAGE_SIZE);
  //   const pagedStudents = filteredStudents.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

  // 模拟根据作业id查询作业信息（实际开发时用接口）
  // 单个勾选/取消
  const handleSelect = (id: string) => {
    setSelectedIds(prev =>
      prev.includes(id) ? prev.filter(_id => _id !== id) : [...prev, id]
    );
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(students.map(s => s.student_base_info?.id.toString() || ''));
    } else {
      setSelectedIds([]);
    }
  };
  const handleUnbind = async () => {
    if(selectedIds.length ===0){
      return;
    }
    const {success,message} = await homeworkStudentsApi.batchUnbindStudentsFromHomework(tenantId,tenantName,{
      homework_id: homeworkId as String,
      student_id_list: selectedIds,
    })
    if(!success){
      console.error('报错:'+message);
      return;
    }
    setSelectedIds([]);
    setIsBatchUnbindDialogOpen(false);
    loadInitialData();
  }


  const handleBatchBindStudent = async (studentIds: string[]) => {

    homeworkStudentsApi.batchBindStudentsToHomework(tenantId, tenantName, {
      homework_id: homeworkId as String,
      student_id_list: studentIds,
    }).then(res => {
      const { success, message } = res;
      if (!success) {
        console.error('报错:' + message);
        return;
      }
      setIsBatchAddDialogOpen(false);
      loadInitialData();
    })
  }

  //绑定试卷
  const handleBindPapers= async (paperId: string[])=>{

      homeworkPapersApi.bindPapersToHomework(tenantId, tenantName, {
        homework_id: homeworkId as string,
        paper_ids: paperId,
      }).then(res => {
        const { success, message } = res;
        if (!success) {
          console.error('报错:' + message);
          return;
        }
        loadInitialData();
      })
  }

  // 编辑作业保存
  const handleSaveEditHomework = async () => {
    if (!homework) return;
    try {
      await homeworkApi.updateHomework(tenantId, tenantName, {
        id: homeworkId as String,
        homework_name: homework?.homework_name as String,
        homework_status: homework?.homework_status as HomeworkStatusEnum,
        subject_group_id: homework?.subject_group_id as String,
        description: homework?.description as String,
      });
      setIsEditHomeworkDialogOpen(false);
      loadInitialData();
    } catch (err) {
      console.error('Failed to update homework');
      console.error('Error updating homework:', err);
    }
  };

  const openEditDialog = (homework: Homework) => {
    setEditHomeworkForm(homework);
    setIsEditHomeworkDialogOpen(true);
  }
  // 删除学生
  const openDeleteStudentDialog = (student: any) => {
    setDeleteStudentDialog({ open: true, student });
  };
  const handleDeleteStudent = () => {
    // 实际开发时调用接口
    setDeleteStudentDialog({ open: false });
  };

  return (
    <div className="space-y-6">
      {/* 删除作业确认 */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            {/* <DialogDescription>你确定要删除作业 {homework.homework_name} 吗？此操作无法撤销。</DialogDescription> */}
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>取消</Button>
            <Button variant="destructive" onClick={() => setDeleteConfirmOpen(false)}>删除</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* 删除学生对话框 */}
      <Dialog open={deleteStudentDialog.open} onOpenChange={open => setDeleteStudentDialog({ open })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认移除学生</DialogTitle>
            <DialogDescription>你确定要移除学生 {deleteStudentDialog.student?.student_base_info?.name} 吗？</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setDeleteStudentDialog({ open: false })}>取消</Button>
            <Button variant="destructive" onClick={handleDeleteStudent}>移除</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* 编辑作业对话框 */}
      <EditHomework
        open={isEditHomeworkDialogOpen}
        onOpenChange={setIsEditHomeworkDialogOpen}
        homeworkForm={editHomeworkForm}
        onHomeworkFormChange={form => setEditHomeworkForm(form)}
        subjectGroups={subjectGroups}
        onSave={handleSaveEditHomework}
        onCancel={() => setIsEditHomeworkDialogOpen(false)}
      />
      {/* 顶部导航栏 */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
        <div className="flex gap-2">
          <Button onClick={() => openEditDialog(homework as Homework)}>
            <Edit className="h-4 w-4 mr-2" />
            编辑作业
          </Button>
          <Button variant="destructive" onClick={() => setDeleteConfirmOpen(true)}>
            <Trash2 className="h-4 w-4 mr-2" />
            删除作业
          </Button>
        </div>
      </div>
      {/* 作业信息卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">作业名称</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{homework?.homework_name}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">学科组</CardTitle>
            <Book className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">参与学生数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">创建时间</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{homework?.created_at ? new Date(homework.created_at.toString()).toLocaleDateString() : '-'}</div>
          </CardContent>
        </Card>
      </div>
      {/* 学生列表 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>学生列表</CardTitle>
            <CardDescription>当前共有 {students.length} 名学生</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setIsAddPapersDialogOpen(true)}>
              <UserPlus className="h-4 w-4 mr-2" />
              添加试卷
            </Button>
            <Button variant="outline" onClick={() => setIsBatchAddDialogOpen(true)}>
              <Users className="h-4 w-4 mr-2" />
              批量添加学生
            </Button>
            <Button variant="destructive" className={selectedIds.length ===0 ? "opacity-50 cursor-not-allowed" : ""} onClick={() => setIsBatchUnbindDialogOpen(true)}>
              <Users className="h-4 w-4 mr-2" />
              批量解绑学生
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* 搜索栏 */}
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <Input
                placeholder="搜索学生姓名"
                value={searchName}
                onChange={e => setSearchName(e.target.value)}
                className="max-w-xs"
              />
            </div>
            <div className="flex-1">
              <Input
                placeholder="学号"
                value={searchNumber}
                onChange={e => setSearchNumber(e.target.value)}
                className="max-w-xs"
              />
            </div>
            <Select value={searchClass} onValueChange={setSearchClass}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="班级" />
              </SelectTrigger>
              <SelectContent>
                {/* {mockClasses.map(cls => (
                  <SelectItem key={cls} value={cls}>{cls}</SelectItem>
                ))} */}
              </SelectContent>
            </Select>
            <Button onClick={() => setCurrentPage(1)}>
              <RefreshCw className="h-4 w-4 mr-2" />
              查询
            </Button>
          </div>
          {/* 学生表格 */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead><Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                  /></TableHead>
                  <TableHead>学生姓名</TableHead>
                  <TableHead>班级</TableHead>
                  <TableHead>学号</TableHead>
                  <TableHead>性别</TableHead>
                  <TableHead>电话</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {students.map(student => (
                  <TableRow key={student.id.toString()}>
                    <TableCell>
                      <Checkbox
                        checked={selectedIds.includes(student.student_base_info?.id.toString() || '')}
                        onCheckedChange={() => handleSelect(student.student_base_info?.id.toString() || '')}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{student.student_base_info?.name}</TableCell>
                    <TableCell>{'-'}</TableCell>
                    <TableCell>{student.student_base_info?.student_number}</TableCell>
                    <TableCell>{student.student_base_info?.gender}</TableCell>
                    <TableCell>{student.student_base_info?.phone}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" onClick={() => openDeleteStudentDialog(student)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* 分页 */}
          {/* <div className="flex justify-center mt-4 gap-2">
            <Button variant="outline" size="sm" disabled={currentPage === 1} onClick={() => setCurrentPage(p => Math.max(1, p - 1))}>上一页</Button>
            <span className="px-2">{currentPage} / {totalPages}</span>
            <Button variant="outline" size="sm" disabled={currentPage === totalPages} onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}>下一页</Button>
          </div> */}
        </CardContent>
      </Card>
      {/* 批量添加学生对话框 */}
      <BatchBindToHomework
        open={isBatchAddDialogOpen}
        onOpenChange={setIsBatchAddDialogOpen}
        onBind={handleBatchBindStudent}
        classList={classList}
        tenantId={tenantId}
        tenantName={tenantName}
      />
      {/* 批量解绑学生对话框*/}
      <Dialog open={isBatchUnbindDialogOpen} onOpenChange={open => setIsBatchUnbindDialogOpen(open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认移除学生</DialogTitle>
            <DialogDescription>你确定要移除学生 {selectedIds.map(id => students.find(s => s.student_base_info?.id.toString() === id)?.student_base_info?.name).join(',')} 吗？</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsBatchUnbindDialogOpen(false)}>取消</Button>
            <Button variant="destructive" onClick={handleUnbind}>移除</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* 添加试卷对话框 */}
      <AddpapersByToHomework
        open={isAddPapersDialogOpen}
        onOpenChange={setIsAddPapersDialogOpen}
        onBind={handleBindPapers}
        textbookList={textbookList}
      />
    </div>
  );
};

export default DetailPage;
