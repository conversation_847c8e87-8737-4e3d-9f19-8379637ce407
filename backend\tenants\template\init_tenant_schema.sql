-- This file is a template for tenant-specific schemas.
-- It will be copied and modified for each new tenant.
-- {schema} 占位符会被实际的 schema 名称替换

-- =============================================
-- ORGANIZATIONAL STRUCTURE
-- 学科组织简化说明：
-- 基于"一个学科组仅包含一个学科"的业务约束，本架构采用以下简化策略：
-- 1. 学科信息通过关联链获取，避免冗余存储
-- 2. 核心关联链：teaching_classes.subject_group_id → subject_groups.subject
-- 3. 学生学科信息：student_teaching_classes → teaching_classes → subject_groups → subject
-- 4. 用户学科权限：user_identities → target关联 → subject_groups → subject
-- =============================================

-- Subject groups table
CREATE TABLE IF NOT EXISTS {schema}.subject_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_name VARCHAR(100) NOT NULL,
    subject VARCHAR(50) NOT NULL,
    description TEXT,
    leader_user_id UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Administrative classes table (行政班表)
CREATE TABLE IF NOT EXISTS {schema}.administrative_classes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_name VARCHAR(100) NOT NULL,                     -- 班级名称
    code VARCHAR(50),                               -- 班级编号
    academic_year VARCHAR(20),                      -- 学年
    grade_level_code  VARCHAR(20),
    teacher_id UUID REFERENCES {schema}.teachers(id),  -- 班主任
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teaching classes table (教学班表)
CREATE TABLE IF NOT EXISTS {schema}.teaching_classes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_name VARCHAR(100) NOT NULL,                     -- 班级名称
    code VARCHAR(50),                               -- 班级编号
    academic_year VARCHAR(20),                      -- 学年
    subject_group_id UUID REFERENCES {schema}.subject_groups(id),
    teacher_id UUID REFERENCES {schema}.teachers(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User identities within tenant (replaces teacher_assignments)
-- 学科信息获取规则：
-- - target_type='subject_group': 通过 subject_groups.subject 获取
-- - target_type='class'(教学班): 通过 teaching_classes.subject_group_id → subject_groups.subject 获取
-- - 其他情况: 可使用 subject 字段明确指定学科（可选）
CREATE TABLE IF NOT EXISTS {schema}.user_identities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) NOT NULL,
    role_id UUID REFERENCES public.roles(id) NOT NULL,
    target_type VARCHAR(30) CHECK (target_type IN ('school', 'subject_group', 'grade', 'class', 'student')),
    target_id UUID,
    subject VARCHAR(50),  -- 可选字段，仅在无法通过关联获取学科信息时使用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_id, target_id)  -- 简化约束，一个用户对同一目标只能有一个角色实例
);

-- =============================================
-- STUDENT MANAGEMENT
-- =============================================

-- Students table (comprehensive version)
CREATE TABLE IF NOT EXISTS {schema}.students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id VARCHAR(50) UNIQUE NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    gender VARCHAR(10),
    birth_date DATE,
    id_number VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    guardian_name VARCHAR(100),
    guardian_phone VARCHAR(20),
    guardian_relation VARCHAR(20),
    administrative_class_id UUID REFERENCES {schema}.administrative_classes(id),
    grade_level_id UUID REFERENCES public.grade_levels(id),
    user_id UUID REFERENCES public.users(id),
    enrollment_date DATE,
    status VARCHAR(20) CHECK (status IN ('active', 'inactive', 'graduated', 'transferred')) DEFAULT 'active',
    profile_level VARCHAR(20) CHECK (profile_level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')),
    profile_tags JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student teaching class relationships
-- 学科信息通过 teaching_classes.subject_group_id → subject_groups.subject 获取
CREATE TABLE IF NOT EXISTS {schema}.student_teaching_classes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    class_id UUID REFERENCES {schema}.teaching_classes(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, class_id)
);

-- Student profile levels
CREATE TABLE IF NOT EXISTS {schema}.student_profile_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    subject VARCHAR(50) NOT NULL,
    level VARCHAR(20) CHECK (level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')),
    level_description TEXT,
    assessment_date DATE NOT NULL,
    assessed_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, subject, assessment_date)
);

-- Student profile tags
CREATE TABLE IF NOT EXISTS {schema}.student_profile_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    tag_name VARCHAR(50) NOT NULL,
    tag_value VARCHAR(100),
    tag_category VARCHAR(30) CHECK (tag_category IN ('academic', 'behavior', 'interest', 'ability', 'other')),
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, tag_name)
);

-- =============================================
-- TEACHER MANAGEMENT
-- =============================================

-- Teachers table
CREATE TABLE IF NOT EXISTS {schema}.teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) NOT NULL,
    teacher_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    gender VARCHAR(10) CHECK (gender IN ('男', '女', '未知')),
    date_of_birth TIMESTAMP WITH TIME ZONE,
    id_card_number VARCHAR(20),
    highest_education VARCHAR(50),
    graduation_school VARCHAR(100),
    major VARCHAR(100),
    hire_date TIMESTAMP WITH TIME ZONE,
    employment_status VARCHAR(20) NOT NULL DEFAULT '在职' 
        CHECK (employment_status IN ('在职', '离职', '退休', '停职', '试用期')),
    title VARCHAR(50),
    teaching_subjects TEXT[],
    homeroom_class_id BIGINT,
    grade_level_id INTEGER,
    subject_group_id BIGINT,
    office_location VARCHAR(100),
    bio TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_employee_id_per_tenant UNIQUE (tenant_id, employee_id),
    CONSTRAINT unique_user_per_tenant UNIQUE (tenant_id, user_id),
    CONSTRAINT valid_phone CHECK (phone IS NULL OR phone ~ '^[0-9+\-\s()]+$'),
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
    CONSTRAINT valid_id_card CHECK (id_card_number IS NULL OR length(id_card_number) IN (15, 18))
);

-- =============================================
-- EXAM MANAGEMENT
-- =============================================

-- Exams table
CREATE TABLE IF NOT EXISTS {schema}.exams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_name VARCHAR(100) NOT NULL,
    exam_type VARCHAR(20) CHECK (exam_type IN ('single', 'joint')),
    grade_level VARCHAR(50) NOT NULL,
    exam_nature VARCHAR(20) CHECK (exam_nature IN ('formal', 'mock', 'practice')),
    description TEXT,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    expected_collection_time TIMESTAMP WITH TIME ZONE,
    scan_start_time TIMESTAMP WITH TIME ZONE,
    grading_mode VARCHAR(20) CHECK (grading_mode IN ('intelligent', 'manual_then_scan')),
    quality_control VARCHAR(20) CHECK (quality_control IN ('single', 'double_blind')),
    ai_confidence_threshold DECIMAL(3,2),
    manual_review_ratio DECIMAL(3,2),
    status VARCHAR(20) CHECK (status IN ('draft', 'published', 'in_progress', 'completed')) DEFAULT 'draft',
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Exam subjects
CREATE TABLE IF NOT EXISTS {schema}.exam_subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES public.subjects(id),
    paper_template_id UUID REFERENCES public.exam_papers(id),
    total_score DECIMAL(5,2),
    pass_score DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Exam students
CREATE TABLE IF NOT EXISTS {schema}.exam_students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    class_id UUID REFERENCES {schema}.administrative_classes(id),
    seat_number VARCHAR(20),
    is_absent BOOLEAN DEFAULT FALSE,
    absent_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Joint exam management
CREATE TABLE IF NOT EXISTS {schema}.joint_exams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    main_exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    organizer_tenant_id UUID REFERENCES public.tenants(id),
    participant_tenant_id UUID REFERENCES public.tenants(id),
    invitation_status VARCHAR(20) CHECK (invitation_status IN ('pending', 'accepted', 'rejected')),
    invitation_sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(20) CHECK (sync_status IN ('pending', 'synced', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Paper scans
CREATE TABLE IF NOT EXISTS {schema}.paper_scans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    paper_sequence INTEGER NOT NULL,
    front_page_url TEXT,
    back_page_url TEXT,
    scan_quality INTEGER CHECK (scan_quality BETWEEN 1 AND 10),
    scan_method VARCHAR(20) CHECK (scan_method IN ('image_upload', 'scanner_direct', 'third_party')),
    scan_device VARCHAR(100),
    is_duplicate BOOLEAN DEFAULT FALSE,
    is_blank BOOLEAN DEFAULT FALSE,
    is_abnormal BOOLEAN DEFAULT FALSE,
    abnormal_reason TEXT,
    needs_review BOOLEAN DEFAULT FALSE,
    reviewed_by UUID REFERENCES public.users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    minio_bucket VARCHAR(100),
    minio_object_key VARCHAR(500),
    file_size BIGINT,
    scan_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Answer card blocks
CREATE TABLE IF NOT EXISTS {schema}.answer_card_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    block_name VARCHAR(100) NOT NULL,
    block_type VARCHAR(30) CHECK (block_type IN ('single_question', 'multi_question', 'composite')),
    position_info JSONB NOT NULL,
    template_version VARCHAR(50),
    max_score DECIMAL(5,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Card block question links
CREATE TABLE IF NOT EXISTS {schema}.card_block_question_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    card_block_id UUID REFERENCES {schema}.answer_card_blocks(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    link_type VARCHAR(20) CHECK (link_type IN ('one_to_one', 'one_to_many', 'many_to_one', 'many_to_many')),
    weight_ratio DECIMAL(3,2) DEFAULT 1.0,
    score_mapping JSONB,
    is_primary BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(card_block_id, question_id)
);

-- =============================================
-- GRADING MANAGEMENT
-- =============================================

-- Grading assignments
CREATE TABLE IF NOT EXISTS {schema}.grading_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    grader_user_id UUID REFERENCES public.users(id),
    assignment_type VARCHAR(20) CHECK (assignment_type IN ('manual', 'ai', 'hybrid')),
    assignment_method VARCHAR(20) CHECK (assignment_method IN ('by_quantity', 'by_difficulty', 'by_subject', 'random')),
    priority_level INTEGER CHECK (priority_level BETWEEN 1 AND 5) DEFAULT 3,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) CHECK (status IN ('assigned', 'in_progress', 'completed', 'paused', 'cancelled')) DEFAULT 'assigned',
    estimated_time INTEGER,
    actual_time INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grading records
CREATE TABLE IF NOT EXISTS {schema}.grading_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    grader_user_id UUID REFERENCES public.users(id),
    original_score DECIMAL(5,2),
    final_score DECIMAL(5,2),
    grading_method VARCHAR(20) CHECK (grading_method IN ('manual', 'ai', 'hybrid')),
    grading_time INTEGER,
    grading_notes TEXT,
    is_reviewed BOOLEAN DEFAULT FALSE,
    reviewed_by UUID REFERENCES public.users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Card block grading records
CREATE TABLE IF NOT EXISTS {schema}.card_block_grading_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    card_block_id UUID REFERENCES {schema}.answer_card_blocks(id) ON DELETE CASCADE,
    grader_user_id UUID REFERENCES public.users(id),
    raw_score DECIMAL(5,2) NOT NULL,
    adjusted_score DECIMAL(5,2),
    grading_method VARCHAR(20) CHECK (grading_method IN ('manual', 'ai', 'hybrid')),
    confidence_score DECIMAL(3,2),
    grading_notes TEXT,
    quality_level VARCHAR(20) CHECK (quality_level IN ('excellent', 'good', 'fair', 'poor')),
    is_reviewed BOOLEAN DEFAULT FALSE,
    reviewed_by UUID REFERENCES public.users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    grading_duration INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI grading records
CREATE TABLE IF NOT EXISTS {schema}.ai_grading_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    ai_agent_id VARCHAR(100) NOT NULL,
    ai_model_version VARCHAR(50) NOT NULL,
    ai_model_result JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    processing_time INTEGER,
    error_message TEXT,
    reviewed_by_human BOOLEAN DEFAULT FALSE,
    human_reviewer_id UUID REFERENCES public.users(id),
    human_review_result JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grading statistics
CREATE TABLE IF NOT EXISTS {schema}.grading_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    question_type VARCHAR(20) NOT NULL,
    grader_user_id UUID REFERENCES public.users(id),
    total_papers INTEGER DEFAULT 0,
    avg_score DECIMAL(5,2),
    min_score DECIMAL(5,2),
    max_score DECIMAL(5,2),
    score_distribution JSONB,
    consistency_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grading control states
CREATE TABLE IF NOT EXISTS {schema}.grading_control_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    question_id UUID,
    grader_user_id UUID REFERENCES public.users(id),
    control_level VARCHAR(20) CHECK (control_level IN ('global', 'question', 'grader')),
    control_action VARCHAR(20) CHECK (control_action IN ('start', 'pause', 'resume', 'stop')),
    control_reason TEXT,
    controlled_by UUID REFERENCES public.users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ACADEMIC STATISTICS & ANALYSIS
-- =============================================

-- Academic statistics
CREATE TABLE IF NOT EXISTS {schema}.academic_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    subject VARCHAR(50) NOT NULL,
    total_score DECIMAL(5,2),
    class_rank INTEGER,
    grade_rank INTEGER,
    school_rank INTEGER,
    is_absent BOOLEAN DEFAULT FALSE,
    absent_reason TEXT,
    performance_trend JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Question analysis
CREATE TABLE IF NOT EXISTS {schema}.question_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    question_type VARCHAR(20) NOT NULL,
    total_students INTEGER,
    correct_count INTEGER,
    score_rate DECIMAL(5,2),
    average_score DECIMAL(5,2),
    score_distribution JSONB,
    option_distribution JSONB,
    zero_score_count INTEGER,
    full_score_count INTEGER,
    difficulty_coefficient DECIMAL(3,2),
    discrimination_index DECIMAL(3,2),
    knowledge_points_mastery JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Question scores
CREATE TABLE IF NOT EXISTS {schema}.question_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES {schema}.exams(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    question_id UUID NOT NULL,
    question_type VARCHAR(20) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL,
    actual_score DECIMAL(5,2) NOT NULL,
    score_percentage DECIMAL(5,2) GENERATED ALWAYS AS (actual_score / max_score * 100) STORED,
    answer_content TEXT,
    is_correct BOOLEAN,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    knowledge_points JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning records
CREATE TABLE IF NOT EXISTS {schema}.learning_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES {schema}.students(id) NOT NULL,
    question_id UUID NOT NULL,
    grading_record_id UUID REFERENCES {schema}.card_block_grading_records(id),
    exam_id UUID REFERENCES {schema}.exams(id),
    subject VARCHAR(50) NOT NULL,
    knowledge_points JSONB,
    difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
    student_score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL,
    score_rate DECIMAL(5,2) GENERATED ALWAYS AS (student_score / max_score * 100) STORED,
    mastery_level VARCHAR(20) CHECK (mastery_level IN ('excellent', 'good', 'fair', 'poor', 'not_mastered')),
    learning_suggestions JSONB,
    recommended_exercises JSONB,
    improvement_areas JSONB,
    historical_comparison JSONB,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, question_id, exam_id)
);

-- Learning record versions
CREATE TABLE IF NOT EXISTS {schema}.learning_record_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    learning_record_id UUID REFERENCES {schema}.learning_records(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    change_reason VARCHAR(100),
    changed_fields JSONB,
    previous_data JSONB,
    changed_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- EXCEPTION HANDLING
-- =============================================

-- Student ID exceptions
CREATE TABLE IF NOT EXISTS {schema}.student_id_exceptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id UUID REFERENCES {schema}.paper_scans(id) ON DELETE CASCADE,
    detected_student_id VARCHAR(50),
    exception_type VARCHAR(30) CHECK (exception_type IN ('unrecognized', 'blurred', 'missing', 'invalid', 'duplicate')),
    suggested_students JSONB,
    confirmed_student_id UUID REFERENCES {schema}.students(id),
    confirmed_by UUID REFERENCES public.users(id),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    resolution_method VARCHAR(20) CHECK (resolution_method IN ('auto_match', 'manual_input', 'name_match', 'teacher_confirm')),
    processing_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Paper scan exceptions
CREATE TABLE IF NOT EXISTS {schema}.paper_scan_exceptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id UUID REFERENCES {schema}.paper_scans(id) ON DELETE CASCADE,
    exception_type VARCHAR(30) CHECK (exception_type IN ('duplicate', 'blank', 'blurred', 'damaged', 'orientation', 'other')),
    exception_description TEXT,
    auto_detected BOOLEAN DEFAULT TRUE,
    confidence_score DECIMAL(3,2),
    resolution_status VARCHAR(20) CHECK (resolution_status IN ('pending', 'resolved', 'ignored')) DEFAULT 'pending',
    resolved_by UUID REFERENCES public.users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- HOMEWORK MANAGEMENT
-- =============================================

-- Homework table
CREATE TABLE IF NOT EXISTS {schema}.homework (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_name VARCHAR(100) NOT NULL,
    homework_type VARCHAR(20) CHECK (homework_type IN ('single', 'joint')),
    grade_level VARCHAR(50) NOT NULL,
    description TEXT,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) CHECK (status IN ('draft', 'published', 'in_progress', 'completed')) DEFAULT 'draft',
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Homework subjects
CREATE TABLE IF NOT EXISTS {schema}.homework_subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_id UUID REFERENCES {schema}.homework(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES public.subjects(id),
    paper_template_id UUID REFERENCES public.exam_papers(id),
    total_score DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Homework students
CREATE TABLE IF NOT EXISTS {schema}.homework_students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_id UUID REFERENCES {schema}.homework(id) ON DELETE CASCADE,
    student_id UUID REFERENCES {schema}.students(id) ON DELETE CASCADE,
    class_id UUID REFERENCES {schema}.administrative_classes(id),
    is_submitted BOOLEAN DEFAULT FALSE,
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =============================================

-- Organizational structure indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_subject_groups_subject ON {schema}.subject_groups(subject);
CREATE INDEX IF NOT EXISTS idx_{schema}_subject_groups_leader ON {schema}.subject_groups(leader_user_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_administrative_classes_grade ON {schema}.administrative_classes(grade_level_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_administrative_classes_code ON {schema}.administrative_classes(code);
CREATE INDEX IF NOT EXISTS idx_{schema}_teaching_classes_subject_group ON {schema}.teaching_classes(subject_group_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teaching_classes_teacher ON {schema}.teaching_classes(teacher_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teaching_classes_code ON {schema}.teaching_classes(code);
CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_user ON {schema}.user_identities(user_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_role ON {schema}.user_identities(role_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_target ON {schema}.user_identities(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_user_identities_unique_combo ON {schema}.user_identities(user_id, role_id, target_id);

-- Student management indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_students_administrative_class ON {schema}.students(administrative_class_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_students_grade ON {schema}.students(grade_level_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_students_user ON {schema}.students(user_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_students_student_id ON {schema}.students(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_students_status ON {schema}.students(status);
CREATE INDEX IF NOT EXISTS idx_{schema}_student_teaching_classes_student ON {schema}.student_teaching_classes(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_student_teaching_classes_teaching_class ON {schema}.student_teaching_classes(class_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_student_profile_levels_student ON {schema}.student_profile_levels(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_student_profile_tags_student ON {schema}.student_profile_tags(student_id);

-- Teacher management indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_tenant_id ON {schema}.teachers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_user_id ON {schema}.teachers(user_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_employee_id ON {schema}.teachers(employee_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_teacher_name ON {schema}.teachers(teacher_name);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_employment_status ON {schema}.teachers(employment_status);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_is_active ON {schema}.teachers(is_active);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_teaching_subjects ON {schema}.teachers USING GIN(teaching_subjects);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_homeroom_class_id ON {schema}.teachers(homeroom_class_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_grade_level_id ON {schema}.teachers(grade_level_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_teachers_subject_group_id ON {schema}.teachers(subject_group_id);

-- Exam management indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_exams_exam_type ON {schema}.exams(exam_type);
CREATE INDEX IF NOT EXISTS idx_{schema}_exams_status ON {schema}.exams(status);
CREATE INDEX IF NOT EXISTS idx_{schema}_exams_date ON {schema}.exams(start_time);
CREATE INDEX IF NOT EXISTS idx_{schema}_exams_creator ON {schema}.exams(created_by);
CREATE INDEX IF NOT EXISTS idx_{schema}_exam_subjects_exam ON {schema}.exam_subjects(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_exam_students_exam ON {schema}.exam_students(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_exam_students_student ON {schema}.exam_students(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_exam ON {schema}.paper_scans(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_student ON {schema}.paper_scans(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_quality ON {schema}.paper_scans(scan_quality);
CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scans_abnormal ON {schema}.paper_scans(is_abnormal);

-- Grading indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_assignments_exam ON {schema}.grading_assignments(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_assignments_grader ON {schema}.grading_assignments(grader_user_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_assignments_status ON {schema}.grading_assignments(status);
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_exam ON {schema}.grading_records(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_student ON {schema}.grading_records(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_grader ON {schema}.grading_records(grader_user_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_grading_records_question ON {schema}.grading_records(question_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_card_block_grading_records_exam ON {schema}.card_block_grading_records(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_card_block_grading_records_student ON {schema}.card_block_grading_records(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_ai_grading_records_exam ON {schema}.ai_grading_records(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_ai_grading_records_student ON {schema}.ai_grading_records(student_id);

-- Academic statistics indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_academic_stats_exam ON {schema}.academic_statistics(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_academic_stats_student ON {schema}.academic_statistics(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_academic_stats_subject ON {schema}.academic_statistics(subject);
CREATE INDEX IF NOT EXISTS idx_{schema}_question_analysis_exam ON {schema}.question_analysis(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_question_analysis_question ON {schema}.question_analysis(question_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_question_scores_exam ON {schema}.question_scores(exam_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_question_scores_student ON {schema}.question_scores(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_question_scores_question ON {schema}.question_scores(question_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_learning_records_student ON {schema}.learning_records(student_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_learning_records_question ON {schema}.learning_records(question_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_learning_records_exam ON {schema}.learning_records(exam_id);

-- Homework indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_homework_status ON {schema}.homework(status);
CREATE INDEX IF NOT EXISTS idx_{schema}_homework_date ON {schema}.homework(start_time);
CREATE INDEX IF NOT EXISTS idx_{schema}_homework_creator ON {schema}.homework(created_by);
CREATE INDEX IF NOT EXISTS idx_{schema}_homework_subjects_homework ON {schema}.homework_subjects(homework_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_homework_students_homework ON {schema}.homework_students(homework_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_homework_students_student ON {schema}.homework_students(student_id);

-- Exception handling indexes
CREATE INDEX IF NOT EXISTS idx_{schema}_student_id_exceptions_scan ON {schema}.student_id_exceptions(scan_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_student_id_exceptions_type ON {schema}.student_id_exceptions(exception_type);
CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_exceptions_scan ON {schema}.paper_scan_exceptions(scan_id);
CREATE INDEX IF NOT EXISTS idx_{schema}_paper_scan_exceptions_status ON {schema}.paper_scan_exceptions(resolution_status);