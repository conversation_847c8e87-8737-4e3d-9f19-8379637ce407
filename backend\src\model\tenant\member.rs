use chrono::NaiveDateTime;
use serde::{Serialize};
use sqlx::FromRow;
use uuid::Uuid;

// 数据库模型
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize)]
pub struct Member {
    pub id: Uuid,
    pub name: String,
    pub status: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: NaiveDateTime,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, FromRow, Serialize)]
pub struct Student {
    pub id: Uuid,
    pub member_id: Uuid,
    #[serde(rename = "studentNo")]
    pub student_no: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize)]
pub struct Teacher {
    pub id: Uuid,
    #[serde(rename = "memberId")]
    pub member_id: Uuid,
    #[serde(rename = "teacherNo")]
    pub teacher_no: Option<String>,
}

// 返回给前端的VO结构
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize)]
pub struct StudentVO {
    pub id: Uuid,
    pub name: String,
    pub status: Option<String>,
    #[serde(rename = "studentNo")]
    pub student_no: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: NaiveDateTime,
}

#[derive(Debug, Clone, FromRow, Serialize)]
pub struct TeacherVO {
    pub id: Uuid,
    pub name: String,
    pub status: Option<String>,
    #[serde(rename = "teacherNo")]
    pub teacher_no: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: NaiveDateTime,
}
