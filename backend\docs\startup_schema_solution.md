# 程序启动时 Schema 不存在的完整解决方案

## 问题描述

在多租户系统中，程序启动时经常遇到以下问题：

1. **编译时错误**: `sqlx::query!` 宏在编译时连接数据库验证 SQL，但租户 schema 可能不存在
2. **动态租户**: 新租户的 schema 在程序运行时才创建
3. **开发环境**: 开发者本地数据库可能没有完整的租户数据
4. **CI/CD**: 构建环境中数据库状态不确定

## 解决方案架构

### 1. 启动时 Schema 初始化

**文件**: `backend/src/service/startup_schema_service.rs`

在程序启动时自动检查和创建必要的 schema：

```rust
// 在 main.rs 中调用
crate::service::startup_schema_service::StartupSchemaInitializer::initialize_for_compilation(&pool)
    .await?;
```

**功能**:
- 检查所有已注册租户的 schema 是否存在
- 自动创建缺失的 schema
- 为开发环境创建默认租户
- 创建编译时需要的测试 schema

### 2. 编译时安全的查询服务

**文件**: `backend/src/service/compile_safe_query_service.rs`

使用 `sqlx::query` 替代 `sqlx::query!` 来避免编译时检查：

```rust
let students = compile_safe_query
    .get_students_safe("tenant_001", Some(10), Some(0))
    .await?;
```

**优点**:
- 避免编译时 schema 依赖
- 保持运行时类型安全
- 提供常用查询的封装方法

### 3. 管理的查询服务

**文件**: `backend/src/service/schema_manager.rs`

自动确保 schema 存在后执行查询：

```rust
let students = managed_query
    .execute_tenant_query("tenant_001", |tx| {
        Box::pin(async move {
            sqlx::query("SELECT * FROM students")
                .fetch_all(&mut **tx)
                .await
        })
    })
    .await?;
```

**优点**:
- 运行时自动创建 schema
- 智能缓存机制
- 事务安全

### 4. 配置驱动的策略

**文件**: `backend/src/config/schema_config.rs`

不同环境使用不同的初始化策略：

```bash
# 开发环境
RUST_ENV=development
SCHEMA_AUTO_CREATE=true
SCHEMA_WARMUP_STRATEGY=lazy

# 生产环境
RUST_ENV=production
SCHEMA_AUTO_CREATE=false
SCHEMA_WARMUP_STRATEGY=all
```

## 使用指南

### 1. 程序启动配置

在 `main.rs` 中添加启动时初始化：

```rust
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // ... 数据库连接和迁移 ...
    
    // 初始化启动时需要的 Schema
    crate::service::startup_schema_service::StartupSchemaInitializer::initialize_for_compilation(&pool)
        .await?;
    
    // ... 其他初始化 ...
}
```

### 2. 服务层使用

#### 方法 A: 编译时安全查询（推荐）

```rust
use crate::service::compile_safe_query_service::CompileSafeQueryService;

pub struct StudentService {
    compile_safe_query: CompileSafeQueryService,
}

impl StudentService {
    pub async fn get_students(&self, tenant_schema: &str) -> Result<Vec<StudentRow>> {
        self.compile_safe_query
            .get_students_safe(tenant_schema, Some(100), Some(0))
            .await
    }
}
```

#### 方法 B: 管理的查询服务

```rust
use crate::service::schema_manager::ManagedQueryService;

pub struct StudentService {
    managed_query: ManagedQueryService,
}

impl StudentService {
    pub async fn create_student(&self, tenant_schema: &str, request: CreateStudentRequest) -> Result<StudentRow> {
        self.managed_query
            .execute_tenant_query(tenant_schema, |tx| {
                Box::pin(async move {
                    // 使用 sqlx::query 而不是 sqlx::query!
                    let row = sqlx::query(
                        "INSERT INTO students (id, name, student_number) VALUES ($1, $2, $3) RETURNING *"
                    )
                    .bind(Uuid::new_v4())
                    .bind(&request.name)
                    .bind(&request.student_number)
                    .fetch_one(&mut **tx)
                    .await?;
                    
                    Ok(StudentRow::from_row(&row)?)
                })
            })
            .await
    }
}
```

### 3. 环境配置

#### 开发环境 (.env.development)

```bash
RUST_ENV=development
SCHEMA_AUTO_CREATE=true
SCHEMA_WARMUP_STRATEGY=lazy
TENANT_TEMPLATE_PATH=tenants/template/init_tenant_schema.migrations_temp
```

**特点**:
- 自动创建缺失的 schema
- 懒加载策略，快速启动
- 自动清理测试数据

#### 生产环境 (.env.production)

```bash
RUST_ENV=production
SCHEMA_AUTO_CREATE=false
SCHEMA_WARMUP_STRATEGY=all
TENANT_TEMPLATE_PATH=/app/templates/init_tenant_schema.migrations_temp
```

**特点**:
- 不自动创建 schema（安全考虑）
- 预热所有 schema
- 完整的启动检查

#### 测试环境 (.env.test)

```bash
RUST_ENV=test
SCHEMA_AUTO_CREATE=true
SCHEMA_WARMUP_STRATEGY=none
SCHEMA_ENABLE_CACHE=false
```

**特点**:
- 每次测试都是干净环境
- 不进行预热
- 不使用缓存

## 最佳实践

### 1. 避免使用 `sqlx::query!` 宏

在多租户环境中，优先使用：

```rust
// ✅ 推荐：使用 sqlx::query
let rows = sqlx::query("SELECT * FROM students WHERE id = $1")
    .bind(student_id)
    .fetch_all(&pool)
    .await?;

// ❌ 避免：使用 sqlx::query!（编译时可能失败）
let rows = sqlx::query!("SELECT * FROM students WHERE id = $1", student_id)
    .fetch_all(&pool)
    .await?;
```

### 2. 使用类型安全的包装

创建自定义的 `FromRow` 实现：

```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct StudentRow {
    pub id: Uuid,
    pub name: String,
    pub student_number: String,
}

impl FromRow<'_, sqlx::postgres::PgRow> for StudentRow {
    fn from_row(row: &sqlx::postgres::PgRow) -> sqlx::Result<Self> {
        Ok(Self {
            id: row.try_get("id")?,
            name: row.try_get("name")?,
            student_number: row.try_get("student_number")?,
        })
    }
}
```

### 3. 错误处理

提供清晰的错误信息：

```rust
.map_err(|e| match e {
    sqlx::Error::RowNotFound => AppError::NotFound("Student not found".to_string()),
    sqlx::Error::Database(db_err) if db_err.code() == Some("42P01") => {
        AppError::SchemaError(format!("Table does not exist in schema: {}", tenant_schema))
    },
    _ => AppError::DatabaseError(format!("Database operation failed: {}", e)),
})
```

### 4. 性能优化

- 使用连接池
- 启用 schema 缓存
- 批量操作替代单个操作
- 合理设置预热策略

## 故障排除

### 1. 编译错误：表不存在

**错误**: `error: error returned from database: relation "tenant_xxx.students" does not exist`

**解决方案**:
1. 确保在 `main.rs` 中调用了启动时初始化
2. 检查模板文件路径是否正确
3. 使用编译时安全的查询服务

### 2. 启动时间过长

**原因**: 预热策略设置为 `all`，但有大量租户

**解决方案**:
1. 改为 `lazy` 策略
2. 增加并发创建数量
3. 优化模板 SQL

### 3. Schema 创建失败

**常见原因**:
1. 数据库权限不足
2. 模板文件语法错误
3. 并发创建冲突

**调试方法**:
1. 检查日志中的详细错误
2. 手动执行模板 SQL
3. 验证数据库权限

## 总结

这套解决方案通过以下方式解决了程序启动时 schema 不存在的问题：

1. **启动时初始化**: 自动检查和创建必要的 schema
2. **编译时安全**: 使用 `sqlx::query` 避免编译时依赖
3. **运行时保障**: 管理的查询服务确保 schema 存在
4. **配置驱动**: 不同环境使用不同策略
5. **类型安全**: 通过自定义类型保持安全性

通过这套方案，你可以在保持代码质量的同时，解决多租户环境下的编译和运行时问题。
