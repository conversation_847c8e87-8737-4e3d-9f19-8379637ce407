1.考试设置可选到班级下的学生（可选）
2.学生Profile分级标签
3.配置考试时间和地点（可选）
4.评分标准在阅卷中都可以更改，保留版本号，是否重阅由有权用户控制
5.删除三重评阅
6.试卷扫描上传（包括minio存储），按纸张存储（正/反页），关注处理重复/空白、异常试卷（人工复核）：
    1）.上传图片 2）. 扫描仪上传（android启辰适配,第三方）
7.学号异常：任课老师确认，系统可根据当前有效信息快速过滤并绑定学生
8.评分监控：多个评分老师对同一题的评分分布，按题/阅卷员 统计
9.AI 阅卷记录：ai agent id,版本，模型结果，时间
10.阅卷记录
11.记录每题的成绩
12.评阅分发：按量分配，AI 评阅分发：按题按量
13.开始/暂停阅卷，可按题暂停
14.简化成绩发布
15.学情：每个学生成绩/排名/缺考/平均分，每个题得分率/平均分/选择题选项分布/大题0分满分考生
