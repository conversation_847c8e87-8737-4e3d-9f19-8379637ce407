use std::collections::HashSet;
use sqlx::PgPool;
use uuid::Uuid;
use crate::{model::homework_papers::homework_papers::{BindPapersToHomeworkParams, HomeworkPapers}, utils::schema::connect_with_schema};
#[derive(Clone)]
pub struct HomeworkPapersService{
    db_pool: PgPool,
}
impl HomeworkPapersService{
    pub fn new(db_pool: PgPool)-> Self{
        Self{db_pool}
    }
}
impl HomeworkPapersService {
    /**
     * 作者：朱若彪
     * 说明：绑定一个试卷到作业中
     */
    pub async fn bind_papers_to_homework(
        &self,
        schema_name: &String,
        params: &BindPapersToHomeworkParams,
    )->Result<Vec<HomeworkPapers>, String>{
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        let BindPapersToHomeworkParams{
            homework_id,
            paper_ids,
        } = params;
        //查询当前papers是否已被关联
        let refs= self.find_all_by_homework_id(schema_name, &homework_id)
        .await?;
        //去掉已经有的关联
        let mut paper_id_set: HashSet<Uuid> = paper_ids.clone().into_iter().collect();
        for ele in refs {
            paper_id_set.remove(&ele.paper_id);
        }
        if paper_id_set.len() == 0 {
            return Ok(vec![]);
        }
        //新建
        let mut builder = sqlx::QueryBuilder::new(
            "insert into homework_papers (homework_id,paper_id) values",
        );
        for(i,ele) in paper_id_set.iter().enumerate(){
            builder
            .push("(")
            .push_bind(homework_id)
            .push(",")
            .push_bind(ele)
            .push(")");
            if i<paper_id_set.len()-1{
                builder.push(",");
            }
        }
        builder.push("RETURNING *");
        builder
            .build_query_as()
            .fetch_all(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }

    pub async fn find_all_by_homework_id(
        &self,
        schema_name: &String,
        homework_id: &Uuid,
    )->Result<Vec<HomeworkPapers>, String>{
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        sqlx::query_as::<_, HomeworkPapers>(
            "SELECT * FROM homework_papers WHERE homework_id = $1",
        )
        .bind(homework_id)
        .fetch_all(&mut *conn)
        .await
        .map_err(|e| e.to_string())
    }

}
