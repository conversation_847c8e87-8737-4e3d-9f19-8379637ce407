pub mod administrative_classes;
pub mod analysis;
pub mod base;
pub mod classes;
pub mod education_stage;
pub mod exam;
pub mod grade;
pub mod grading;
pub mod homework;
pub mod homework_students;
pub mod homework_papers;
pub mod question;
pub mod role;
pub mod student;
pub mod subject;
pub mod subject_groups;
pub mod teacher;
pub mod teaching_aids;
pub mod teaching_classes;
pub mod tenant;
pub mod user;
pub mod workflow;
pub mod paper;

// Re-export commonly used types
pub use base::{PageParams, PageResult};

// Re-export auth types from user module
pub use user::auth;

// Re-export other commonly used types
pub use user::user::*;

// Re-export textbooks from teaching_aids
pub use teaching_aids::textbooks;

// Re-export role types
pub use role::*;

// Re-export subject types
pub use subject::*;

// Re-export grade types
pub use grade::*;

// Re-export education_stage types
pub use education_stage::*;

// Re-export student types
pub use student::student::*;

// Re-export teacher types
pub use teacher::*;
