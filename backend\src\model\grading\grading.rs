use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScan {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub paper_sequence: i32,
    pub front_page_url: Option<String>,
    pub back_page_url: Option<String>,
    pub scan_quality: i32, // 1-10
    pub scan_method: String, // 'image_upload', 'scanner_direct', 'third_party'
    pub scan_device: Option<String>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub needs_review: bool,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub minio_bucket: String,
    pub minio_object_key: String,
    pub file_size: i64,
    pub scan_timestamp: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct AnswerCardBlock {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub block_name: String,
    pub block_type: String, // 'single_question', 'multi_question', 'composite'
    pub position_info: serde_json::Value,
    pub template_version: Option<String>,
    pub max_score: f32,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct CardBlockQuestionLink {
    pub id: Uuid,
    pub card_block_id: Uuid,
    pub question_id: Uuid,
    pub link_type: String, // 'one_to_one', 'one_to_many', 'many_to_one', 'many_to_many'
    pub weight_ratio: f32,
    pub score_mapping: serde_json::Value,
    pub is_primary: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct CardBlockGradingRecord {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub card_block_id: Uuid,
    pub grader_user_id: Uuid,
    pub raw_score: f32,
    pub adjusted_score: Option<f32>,
    pub grading_method: String, // 'manual', 'ai', 'hybrid'
    pub confidence_score: Option<f32>,
    pub grading_notes: Option<String>,
    pub quality_level: String, // 'excellent', 'good', 'fair', 'poor'
    pub is_reviewed: bool,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub grading_duration: Option<i32>, // in seconds
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GradingStatistics {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub question_id: Uuid,
    pub question_type: String,
    pub grader_user_id: Uuid,
    pub total_papers: i32,
    pub avg_score: f32,
    pub min_score: f32,
    pub max_score: f32,
    pub score_distribution: serde_json::Value,
    pub consistency_score: f32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AIGradingRecord {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub question_id: Uuid,
    pub ai_agent_id: String,
    pub ai_model_version: String,
    pub ai_model_result: serde_json::Value,
    pub confidence_score: f32,
    pub processing_time: i32, // in milliseconds
    pub error_message: Option<String>,
    pub reviewed_by_human: bool,
    pub human_reviewer_id: Option<Uuid>,
    pub human_review_result: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GradingControlState {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub question_id: Option<Uuid>,
    pub grader_user_id: Option<Uuid>,
    pub control_level: String, // 'global', 'question', 'grader'
    pub control_action: String, // 'start', 'pause', 'resume', 'stop'
    pub control_reason: Option<String>,
    pub controlled_by: Uuid,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentIdException {
    pub id: Uuid,
    pub scan_id: Uuid,
    pub detected_student_id: Option<String>,
    pub exception_type: String, // 'unrecognized', 'blurred', 'missing', 'invalid', 'duplicate'
    pub suggested_students: serde_json::Value,
    pub confirmed_student_id: Option<Uuid>,
    pub confirmed_by: Option<Uuid>,
    pub confirmed_at: Option<DateTime<Utc>>,
    pub resolution_method: String, // 'auto_match', 'manual_input', 'name_match', 'teacher_confirm'
    pub processing_notes: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PaperScanException {
    pub id: Uuid,
    pub scan_id: Uuid,
    pub exception_type: String, // 'duplicate', 'blank', 'blurred', 'damaged', 'orientation', 'other'
    pub exception_description: Option<String>,
    pub auto_detected: bool,
    pub confidence_score: f32,
    pub resolution_status: String, // 'pending', 'resolved', 'ignored'
    pub resolved_by: Option<Uuid>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub resolution_notes: Option<String>,
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct UploadPaperRequest {
    pub exam_id: Uuid,
    pub student_id: Option<Uuid>,
    pub paper_sequence: i32,
    pub scan_method: String,
    pub scan_device: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateAnswerCardBlockRequest {
    pub exam_id: Uuid,
    pub block_name: String,
    pub block_type: String,
    pub position_info: serde_json::Value,
    pub max_score: f32,
    pub questions: Vec<CardBlockQuestionRequest>,
    pub question_links: Vec<CardBlockQuestionRequest>,
}

#[derive(Debug, Deserialize)]
pub struct CardBlockQuestionRequest {
    pub question_id: Uuid,
    pub link_type: String,
    pub weight_ratio: f32,
    pub score_mapping: serde_json::Value,
    pub is_primary: bool,
}

#[derive(Debug, Deserialize)]
pub struct GradeCardBlockRequest {
    pub card_block_id: Uuid,
    pub student_id: Uuid,
    pub raw_score: f32,
    pub grading_method: String,
    pub confidence_score: Option<f32>,
    pub grading_notes: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct ControlGradingRequest {
    pub exam_id: Uuid,
    pub question_id: Option<Uuid>,
    pub grader_user_id: Option<Uuid>,
    pub control_level: String,
    pub control_action: String,
    pub control_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct ResolveStudentIdExceptionRequest {
    pub exception_id: Uuid,
    pub confirmed_student_id: Uuid,
    pub resolution_method: String,
    pub processing_notes: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct ResolvePaperScanExceptionRequest {
    pub exception_id: Uuid,
    pub resolution_status: String,
    pub resolution_notes: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct GradingQueryParams {
    pub exam_id: Option<Uuid>,
    pub student_id: Option<Uuid>,
    pub grader_id: Option<Uuid>,
    pub question_id: Option<Uuid>,
    pub grading_method: Option<String>,
    pub quality_level: Option<String>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct ScanQueryParams {
    pub exam_id: Option<Uuid>,
    pub student_id: Option<Uuid>,
    pub scan_quality_min: Option<i32>,
    pub scan_quality_max: Option<i32>,
    pub is_abnormal: Option<bool>,
    pub needs_review: Option<bool>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct PaperScanResponse {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub student_name: String,
    pub paper_sequence: i32,
    pub front_page_url: Option<String>,
    pub back_page_url: Option<String>,
    pub scan_quality: i32,
    pub scan_method: String,
    pub scan_device: Option<String>,
    pub is_duplicate: bool,
    pub is_blank: bool,
    pub is_abnormal: bool,
    pub abnormal_reason: Option<String>,
    pub needs_review: bool,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub file_size: i64,
    pub scan_timestamp: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub exceptions: Vec<PaperScanException>,
    pub student_id_exceptions: Vec<StudentIdException>,
}

#[derive(Debug, Serialize)]
pub struct GradingProgressResponse {
    pub exam_id: Uuid,
    pub exam_name: String,
    pub total_papers: i64,
    pub scanned_papers: i64,
    pub graded_papers: i64,
    pub reviewed_papers: i64,
    pub completion_percentage: f32,
    pub ai_grading_count: i64,
    pub manual_grading_count: i64,
    pub exception_count: i64,
    pub quality_distribution: serde_json::Value,
    pub grader_statistics: Vec<GraderStatistics>,
}

#[derive(Debug, Serialize)]
pub struct GraderStatistics {
    pub grader_id: Uuid,
    pub grader_name: String,
    pub assigned_papers: i64,
    pub completed_papers: i64,
    pub average_score: f32,
    pub average_time: f32,
    pub consistency_score: f32,
    pub quality_level: String,
}

#[derive(Debug, Serialize)]
pub struct AnswerCardBlockResponse {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub block_name: String,
    pub block_type: String,
    pub position_info: serde_json::Value,
    pub template_version: Option<String>,
    pub max_score: f32,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub questions: Vec<CardBlockQuestionLink>,
    pub grading_records: Vec<CardBlockGradingRecord>,
}

#[derive(Debug, Serialize)]
pub struct GradingStatisticsResponse {
    pub exam_id: Uuid,
    pub question_statistics: Vec<QuestionStatistics>,
    pub grader_statistics: Vec<GraderStatistics>,
    pub overall_statistics: OverallGradingStatistics,
}

#[derive(Debug, Serialize)]
pub struct QuestionStatistics {
    pub question_id: Uuid,
    pub question_type: String,
    pub total_graded: i64,
    pub average_score: f32,
    pub score_distribution: serde_json::Value,
    pub difficulty_coefficient: f32,
    pub discrimination_index: f32,
    pub grader_consistency: f32,
}

#[derive(Debug, Serialize)]
pub struct OverallGradingStatistics {
    pub total_questions: i64,
    pub total_graded: i64,
    pub average_consistency: f32,
    pub quality_distribution: serde_json::Value,
    pub grading_method_distribution: serde_json::Value,
    pub time_statistics: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct ExceptionSummary {
    pub scan_exceptions: i64,
    pub student_id_exceptions: i64,
    pub grading_exceptions: i64,
    pub resolved_exceptions: i64,
    pub pending_exceptions: i64,
    pub exception_types: serde_json::Value,
}

// Request types
#[derive(Debug, Deserialize)]
pub struct PaperScanBatchRequest {
    pub exam_id: Uuid,
    pub scans: Vec<CreatePaperScanRequest>,
}

#[derive(Debug, Deserialize)]
pub struct CreatePaperScanRequest {
    pub student_id: Uuid,
    pub paper_sequence: i32,
    pub front_page_url: Option<String>,
    pub back_page_url: Option<String>,
    pub scan_quality: i32,
    pub scan_method: String,
    pub scan_device: Option<String>,
    pub file_size: i64,
    pub exam_id: Uuid
}

#[derive(Debug, Deserialize)]
pub struct PaperScanQueryParams {
    pub exam_id: Option<Uuid>,
    pub student_id: Option<Uuid>,
    pub min_quality: Option<i32>,
    pub needs_review: Option<bool>,
    pub is_abnormal: Option<bool>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateAnswerCardBlocksRequest {
    pub exam_id: Uuid,
    pub blocks: Vec<CreateAnswerCardBlockRequest>,
}

// CreateAnswerCardBlockRequest is already defined above

#[derive(Debug, Deserialize)]
pub struct GradingAssignmentRequest {
    pub exam_id: Uuid,
    pub grader_id: Uuid,
    pub question_ids: Vec<Uuid>,
    pub assignment_type: String,
    pub assignments: Vec<GradingAssignmentItem>,
}

#[derive(Debug, Deserialize)]
pub struct GradingAssignmentItem {
    pub question_id: Uuid,
    pub student_ids: Vec<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct GradingAssignmentQueryParams {
    pub exam_id: Option<Uuid>,
    pub grader_id: Option<Uuid>,
    pub grader_user_id: Option<Uuid>,
    pub priority_level: Option<String>,
    pub status: Option<String>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct SubmitGradingRecordRequest {
    pub assignment_id: Uuid,
    pub student_id: Uuid,
    pub question_id: Uuid,
    pub score: f32,
    pub comments: Option<String>,
    pub grading_method: String,
}

#[derive(Debug, Deserialize)]
pub struct SubmitCardBlockGradingRequest {
    pub card_block_id: Uuid,
    pub student_id: Uuid,
    pub score: f32,
    pub comments: Option<String>,
    pub grading_method: String,
}

#[derive(Debug, Deserialize)]
pub struct ProcessAIGradingRequest {
    pub exam_id: Uuid,
    pub question_ids: Vec<Uuid>,
    pub ai_model: String,
    pub confidence_threshold: f32,
    pub ai_gradings: Vec<AIGradingRequest>,
}

#[derive(Debug, Deserialize)]
pub struct AIGradingRequest {
    pub question_id: Uuid,
    pub student_id: Uuid,
    pub ai_score: f32,
    pub confidence: f32,
    pub ai_model: String,
    pub needs_review: bool,
}

#[derive(Debug, Deserialize)]
pub struct GradingControlRequest {
    pub exam_id: Uuid,
    pub action: String, // 'start', 'pause', 'resume', 'stop'
    pub grading_mode: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct HandleScanExceptionRequest {
    pub scan_id: Uuid,
    pub exception_type: String,
    pub resolution_type: String,
    pub resolution_data: serde_json::Value,
    pub confirmed_student_id: Option<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct GradingStatisticsQueryParams {
    pub exam_id: Option<Uuid>,
    pub grader_id: Option<Uuid>,
    pub grader_user_id: Option<Uuid>,
    pub question_id: Option<Uuid>,
    pub date_from: Option<DateTime<Utc>>,
    pub date_to: Option<DateTime<Utc>>,
}

// Response types
#[derive(Debug, Serialize)]
pub struct PaperScanListResponse {
    pub scans: Vec<PaperScanResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
}

#[derive(Debug, Serialize)]
pub struct GradingAssignmentResponse {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub grader_id: Uuid,
    pub grader_name: String,
    pub question_count: i32,
    pub completed_count: i32,
    pub status: String,
    pub assigned_at: DateTime<Utc>,
    pub deadline: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct GradingAssignmentListResponse {
    pub assignments: Vec<GradingAssignmentResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
}

#[derive(Debug, Serialize)]
pub struct GradingRecordResponse {
    pub id: Uuid,
    pub assignment_id: Uuid,
    pub student_id: Uuid,
    pub student_name: String,
    pub question_id: Uuid,
    pub score: f32,
    pub max_score: f32,
    pub comments: Option<String>,
    pub grading_method: String,
    pub graded_at: DateTime<Utc>,
    pub graded_by: Uuid,
}

#[derive(Debug, Serialize)]
pub struct CardBlockGradingRecordResponse {
    pub id: Uuid,
    pub card_block_id: Uuid,
    pub student_id: Uuid,
    pub student_name: String,
    pub score: f32,
    pub max_score: f32,
    pub comments: Option<String>,
    pub grading_method: String,
    pub graded_at: DateTime<Utc>,
    pub graded_by: Uuid,
}

#[derive(Debug, Serialize)]
pub struct AIGradingRecordResponse {
    pub id: Uuid,
    pub question_id: Uuid,
    pub student_id: Uuid,
    pub ai_score: f32,
    pub confidence: f32,
    pub ai_model: String,
    pub needs_review: bool,
    pub processed_at: DateTime<Utc>,
    pub ai_agent_id: String,
    pub ai_model_version: String,
    pub ai_model_result: serde_json::Value,
    pub confidence_score: f32,
    pub processing_time: i32,
    pub error_message: Option<String>,
    pub reviewed_by_human: bool,
    pub human_reviewer_id: Option<Uuid>,
}

#[derive(Debug, Serialize)]
pub struct GradingControlStateResponse {
    pub exam_id: Uuid,
    pub current_state: String,
    pub grading_mode: String,
    pub progress: f32,
    pub started_at: Option<DateTime<Utc>>,
    pub estimated_completion: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct ScanExceptionResponse {
    pub id: Uuid,
    pub exception_id: Uuid,
    pub scan_id: Uuid,
    pub exception_type: String,
    pub description: String,
    pub resolution_status: String,
    pub resolution_notes: Option<String>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub resolved_by: Option<Uuid>,
}

// Additional entity types
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GradingAssignment {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub grader_id: Uuid,
    pub question_ids: serde_json::Value,
    pub assignment_type: String,
    pub status: String,
    pub assigned_at: DateTime<Utc>,
    pub deadline: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct GradingRecord {
    pub id: Uuid,
    pub assignment_id: Uuid,
    pub student_id: Uuid,
    pub question_id: Uuid,
    pub score: f32,
    pub max_score: f32,
    pub comments: Option<String>,
    pub grading_method: String,
    pub graded_at: DateTime<Utc>,
    pub graded_by: Uuid,
}