import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { createHomework, getHomeworkById, updateHomework } from '../../services/homeworkService';
import { Homework, CreateHomeworkPayload } from '../../types/homework';

const HomeworkForm: React.FC = () => {
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const [homework, setHomework] = useState<Partial<CreateHomeworkPayload>>({
    name: '',
    type: 'single',
    grade_level: '',
    description: '',
    start_time: new Date().toISOString(),
    end_time: new Date().toISOString(),
    status: 'draft',
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const isEditMode = Boolean(id);

  useEffect(() => {
    if (isEditMode) {
      const fetchHomework = async () => {
        try {
          setLoading(true);
          const data = await getHomeworkById(id!);
          setHomework(data);
        } catch (err) {
          setError('Failed to fetch Homework');
        } finally {
          setLoading(false);
        }
      };
      fetchHomework();
    }
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setHomework(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isEditMode) {
        await updateHomework(id!, homework as Homework);
      } else {
        await createHomework(homework as CreateHomeworkPayload);
      }
      navigate('/homeworks');
    } catch (err) {
      setError('Failed to save Homework');
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) return <div>Loading...</div>;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">{isEditMode ? 'Edit' : 'Create'} Homework</h1>
      {error && <div className="bg-red-100 text-red-700 p-2 mb-4 rounded">{error}</div>}
      <form onSubmit={handleSubmit} className="bg-white p-4 rounded shadow">
        <div className="mb-4">
          <label className="block text-gray-700">Name</label>
          <input
            type="text"
            name="name"
            value={homework.name}
            onChange={handleChange}
            className="w-full px-3 py-2 border rounded"
            required
          />
        </div>
        {/* Add other form fields for type, grade_level, description, etc. */}
        <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded" disabled={loading}>
          {loading ? 'Saving...' : 'Save'}
        </button>
      </form>
    </div>
  );
};

export default HomeworkForm;
