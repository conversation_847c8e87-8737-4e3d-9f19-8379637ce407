use crate::middleware::tenant_middleware::TenantExtractor;
use crate::model::{
    Student, CreateStudent, UpdateStudent, StudentDetail, StudentSearchParams,
    StudentTeachingClass, CreateStudentTeachingClass,
    StudentProfileLevel, CreateStudentProfileLevel,
    StudentProfileTag, CreateStudentProfileTag
};
use crate::service::student::student_service::StudentService;
use crate::utils::api_response::ApiResponse;
use crate::utils::error::AppError;
use axum::{
    extract::{Path, Query, State},
    routing::{get, post},
    Json, Router,
};
use uuid::Uuid;

pub fn create_router() -> Router<StudentService> {
    Router::new()
        .route("/students", get(get_students).post(create_student))
        .route("/students/{id}", get(get_student_by_id).put(update_student).delete(delete_student))
        .route("/students/{id}/detail", get(get_student_detail))
        .route("/students/{id}/teaching-classes", post(add_teaching_class))
        .route("/students/{id}/teaching-classes/{class_id}/{subject}", axum::routing::delete(remove_teaching_class))
        .route("/students/{id}/profile-levels", post(update_profile_level))
        .route("/students/{id}/profile-tags", post(update_profile_tag))
        .route("/students/{id}/profile-tags/{tag_name}", axum::routing::delete(remove_profile_tag))
        .route("/students/count/by-class/{class_id}", get(get_students_count_by_class))
        .route("/students/count/by-grade/{grade_id}", get(get_students_count_by_grade))
}

#[axum::debug_handler]
async fn get_students(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Query(params): Query<StudentSearchParams>,
) -> Result<Json<ApiResponse<Vec<Student>>>, AppError> {
    let students = student_service
        .get_students(&tenant_context.schema_name, params)
        .await?;
    Ok(Json(ApiResponse::success(students, None)))
}

#[axum::debug_handler]
async fn get_student_by_id(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
) -> Result<Json<ApiResponse<Student>>, AppError> {
    let student = student_service
        .get_student_by_id(&tenant_context.schema_name, id)
        .await?;
    if let Some(s) = student {
        Ok(Json(ApiResponse::success(s, None)))
    } else {
        Err(AppError::NotFound("Student not found".to_string()))
    }
}

#[axum::debug_handler]
async fn get_student_detail(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
) -> Result<Json<ApiResponse<StudentDetail>>, AppError> {
    let student_detail = student_service
        .get_student_detail(&tenant_context.schema_name, id)
        .await?;
    if let Some(detail) = student_detail {
        Ok(Json(ApiResponse::success(detail, None)))
    } else {
        Err(AppError::NotFound("Student not found".to_string()))
    }
}

#[axum::debug_handler]
async fn create_student(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Json(payload): Json<CreateStudent>,
) -> Result<Json<ApiResponse<Student>>, AppError> {
    let student = student_service
        .create_student(&tenant_context.schema_name, payload)
        .await?;
    Ok(Json(ApiResponse::success(
        student,
        Some("Student created successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn update_student(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateStudent>,
) -> Result<Json<ApiResponse<Student>>, AppError> {
    let student = student_service
        .update_student(&tenant_context.schema_name, id, payload)
        .await?;
    Ok(Json(ApiResponse::success(
        student,
        Some("Student updated successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn delete_student(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, AppError> {
    student_service
        .delete_student(&tenant_context.schema_name, id)
        .await?;
    Ok(Json(ApiResponse::success(
        (),
        Some("Student deleted successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn add_teaching_class(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
    Json(mut payload): Json<CreateStudentTeachingClass>,
) -> Result<Json<ApiResponse<StudentTeachingClass>>, AppError> {
    payload.student_id = id; // Ensure consistency
    let teaching_class = student_service
        .add_teaching_class(&tenant_context.schema_name, payload)
        .await?;
    Ok(Json(ApiResponse::success(
        teaching_class,
        Some("Teaching class added successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn remove_teaching_class(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path((student_id, class_id, subject)): Path<(Uuid, Uuid, String)>,
) -> Result<Json<ApiResponse<()>>, AppError> {
    student_service
        .remove_teaching_class(&tenant_context.schema_name, student_id, class_id, &subject)
        .await?;
    Ok(Json(ApiResponse::success(
        (),
        Some("Teaching class removed successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn update_profile_level(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
    Json(mut payload): Json<CreateStudentProfileLevel>,
) -> Result<Json<ApiResponse<StudentProfileLevel>>, AppError> {
    payload.student_id = id; // Ensure consistency
    let profile_level = student_service
        .update_profile_level(&tenant_context.schema_name, payload)
        .await?;
    Ok(Json(ApiResponse::success(
        profile_level,
        Some("Profile level updated successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn update_profile_tag(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(id): Path<Uuid>,
    Json(mut payload): Json<CreateStudentProfileTag>,
) -> Result<Json<ApiResponse<StudentProfileTag>>, AppError> {
    payload.student_id = id; // Ensure consistency
    let profile_tag = student_service
        .update_profile_tag(&tenant_context.schema_name, payload)
        .await?;
    Ok(Json(ApiResponse::success(
        profile_tag,
        Some("Profile tag updated successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn remove_profile_tag(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path((student_id, tag_name)): Path<(Uuid, String)>,
) -> Result<Json<ApiResponse<()>>, AppError> {
    student_service
        .remove_profile_tag(&tenant_context.schema_name, student_id, &tag_name)
        .await?;
    Ok(Json(ApiResponse::success(
        (),
        Some("Profile tag removed successfully.".to_string()),
    )))
}

#[axum::debug_handler]
async fn get_students_count_by_class(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(class_id): Path<Uuid>,
) -> Result<Json<ApiResponse<i64>>, AppError> {
    let count = student_service
        .get_students_count_by_class(&tenant_context.schema_name, class_id)
        .await?;
    Ok(Json(ApiResponse::success(count, None)))
}

#[axum::debug_handler]
async fn get_students_count_by_grade(
    TenantExtractor(tenant_context): TenantExtractor,
    State(student_service): State<StudentService>,
    Path(grade_id): Path<Uuid>,
) -> Result<Json<ApiResponse<i64>>, AppError> {
    let count = student_service
        .get_students_count_by_grade(&tenant_context.schema_name, grade_id)
        .await?;
    Ok(Json(ApiResponse::success(count, None)))
}