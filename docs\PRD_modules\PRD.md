# Deep-Mate PRD 文档索引

## 📋 文档概览

本文档集合详细描述了 Deep-Mate 考试管理平台的产品需求，包含从系统架构到功能模块的完整技术规格。

### 📊 文档统计
- **总模块数**: 8 个主要模块
- **技术架构**: Rust + React + PostgreSQL + MinIO
- **部署方式**: 多租户 SaaS 平台, 优先采用 WebAssembly (WASM)
- **更新日期**: 2025年7月

---

## 🗂️ 文档结构

### 🎯 基础架构文档

#### [1. 项目概述](./1_project_overview.md)
- **内容**: 项目背景、目标、产品定位
- **读者**: 产品经理、技术负责人、业务决策者
- **关键概念**: 多租户架构、跨校联考、智能阅卷

#### [2. 用户角色与权限](./2_user_roles_and_permissions.md)
- **内容**: 用户角色层次、权限矩阵、权限管理
- **读者**: 系统架构师、安全工程师、产品经理
- **关键概念**: RBAC权限模型、跨租户权限、身份管理

#### [3. 核心架构需求](./3_core_architecture.md)
- **内容**: 多租户架构、用户认证、权限管理体系
- **读者**: 系统架构师、后端工程师、DevOps
- **关键概念**: Schema分离、身份绑定、动态权限

---

### 🔧 功能模块文档

#### [4.0 试卷与题库管理](./4_0_paper_question_management.md)
- **内容**: 中央题库（增强版）、试卷模板、混合内容引用管理
- **读者**: 产品经理、后端工程师
- **关键功能**: 题目导入、智能组卷、版本控制、多维度题目属性
- **数据模型**: `question_bank`, `question_answers`, `question_explanations`, `question_difficulties`, `question_knowledge_points`
- **新特性**: 支持题目多答案、多解析、多难度、多知识点的全面管理

#### [4.1 考试管理系统](./4_1_exam_management.md)
- **内容**: 考试创建、联考协作、成绩管理
- **读者**: 产品经理、后端工程师、测试工程师
- **关键功能**: 单校考试、多校联考、成绩发布
- **业务流程**: 考试全生命周期管理

#### [4.2 阅卷中心](./4_2_grading_center.md)
- **内容**: 智能阅卷、扫描识别、异常处理
- **读者**: AI工程师、后端工程师、产品经理
- **关键功能**: OCR识别、AI评分、人工复核
- **技术重点**: MinIO存储、图像处理、质量控制

#### [4.3 学情分析系统](./4_3_academic_analysis.md)
- **内容**: 成绩分析、能力评估、报告生成
- **读者**: 数据工程师、产品经理
- **关键功能**: 多维度分析、个性化报告、学习记录
- **分析维度**: 个人、班级、年级、学校

#### [4.4 教辅管理系统](./4_4_teaching_aids_management.md)
- **内容**: 教辅资源、混合内容组织（知识讲解/例题/试题混排）、授权管理
- **读者**: 产品经理、后端工程师
- **关键功能**: 结构化导入、混合内容阅读顺序、版本管理、跨租户共享
- **支持格式**: JSON, XML, CSV, Excel
- **新特性**: 支持知识讲解、例题、试题的混合排列和阅读顺序控制

---

### 🛠️ 技术规格文档

#### [5. 非功能性需求](./5_non_functional_requirements.md)
- **内容**: 性能要求、安全要求、兼容性要求
- **读者**: 技术负责人、测试工程师、DevOps
- **关键指标**: 
  - 响应时间: <1s
  - 并发用户: 10万
  - 可用性: 99.9%

#### [6. 技术架构](./6_technical_architecture.md)
- **内容**: 系统架构、数据模型、API设计
- **读者**: 系统架构师、后端工程师、DBA
- **技术栈**: 
  - 前端: React + TypeScript + shadcn/ui
  - 后端: Rust + Axum + PostgreSQL
  - 存储: MinIO + Redis
  - 权限: Casbin-RS

---

## 🔗 模块间依赖关系

### 核心依赖图

```mermaid
graph TD
    A[用户角色与权限] --> B[核心架构需求]
    B --> C[试卷与题库管理]
    C --> D[考试管理系统]
    D --> E[阅卷中心]
    E --> F[学情分析系统]
    C --> G[教辅管理系统]
    G --> C
    
    H[技术架构] --> A
    H --> B
    H --> C
    H --> D
    H --> E
    H --> F
    H --> G
    
    I[非功能性需求] --> H
```

### 数据流向图

```mermaid
graph LR
    A[教辅管理] --> B[题库管理]
    B --> C[考试管理]
    C --> D[阅卷中心]
    D --> E[学情分析]
    E --> F[报告生成]
    
    G[用户管理] --> C
    G --> D
    G --> E
```

---

## 🎯 按角色推荐阅读路径

### 产品经理
1. [项目概述](./1_project_overview.md) - 了解产品定位
2. [用户角色与权限](./2_user_roles_and_permissions.md) - 理解用户体系
3. [功能模块文档](./4_1_exam_management.md) - 熟悉核心功能
4. [非功能性需求](./5_non_functional_requirements.md) - 明确质量标准

### 系统架构师
1. [核心架构需求](./3_core_architecture.md) - 了解架构设计
2. [技术架构](./6_technical_architecture.md) - 详细技术方案
3. [用户角色与权限](./2_user_roles_and_permissions.md) - 权限体系设计
4. [非功能性需求](./5_non_functional_requirements.md) - 性能和安全要求

### 后端工程师
1. [技术架构](./6_technical_architecture.md) - 技术实现方案
2. [核心架构需求](./3_core_architecture.md) - 多租户实现
3. [功能模块文档](./4_1_exam_management.md) - 业务逻辑实现
4. [阅卷中心](./4_2_grading_center.md) - AI集成方案

### 前端工程师
1. [用户角色与权限](./2_user_roles_and_permissions.md) - 用户界面设计
2. [功能模块文档](./4_1_exam_management.md) - 界面功能需求
3. [技术架构](./6_technical_architecture.md) - API接口设计
4. [非功能性需求](./5_non_functional_requirements.md) - 性能要求

### 测试工程师
1. [项目概述](./1_project_overview.md) - 了解测试范围
2. [功能模块文档](./4_1_exam_management.md) - 功能测试用例
3. [非功能性需求](./5_non_functional_requirements.md) - 性能测试标准
4. [技术架构](./6_technical_architecture.md) - 系统测试方案

---

## 📝 更新日志

### v1.2.1 (2025-07-25)
- ✅ 简化混合内容架构设计，移除过度复杂的表结构
- ✅ 移除content_transitions、content_learning_paths、content_path_items表
- ✅ 在核心表中增加个性化支持字段（difficulty_level、is_optional等）
- ✅ 优化API架构，简化学习路径管理接口
- ✅ 采用渐进式设计原则，保持架构简洁性

### v1.2.0 (2025-07-25)
- ✅ 新增混合内容组织系统架构
- ✅ 支持教辅书结构-章节-模块-知识讲解/例题/试题混排
- ✅ 增强题库系统，支持多答案/解析/难度/知识点
- ✅ 添加内容阅读顺序和学习路径管理
- ✅ 更新核心架构和技术架构文档
- ✅ 扩展API架构，支持混合内容管理

### v1.1.0 (2025-07-15)
- ✅ 优化文档结构，标准化标题层级
- ✅ 增加模块间交叉引用
- ✅ 完善技术实现细节
- ✅ 添加监控与运维章节

### v1.0.0 (2025-07-01)
- ✅ 初始版本发布
- ✅ 完成所有核心模块文档
- ✅ 建立基础架构文档

---

## 📚 相关资源

### 技术文档
- [API 文档](../api/README.md)
- [数据库设计](../database/schema.md)
- [部署指南](../deployment/README.md)

### 开发资源
- [开发环境搭建](../development/setup.md)
- [编码规范](../development/coding-standards.md)
- [测试指南](../testing/README.md)

### 项目管理
- [项目计划](../project/roadmap.md)
- [里程碑](../project/milestones.md)
- [风险管理](../project/risks.md)

---

## 🤝 贡献指南

### 文档更新流程
1. 修改相关模块文档
2. 更新本索引文档的相关链接
3. 提交PR并请求审核
4. 合并后更新版本号

### 文档规范
- 使用中文编写，技术术语可使用英文
- 遵循 Markdown 格式规范
- 包含必要的图表和流程图
- 添加适当的交叉引用

### 联系方式
- 产品团队：[<EMAIL>](mailto:<EMAIL>)
- 技术团队：[<EMAIL>](mailto:<EMAIL>)
- 文档维护：[<EMAIL>](mailto:<EMAIL>)

---

*本文档由 Deep-Mate 产品团队维护，如有问题请及时联系相关负责人。*