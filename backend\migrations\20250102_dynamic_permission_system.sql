-- Dynamic Permission System Migration
-- Migration: 20250102_dynamic_permission_system.migrations_temp

-- Permission categories for organization
CREATE TABLE IF NOT EXISTS public.permission_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    risk_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.0,
    requires_special_approval BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Permission templates and categories
CREATE TABLE IF NOT EXISTS public.permission_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    risk_level INTEGER NOT NULL DEFAULT 1, -- 1-5 scale
    requires_approval BOOLEAN NOT NULL DEFAULT false,
    approval_levels INTEGER NOT NULL DEFAULT 1,
    max_duration_hours INTEGER, -- NULL for permanent
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Approval workflows
CREATE TABLE IF NOT EXISTS public.approval_workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    workflow_steps JSONB NOT NULL, -- Array of approval steps
    escalation_rules JSONB, -- Rules for escalation
    timeout_rules JSONB, -- Timeout handling
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Special permission requests
CREATE TABLE IF NOT EXISTS public.special_permission_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    requester_id UUID NOT NULL REFERENCES public.users(id),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id),
    permission_template_id UUID NOT NULL REFERENCES public.permission_templates(id),
    target_resource VARCHAR(200), -- Resource being accessed
    justification TEXT NOT NULL,
    risk_score INTEGER NOT NULL,
    risk_factors JSONB, -- Detailed risk assessment
    requested_duration_hours INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, approved, rejected, expired, revoked
    workflow_id UUID REFERENCES public.approval_workflows(id),
    current_approval_level INTEGER NOT NULL DEFAULT 1,
    approval_history JSONB NOT NULL DEFAULT '[]',
    approved_by JSONB, -- Array of approver IDs and timestamps
    rejected_by UUID REFERENCES public.users(id),
    rejection_reason TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    granted_at TIMESTAMP WITH TIME ZONE,
    revoked_at TIMESTAMP WITH TIME ZONE,
    revoked_by UUID REFERENCES public.users(id),
    revocation_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Permission grants (active permissions)
CREATE TABLE IF NOT EXISTS public.permission_grants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id),
    permission_template_id UUID NOT NULL REFERENCES public.permission_templates(id),
    request_id UUID REFERENCES public.special_permission_requests(id),
    granted_by UUID NOT NULL REFERENCES public.users(id),
    target_resource VARCHAR(200),
    conditions JSONB, -- Additional conditions or restrictions
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Permission usage audit log
CREATE TABLE IF NOT EXISTS public.permission_usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    grant_id UUID NOT NULL REFERENCES public.permission_grants(id),
    user_id UUID NOT NULL REFERENCES public.users(id),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(200),
    resource_id UUID,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    session_id UUID,
    additional_context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for permission system
CREATE INDEX IF NOT EXISTS idx_permission_templates_category ON public.permission_templates(category);
CREATE INDEX IF NOT EXISTS idx_permission_templates_active ON public.permission_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_permission_requests_requester ON public.special_permission_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_permission_requests_tenant ON public.special_permission_requests(tenant_id);
CREATE INDEX IF NOT EXISTS idx_permission_requests_status ON public.special_permission_requests(status);
CREATE INDEX IF NOT EXISTS idx_permission_requests_expires ON public.special_permission_requests(expires_at);
CREATE INDEX IF NOT EXISTS idx_permission_grants_user ON public.permission_grants(user_id);
CREATE INDEX IF NOT EXISTS idx_permission_grants_tenant ON public.permission_grants(tenant_id);
CREATE INDEX IF NOT EXISTS idx_permission_grants_active ON public.permission_grants(is_active);
CREATE INDEX IF NOT EXISTS idx_permission_grants_expires ON public.permission_grants(expires_at);
CREATE INDEX IF NOT EXISTS idx_permission_usage_grant ON public.permission_usage_logs(grant_id);
CREATE INDEX IF NOT EXISTS idx_permission_usage_user ON public.permission_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_permission_usage_created ON public.permission_usage_logs(created_at);

-- Insert default permission categories
INSERT INTO public.permission_categories (name, description, risk_multiplier, requires_special_approval) VALUES
('exam_management', 'Exam creation, modification, and administration', 1.2, false),
('grading_system', 'Grading operations and score management', 1.5, false),
('user_management', 'User account and identity management', 2.0, true),
('system_administration', 'System configuration and maintenance', 3.0, true),
('emergency_access', 'Emergency system access and overrides', 5.0, true),
('data_export', 'Data export and reporting operations', 1.8, true),
('tenant_management', 'Multi-tenant configuration and management', 2.5, true)
ON CONFLICT (name) DO NOTHING;
