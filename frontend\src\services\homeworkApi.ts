import { ApiResponse } from '@/types';
import apiClient from './apiClient';

// Homework Management API
export const homeworkApi = {
  /**
   * 作者：张瀚
   * 说明：获取作业统计数据
   */
  getStatistics: async (tenantId: string): Promise<ApiResponse<HomeworkStatistics>> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/homework/getStatistics`);
  },

  // Create homework
  createHomework: async (tenantId: string, homeworkData: HomeworkCreateRequest): Promise<HomeworkResponse> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/homeworks`, homeworkData);
  },

  // Get homework list
  getHomeworks: async (tenantId: string, params?: HomeworkQueryParams): Promise<HomeworkListResponse> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/homeworks`, { params });
  },

  // Get homework by ID
  getHomework: async (tenantId: string, homeworkId: string): Promise<HomeworkResponse> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/homeworks/${homeworkId}`);
  },

  // Update homework
  updateHomework: async (tenantId: string, homeworkId: string, homeworkData: UpdateHomeworkRequest): Promise<HomeworkResponse> => {
    return apiClient.put(`/api/v1/tenant/${tenantId}/homeworks/${homeworkId}`, homeworkData);
  },

  // Delete homework
  deleteHomework: async (tenantId: string, homeworkId: string): Promise<boolean> => {
    return apiClient.delete(`/api/v1/tenant/${tenantId}/homeworks/${homeworkId}`);
  },

  // Publish homework
  publishHomework: async (tenantId: string, homeworkId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/homeworks/${homeworkId}/publish`);
  },

  // Start homework
  startHomework: async (tenantId: string, homeworkId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/homeworks/${homeworkId}/start`);
  },

  // Complete homework
  completeHomework: async (tenantId: string, homeworkId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/homeworks/${homeworkId}/complete`);
  },

  // Joint homework invitation
  createJointHomeworkInvitation: async (tenantId: string, invitationData: JointHomeworkInvitationRequest): Promise<JointHomework[]> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/homeworks/joint-invitation`, invitationData);
  },

  // Respond to joint homework invitation
  respondToJointHomeworkInvitation: async (tenantId: string, responseData: JointHomeworkResponseRequest): Promise<JointHomework> => {
    return apiClient.post(`/api/v1/tenant/${tenantId}/homeworks/joint-response`, responseData);
  },

  // Get joint homework invitations
  getJointHomeworkInvitations: async (tenantId: string): Promise<JointHomework[]> => {
    return apiClient.get(`/api/v1/tenant/${tenantId}/homeworks/joint-invitations`);
  },
};

export interface HomeworkStatistics {
  total_homeworks: number;
  completed_homeworks: number;
  in_progress_homeworks: number;
  total_students: number;
}

// Subjects API
export interface Subject {
  id: string;
  code: string;
  name: string;
  description?: string;
  order_level: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const subjectsApi = {
  getSubjects: async (): Promise<Subject[]> => {
    return apiClient.get('/api/v1/subjects');
  },
};

// Grade Levels API
export interface GradeLevel {
  id: string;
  code: string;
  name: string;
  level_order: number;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const gradeLevelsApi = {
  getGradeLevels: async (): Promise<GradeLevel[]> => {
    return apiClient.get('/api/v1/grade-levels');
  },
};

// Students API
export interface Student {
  id: string;
  student_id: string;
  name: string;
  administrative_class_id: string;
  grade_level_id: string;
  user_id?: string;
  profile_level?: string;
  profile_tags?: any;
  created_at: string;
  updated_at: string;
}

export const studentsApi = {
  getStudents: async (tenantId: string, classId?: string): Promise<Student[]> => {
    const params = classId ? { class_id: classId } : {};
    return apiClient.get(`/api/v1/tenant/${tenantId}/students`, { params });
  },
};

// Paper Templates API
export interface PaperTemplate {
  id: string;
  title: string;
  subject: string;
  grade_level: number;
  total_score: number;
  description?: string;
  structure: any;
  creator_id: string;
  version: number;
  status: string;
  created_at: string;
  updated_at: string;
  questions: any[];
  usage_count: number;
}

export const paperTemplatesApi = {
  getPaperTemplates: async (subject?: string, gradeLevel?: number): Promise<PaperTemplate[]> => {
    const params: any = {};
    if (subject) params.subject = subject;
    if (gradeLevel) params.grade_level = gradeLevel;
    return apiClient.get('/api/v1/paper-templates', { params });
  },
};

export default apiClient;


// Types
export interface HomeworkCreateRequest {
  name: string;
  homework_type: string;
  grade_level: string;
  homework_nature: string;
  description?: string;
  start_time: string;
  end_time: string;
  expected_collection_time?: string;
  scan_start_time?: string;
  grading_mode: string;
  quality_control: string;
  ai_confidence_threshold?: number;
  manual_review_ratio?: number;
  subjects: HomeworkSubjectRequest[];
  classes: HomeworkClassRequest[];
  selected_students?: string[];
}

export interface HomeworkSubjectRequest {
  subject_id: string;
  paper_template_id: string;
  total_score: number;
  pass_score: number;
}

export interface HomeworkClassRequest {
  class_id: string;
  class_type: string;
}

export interface HomeworkResponse {
  id: string;
  name: string;
  homework_type: string;
  grade_level: string;
  homework_nature: string;
  description?: string;
  start_time: string;
  end_time: string;
  expected_collection_time?: string;
  scan_start_time?: string;
  grading_mode: string;
  quality_control: string;
  ai_confidence_threshold?: number;
  manual_review_ratio?: number;
  status: string;
  created_by: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  subjects: HomeworkSubject[];
  classes: HomeworkClass[];
  student_count: number;
  joint_homework_info?: JointHomework[];
}

export interface HomeworkSubject {
  id: string;
  homework_id: string;
  subject_id: string;
  paper_template_id: string;
  total_score: number;
  pass_score: number;
  created_at: string;
}

export interface HomeworkClass {
  id: string;
  homework_id: string;
  class_id: string;
  class_type: string;
  created_at: string;
}

export interface JointHomework {
  id: string;
  main_homework_id: string;
  organizer_tenant_id: string;
  participant_tenant_id: string;
  invitation_status: string;
  invitation_sent_at: string;
  responded_at?: string;
  sync_status: string;
  created_at: string;
}

export interface HomeworkQueryParams {
  page?: number;
  page_size?: number;
  name?: string;
  homework_type?: string;
  grade_level?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
}

export interface HomeworkListResponse {
  homeworks: HomeworkResponse[];
  total: number;
  page: number;
  page_size: number;
}



export interface UpdateHomeworkRequest {
  name?: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  status?: string;
}

export interface JointHomeworkInvitationRequest {
  homework_id: string;
  participant_tenant_ids: string[];
}

export interface JointHomeworkResponseRequest {
  invitation_id: string;
  response: string; // 'accept' or 'reject'
}
