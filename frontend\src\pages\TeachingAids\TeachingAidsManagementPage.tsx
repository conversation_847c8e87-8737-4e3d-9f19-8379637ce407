import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';

import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

import {
  Upload,
  FileText,
  BookOpen,
  Users,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  AlertCircle,
  CheckCircle2,
  Clock,
  BarChart3,
  Tag,
  Package,
  Database,
  Globe,
  Library
} from 'lucide-react';

import {
  teachingAidApi,
  chapterApi,
  exerciseApi,
  importApi,
  authorizationApi,
  versionApi,
  TeachingAid,
  TeachingAidChapter,
  TeachingAidExercise,
  TeachingAidStats,
  ImportProgress,
  TeachingAidAuthorization,
  TeachingAidVersion,
  ImportTeachingAidRequest,
  CreateTeachingAidRequest,
} from '@/services/teachingAidsApi';

const TeachingAidsManagementPage: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Data states
  const [teachingAids, setTeachingAids] = useState<TeachingAid[]>([]);
  const [filteredTeachingAids, setFilteredTeachingAids] = useState<TeachingAid[]>([]);
  const [stats, setStats] = useState<TeachingAidStats | null>(null);
  const [chapters, setChapters] = useState<TeachingAidChapter[]>([]);
  const [exercises, setExercises] = useState<TeachingAidExercise[]>([]);
  const [importHistory, setImportHistory] = useState<ImportProgress[]>([]);
  const [authorizations, setAuthorizations] = useState<TeachingAidAuthorization[]>([]);
  const [setVersions] = useState<TeachingAidVersion[]>([]);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState<string>('');
  const [selectedGradeLevel, setSelectedGradeLevel] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedTeachingAid, setSelectedTeachingAid] = useState<string>('');

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [selectedAid, setSelectedAid] = useState<TeachingAid | null>(null);

  // Form states
  const [createForm, setCreateForm] = useState<CreateTeachingAidRequest>({
    title: '',
    description: '',
    author: '',
    publisher: '',
    subject: '',
    grade_level: '',
    content_type: '',
    authorization_scope: []
  });

  const [importForm, setImportForm] = useState<Partial<ImportTeachingAidRequest>>({
    title: '',
    description: '',
    author: '',
    publisher: '',
    subject: '',
    grade_level: '',
    content_type: '',
    import_exercises: true,
    authorization_scope: []
  });

  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [teachingAidsData, statsData, importHistoryData] = await Promise.all([
        teachingAidApi.getTeachingAids(),
        teachingAidApi.getTeachingAidStats(),
        importApi.getImportHistory()
      ]);

      setTeachingAids(teachingAidsData);
      setStats(statsData);
      setImportHistory(importHistoryData);
      setError(null);
    } catch (err) {
      setError('Failed to load teaching aids data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    const filteredTeachingAids = teachingAids.filter(aid => {
      const matchesSearch = !searchQuery ||
        aid.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        aid.author?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        aid.description?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesSubject = !selectedSubject || aid.subject === selectedSubject;
      const matchesGradeLevel = !selectedGradeLevel || aid.grade_level === selectedGradeLevel;
      const matchesStatus = !selectedStatus || aid.status === selectedStatus;
      return matchesSearch && matchesSubject && matchesGradeLevel && matchesStatus;
    });

    setFilteredTeachingAids(filteredTeachingAids);
  }, [teachingAids, searchQuery, selectedSubject, selectedGradeLevel, selectedStatus])

  const loadChapters = async (teachingAidId: string) => {
    try {
      const chaptersData = await chapterApi.getChapters(teachingAidId);
      setChapters(chaptersData);
    } catch (err) {
      setError('Failed to load chapters');
      console.error('Error loading chapters:', err);
    }
  };

  const loadExercises = async (teachingAidId?: string, chapterId?: string) => {
    try {
      const exercisesData = await exerciseApi.getExercises({
        teaching_aid_id: teachingAidId,
        chapter_id: chapterId
      });
      setExercises(exercisesData);
    } catch (err) {
      setError('Failed to load exercises');
      console.error('Error loading exercises:', err);
    }
  };

  const loadAuthorizations = async (teachingAidId: string) => {
    try {
      const authData = await authorizationApi.getTeachingAidAuthorizations(teachingAidId);
      setAuthorizations(authData);
    } catch (err) {
      setError('Failed to load authorizations');
      console.error('Error loading authorizations:', err);
    }
  };

  const loadVersions = async (teachingAidId: string) => {
    try {
      const versionsData = await versionApi.getTeachingAidVersions(teachingAidId);
      // @ts-ignore
      setVersions(versionsData);
    } catch (err) {
      setError('Failed to load versions');
      console.error('Error loading versions:', err);
    }
  };

  const handleCreateTeachingAid = async () => {
    try {
      await teachingAidApi.createTeachingAid(createForm);
      setIsCreateDialogOpen(false);
      setCreateForm({
        title: '',
        description: '',
        author: '',
        publisher: '',
        subject: '',
        grade_level: '',
        content_type: '',
        authorization_scope: []
      });
      loadInitialData();
    } catch (err) {
      setError('Failed to create teaching aid');
      console.error('Error creating teaching aid:', err);
    }
  };

  const handleImportTeachingAid = async () => {
    if (!uploadFile) return;

    try {
      const importRequest: ImportTeachingAidRequest = {
        file: uploadFile,
        title: importForm.title || '',
        description: importForm.description || '',
        author: importForm.author || '',
        publisher: importForm.publisher || '',
        subject: importForm.subject || '',
        grade_level: importForm.grade_level || '',
        content_type: importForm.content_type || '',
        import_exercises: importForm.import_exercises || true,
        authorization_scope: importForm.authorization_scope || []
      };

      const progress = await importApi.importTeachingAid(importRequest);
      setImportProgress(progress);
      setIsImportDialogOpen(false);
      setUploadFile(null);

      // Poll for progress updates
      const pollProgress = setInterval(async () => {
        try {
          const updatedProgress = await importApi.getImportProgress(progress.id);
          setImportProgress(updatedProgress);

          if (updatedProgress.status === 'completed' || updatedProgress.status === 'failed') {
            clearInterval(pollProgress);
            loadInitialData();
          }
        } catch (err) {
          clearInterval(pollProgress);
          console.error('Error polling import progress:', err);
        }
      }, 2000);

    } catch (err) {
      setError('Failed to import teaching aid');
      console.error('Error importing teaching aid:', err);
    }
  };

  const handleDeleteTeachingAid = async (aidId: string) => {
    if (!confirm('确定要删除这个教辅资源吗？此操作无法撤销。')) return;

    try {
      await teachingAidApi.deleteTeachingAid(aidId);
      loadInitialData();
    } catch (err) {
      setError('Failed to delete teaching aid');
      console.error('Error deleting teaching aid:', err);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
    }
  };

  const openDetailDialog = (aid: TeachingAid) => {
    setSelectedAid(aid);
    setSelectedTeachingAid(aid.id);
    setIsDetailDialogOpen(true);
    loadChapters(aid.id);
    loadExercises(aid.id);
    loadAuthorizations(aid.id);
    loadVersions(aid.id);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: '草稿', icon: Clock },
      published: { variant: 'default' as const, label: '已发布', icon: CheckCircle2 },
      archived: { variant: 'outline' as const, label: '已归档', icon: Package },
      processing: { variant: 'outline' as const, label: '处理中', icon: RefreshCw }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getContentTypeBadge = (contentType: string) => {
    const typeConfig = {
      textbook: { variant: 'default' as const, label: '教科书', icon: BookOpen },
      workbook: { variant: 'secondary' as const, label: '练习册', icon: FileText },
      reference: { variant: 'outline' as const, label: '参考资料', icon: Library },
      multimedia: { variant: 'outline' as const, label: '多媒体', icon: Globe }
    };

    const config = typeConfig[contentType as keyof typeof typeConfig] || typeConfig.textbook;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <AlertDialog>
          <AlertCircle className="h-4 w-4" />
          <AlertDialogDescription>{error}</AlertDialogDescription>
        </AlertDialog>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">教辅管理</h1>
          <p className="text-muted-foreground">跨租户共享的结构化教辅资源管理</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadInitialData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                导入教辅
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>导入教辅资源</DialogTitle>
                <DialogDescription>
                  支持JSON、XML、CSV、Excel格式的结构化教辅数据
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="import-title">标题</Label>
                    <Input
                      id="import-title"
                      value={importForm.title}
                      onChange={(e) => setImportForm({ ...importForm, title: e.target.value })}
                      placeholder="教辅标题"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="import-author">作者</Label>
                    <Input
                      id="import-author"
                      value={importForm.author}
                      onChange={(e) => setImportForm({ ...importForm, author: e.target.value })}
                      placeholder="教辅作者"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="import-publisher">出版社</Label>
                    <Input
                      id="import-publisher"
                      value={importForm.publisher}
                      onChange={(e) => setImportForm({ ...importForm, publisher: e.target.value })}
                      placeholder="出版社"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="import-subject">学科</Label>
                    <Select value={importForm.subject} onValueChange={(value) => setImportForm({ ...importForm, subject: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择学科" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mathematics">数学</SelectItem>
                        <SelectItem value="chinese">语文</SelectItem>
                        <SelectItem value="english">英语</SelectItem>
                        <SelectItem value="physics">物理</SelectItem>
                        <SelectItem value="chemistry">化学</SelectItem>
                        <SelectItem value="biology">生物</SelectItem>
                        <SelectItem value="history">历史</SelectItem>
                        <SelectItem value="geography">地理</SelectItem>
                        <SelectItem value="politics">政治</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="import-grade">年级</Label>
                    <Select value={importForm.grade_level} onValueChange={(value) => setImportForm({ ...importForm, grade_level: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grade_7">七年级</SelectItem>
                        <SelectItem value="grade_8">八年级</SelectItem>
                        <SelectItem value="grade_9">九年级</SelectItem>
                        <SelectItem value="grade_10">高一</SelectItem>
                        <SelectItem value="grade_11">高二</SelectItem>
                        <SelectItem value="grade_12">高三</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="import-content-type">内容类型</Label>
                    <Select value={importForm.content_type} onValueChange={(value) => setImportForm({ ...importForm, content_type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择内容类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="textbook">教科书</SelectItem>
                        <SelectItem value="workbook">练习册</SelectItem>
                        <SelectItem value="reference">参考资料</SelectItem>
                        <SelectItem value="multimedia">多媒体资料</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="import-description">描述</Label>
                  <Textarea
                    id="import-description"
                    value={importForm.description}
                    onChange={(e) => setImportForm({ ...importForm, description: e.target.value })}
                    placeholder="教辅描述信息"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="import-file">上传文件</Label>
                  <Input
                    id="import-file"
                    type="file"
                    accept=".json,.xml,.csv,.xlsx,.xls"
                    onChange={handleFileUpload}
                  />
                  {uploadFile && (
                    <div className="text-sm text-muted-foreground">
                      已选择: {uploadFile.name} ({(uploadFile.size / 1024 / 1024).toFixed(2)}MB)
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleImportTeachingAid} disabled={!uploadFile}>
                  开始导入
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                创建教辅
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>创建新教辅</DialogTitle>
                <DialogDescription>
                  创建一个新的教辅资源
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="create-title">标题</Label>
                    <Input
                      id="create-title"
                      value={createForm.title}
                      onChange={(e) => setCreateForm({ ...createForm, title: e.target.value })}
                      placeholder="教辅标题"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="create-author">作者</Label>
                    <Input
                      id="create-author"
                      value={createForm.author}
                      onChange={(e) => setCreateForm({ ...createForm, author: e.target.value })}
                      placeholder="教辅作者"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="create-publisher">出版社</Label>
                    <Input
                      id="create-publisher"
                      value={createForm.publisher}
                      onChange={(e) => setCreateForm({ ...createForm, publisher: e.target.value })}
                      placeholder="出版社"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="create-subject">学科</Label>
                    <Select value={createForm.subject} onValueChange={(value) => setCreateForm({ ...createForm, subject: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择学科" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mathematics">数学</SelectItem>
                        <SelectItem value="chinese">语文</SelectItem>
                        <SelectItem value="english">英语</SelectItem>
                        <SelectItem value="physics">物理</SelectItem>
                        <SelectItem value="chemistry">化学</SelectItem>
                        <SelectItem value="biology">生物</SelectItem>
                        <SelectItem value="history">历史</SelectItem>
                        <SelectItem value="geography">地理</SelectItem>
                        <SelectItem value="politics">政治</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="create-grade">年级</Label>
                    <Select value={createForm.grade_level} onValueChange={(value) => setCreateForm({ ...createForm, grade_level: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grade_7">七年级</SelectItem>
                        <SelectItem value="grade_8">八年级</SelectItem>
                        <SelectItem value="grade_9">九年级</SelectItem>
                        <SelectItem value="grade_10">高一</SelectItem>
                        <SelectItem value="grade_11">高二</SelectItem>
                        <SelectItem value="grade_12">高三</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="create-content-type">内容类型</Label>
                    <Select value={createForm.content_type} onValueChange={(value) => setCreateForm({ ...createForm, content_type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择内容类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="textbook">教科书</SelectItem>
                        <SelectItem value="workbook">练习册</SelectItem>
                        <SelectItem value="reference">参考资料</SelectItem>
                        <SelectItem value="multimedia">多媒体资料</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="create-description">描述</Label>
                  <Textarea
                    id="create-description"
                    value={createForm.description}
                    onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                    placeholder="教辅描述信息"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateTeachingAid}>
                  创建
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {importProgress && (
        <Card>
          <CardHeader>
            <CardTitle>导入进度</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{importProgress.filename}</span>
                <span>{importProgress.status}</span>
              </div>
              <Skeleton className="h-4 w-full" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>已处理: {importProgress.processed_records}/{importProgress.total_records}</span>
                <span>成功: {importProgress.success_count} | 失败: {importProgress.error_count}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="teaching-aids">教辅列表</TabsTrigger>
          <TabsTrigger value="imports">导入记录</TabsTrigger>
          <TabsTrigger value="authorization">授权管理</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">教辅总数</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.total_textbooks || 0}</div>
                <div className="text-xs text-muted-foreground">
                  章节: {stats?.total_chapters || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">练习题数</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.total_exercises || 0}</div>
                <div className="text-xs text-muted-foreground">
                  导入题库可用
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">授权租户</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.total_authorized_tenants || 0}</div>
                <div className="text-xs text-muted-foreground">
                  跨租户共享
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">热门学科</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.popular_subjects?.length || 0}</div>
                <div className="text-xs text-muted-foreground">
                  学科覆盖
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Popular Subjects */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>热门学科</CardTitle>
                <CardDescription>各学科教辅资源分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats?.popular_subjects?.map((subject) => (
                    <div key={subject.subject} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4" />
                        <span className="font-medium">{subject.subject}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-20" />
                        <span className="text-sm font-medium">{subject.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>最近导入</CardTitle>
                <CardDescription>最近的教辅导入记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats?.recent_imports?.slice(0, 5).map((import_record) => (
                    <div key={import_record.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <div className="font-medium">{import_record.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(import_record.imported_at).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{import_record.count} 题</Badge>
                        {getStatusBadge(import_record.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="teaching-aids" className="space-y-6">
          {/* Search and Filter */}
          <Card>
            <CardHeader>
              <CardTitle>搜索与筛选</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">搜索</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="搜索教辅..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>学科</Label>
                  <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                    <SelectTrigger>
                      <SelectValue placeholder="全部学科" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="allSubject">全部学科</SelectItem>
                      <SelectItem value="mathematics">数学</SelectItem>
                      <SelectItem value="chinese">语文</SelectItem>
                      <SelectItem value="english">英语</SelectItem>
                      <SelectItem value="physics">物理</SelectItem>
                      <SelectItem value="chemistry">化学</SelectItem>
                      <SelectItem value="biology">生物</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>年级</Label>
                  <Select value={selectedGradeLevel} onValueChange={setSelectedGradeLevel}>
                    <SelectTrigger>
                      <SelectValue placeholder="全部年级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="allGrade">全部年级</SelectItem>
                      <SelectItem value="grade_7">七年级</SelectItem>
                      <SelectItem value="grade_8">八年级</SelectItem>
                      <SelectItem value="grade_9">九年级</SelectItem>
                      <SelectItem value="grade_10">高一</SelectItem>
                      <SelectItem value="grade_11">高二</SelectItem>
                      <SelectItem value="grade_12">高三</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>状态</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="全部状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="allStatus">全部状态</SelectItem>
                      <SelectItem value="draft">草稿</SelectItem>
                      <SelectItem value="published">已发布</SelectItem>
                      <SelectItem value="archived">已归档</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>&nbsp;</Label>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedSubject('');
                      setSelectedGradeLevel('');
                      setSelectedStatus('');
                    }}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    重置
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Teaching Aids List */}
          <Card>
            <CardHeader>
              <CardTitle>教辅列表</CardTitle>
              <CardDescription>
                显示 {filteredTeachingAids.length} 个教辅资源
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>标题</TableHead>
                    <TableHead>作者</TableHead>
                    <TableHead>学科</TableHead>
                    <TableHead>年级</TableHead>
                    <TableHead>内容类型</TableHead>
                    <TableHead>版本</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTeachingAids.map((aid) => (
                    <TableRow key={aid.id}>
                      <TableCell className="font-medium">{aid.title}</TableCell>
                      <TableCell>{aid.author}</TableCell>
                      <TableCell>{aid.subject}</TableCell>
                      <TableCell>{aid.grade_level}</TableCell>
                      <TableCell>{getContentTypeBadge(aid.content_type)}</TableCell>
                      <TableCell>{aid.version}</TableCell>
                      <TableCell>{getStatusBadge(aid.status)}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm" onClick={() => openDetailDialog(aid)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => handleDeleteTeachingAid(aid.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="imports" className="space-y-6">
          {/* Import History */}
          <Card>
            <CardHeader>
              <CardTitle>导入历史</CardTitle>
              <CardDescription>教辅数据导入记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>文件名</TableHead>
                    <TableHead>总记录数</TableHead>
                    <TableHead>成功数</TableHead>
                    <TableHead>失败数</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>开始时间</TableHead>
                    <TableHead>完成时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {importHistory.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.filename}</TableCell>
                      <TableCell>{record.total_records}</TableCell>
                      <TableCell>{record.success_count}</TableCell>
                      <TableCell>{record.error_count}</TableCell>
                      <TableCell>{getStatusBadge(record.status)}</TableCell>
                      <TableCell>{new Date(record.started_at).toLocaleString()}</TableCell>
                      <TableCell>
                        {record.completed_at ? new Date(record.completed_at).toLocaleString() : '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {record.status === 'failed' && (
                            <Button variant="ghost" size="sm">
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="authorization" className="space-y-6">
          {/* Authorization Management */}
          <Card>
            <CardHeader>
              <CardTitle>授权管理</CardTitle>
              <CardDescription>管理教辅资源的租户访问权限</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  请选择一个教辅资源查看其授权信息
                </div>
                {selectedTeachingAid && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">当前授权</h3>
                      <Button size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        添加授权
                      </Button>
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>租户</TableHead>
                          <TableHead>访问级别</TableHead>
                          <TableHead>授权时间</TableHead>
                          <TableHead>过期时间</TableHead>
                          <TableHead>状态</TableHead>
                          <TableHead>操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {authorizations.map((auth) => (
                          <TableRow key={auth.id}>
                            <TableCell className="font-medium">{auth.tenant_name}</TableCell>
                            <TableCell>
                              <Badge variant={auth.access_level === 'full' ? 'default' : 'secondary'}>
                                {auth.access_level}
                              </Badge>
                            </TableCell>
                            <TableCell>{new Date(auth.authorized_at).toLocaleString()}</TableCell>
                            <TableCell>{auth.expires_at ? new Date(auth.expires_at).toLocaleString() : '永久'}</TableCell>
                            <TableCell>
                              <Badge variant={auth.status === 'active' ? 'default' : 'secondary'}>
                                {auth.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Button variant="ghost" size="sm">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>数据分析</CardTitle>
              <CardDescription>教辅资源使用情况分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                数据分析功能开发中...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Teaching Aid Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>教辅详情</DialogTitle>
            <DialogDescription>
              {selectedAid?.title} - {selectedAid?.author}
            </DialogDescription>
          </DialogHeader>
          {selectedAid && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>标题</Label>
                  <div className="font-medium">{selectedAid.title}</div>
                </div>
                <div>
                  <Label>作者</Label>
                  <div className="font-medium">{selectedAid.author}</div>
                </div>
                <div>
                  <Label>出版社</Label>
                  <div className="font-medium">{selectedAid.publisher}</div>
                </div>
                <div>
                  <Label>学科</Label>
                  <div className="font-medium">{selectedAid.subject}</div>
                </div>
                <div>
                  <Label>年级</Label>
                  <div className="font-medium">{selectedAid.grade_level}</div>
                </div>
                <div>
                  <Label>版本</Label>
                  <div className="font-medium">{selectedAid.version}</div>
                </div>
              </div>

              <Separator />

              <div>
                <Label>描述</Label>
                <div className="mt-1 text-sm text-muted-foreground">{selectedAid.description}</div>
              </div>

              <Separator />

              <div>
                <Label>章节 ({chapters.length})</Label>
                <ScrollArea className="h-48 mt-2 border rounded">
                  <div className="p-2 space-y-1">
                    {chapters.map((chapter) => (
                      <div key={chapter.id} className="flex items-center justify-between p-2 border rounded text-sm">
                        <div>
                          <div className="font-medium">{chapter.title}</div>
                          <div className="text-muted-foreground">{chapter.description}</div>
                        </div>
                        <Badge variant="outline">{chapter.difficulty_level}</Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              <Separator />

              <div>
                <Label>练习题 ({exercises.length})</Label>
                <ScrollArea className="h-48 mt-2 border rounded">
                  <div className="p-2 space-y-1">
                    {exercises.slice(0, 10).map((exercise) => (
                      <div key={exercise.id} className="flex items-center justify-between p-2 border rounded text-sm">
                        <div>
                          <div className="font-medium">{exercise.question_type}</div>
                          <div className="text-muted-foreground line-clamp-2">{exercise.question_content}</div>
                        </div>
                        <Badge variant="outline">{exercise.difficulty_level}</Badge>
                      </div>
                    ))}
                    {exercises.length > 10 && (
                      <div className="text-center text-sm text-muted-foreground">
                        ... 还有 {exercises.length - 10} 个练习题
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TeachingAidsManagementPage;